@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HC:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=arm64-v8a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\2v2d5r5q\\obj\\arm64-v8a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\2v2d5r5q\\obj\\arm64-v8a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\prefab\\arm64-v8a\\prefab" ^
  "-BC:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\arm64-v8a" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared" ^
  "-DREACT_NATIVE_DIR=C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native" ^
  "-DREACT_NATIVE_TARGET_VERSION=76" ^
  "-DUSE_HERMES=false" ^
  "-DIS_NEW_ARCHITECTURE_ENABLED=true" ^
  "-DUNIT_TEST=false"
