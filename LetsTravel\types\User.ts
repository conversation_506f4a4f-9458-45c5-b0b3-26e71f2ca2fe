/**
 * Trekmate 4.0 - 用户类型定义
 */

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string | null;
  preferences: UserPreferences;
  createdAt: string;
  lastLoginAt?: string | null;
}

export interface UserProfile {
  name: string;
  email: string;
  avatar?: string | null;
  preferences: UserPreferences;
}

export interface UserPreferences {
  language: string;
  currency: string;
  notifications: boolean;
  theme?: 'light' | 'dark' | 'auto';
  units?: 'metric' | 'imperial';
  privacy?: {
    shareLocation: boolean;
    shareJourneys: boolean;
    allowMessages: boolean;
  };
} 