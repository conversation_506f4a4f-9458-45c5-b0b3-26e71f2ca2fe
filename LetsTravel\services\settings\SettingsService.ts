/**
 * Trekmate 4.0 - 设置管理服务
 * 提供应用设置的存储、同步和管理功能
 */

import { defaultStorage } from '../storage/StorageService';
import type { ServiceResponse } from '../../types/CoreServices';

// ============================================================================
// 设置数据类型
// ============================================================================

export interface AppSettings {
  // 通用设置
  language: 'zh-CN' | 'en-US' | 'ms-MY' | 'th-TH';
  theme: 'light' | 'dark' | 'auto';
  currency: string;
  units: 'metric' | 'imperial';
  
  // 隐私设置
  privacy: {
    shareLocation: boolean;
    shareActivity: boolean;
    allowAnalytics: boolean;
    allowCrashReports: boolean;
  };
  
  // 通知设置
  notifications: {
    enabled: boolean;
    journeyReminders: boolean;
    weatherAlerts: boolean;
    communityUpdates: boolean;
    promotions: boolean;
    quietHours: {
      enabled: boolean;
      startTime: string; // HH:mm
      endTime: string; // HH:mm
    };
  };
  
  // 地图设置
  map: {
    defaultMapType: 'standard' | 'satellite' | 'hybrid';
    showTraffic: boolean;
    showPublicTransit: boolean;
    offlineDownloads: boolean;
    autoDownloadMaps: boolean;
  };
  
  // 相机设置
  camera: {
    saveToGallery: boolean;
    includeLocation: boolean;
    imageQuality: 'low' | 'medium' | 'high';
    autoBackup: boolean;
  };
  
  // 同步设置
  sync: {
    enabled: boolean;
    autoSync: boolean;
    syncOnWifiOnly: boolean;
    lastSyncTime?: string;
  };
  
  // 性能设置
  performance: {
    enableAnimations: boolean;
    reducedMotion: boolean;
    lowDataMode: boolean;
    cacheSize: 'small' | 'medium' | 'large';
  };
  
  // 辅助功能
  accessibility: {
    fontSize: 'small' | 'medium' | 'large' | 'extra-large';
    highContrast: boolean;
    screenReader: boolean;
    hapticFeedback: boolean;
  };
}

export interface SettingsCategory {
  id: string;
  title: string;
  description: string;
  icon: string;
  settings: SettingItem[];
}

export interface SettingItem {
  id: string;
  type: 'toggle' | 'select' | 'slider' | 'text' | 'time' | 'action';
  title: string;
  description?: string;
  value: any;
  options?: { label: string; value: any }[];
  min?: number;
  max?: number;
  step?: number;
  action?: () => void;
  disabled?: boolean;
  warning?: string;
}

export interface SettingsBackup {
  version: string;
  timestamp: string;
  settings: AppSettings;
  deviceInfo: {
    platform: string;
    version: string;
    model?: string;
  };
}

// ============================================================================
// 设置服务接口
// ============================================================================

export interface SettingsService {
  // 基础设置管理
  getSettings(): Promise<ServiceResponse<AppSettings>>;
  updateSettings(settings: Partial<AppSettings>): Promise<ServiceResponse<void>>;
  resetSettings(): Promise<ServiceResponse<void>>;
  
  // 分类设置管理
  getSettingsCategories(): Promise<ServiceResponse<SettingsCategory[]>>;
  updateCategorySetting(categoryId: string, settingId: string, value: any): Promise<ServiceResponse<void>>;
  
  // 设置同步
  syncSettings(): Promise<ServiceResponse<void>>;
  uploadSettings(): Promise<ServiceResponse<void>>;
  downloadSettings(): Promise<ServiceResponse<AppSettings>>;
  
  // 备份和恢复
  createBackup(): Promise<ServiceResponse<SettingsBackup>>;
  restoreFromBackup(backup: SettingsBackup): Promise<ServiceResponse<void>>;
  exportSettings(): Promise<ServiceResponse<string>>;
  importSettings(data: string): Promise<ServiceResponse<void>>;
  
  // 设置验证
  validateSettings(settings: Partial<AppSettings>): Promise<ServiceResponse<string[]>>;
  getDefaultSettings(): Promise<ServiceResponse<AppSettings>>;
}

// ============================================================================
// 设置服务实现
// ============================================================================

class SettingsServiceImpl implements SettingsService {
  private readonly SETTINGS_KEY = 'app_settings';
  private readonly BACKUP_PREFIX = 'settings_backup_';
  
  private defaultSettings: AppSettings = {
    language: 'zh-CN',
    theme: 'auto',
    currency: 'MYR',
    units: 'metric',
    
    privacy: {
      shareLocation: true,
      shareActivity: true,
      allowAnalytics: true,
      allowCrashReports: true,
    },
    
    notifications: {
      enabled: true,
      journeyReminders: true,
      weatherAlerts: true,
      communityUpdates: true,
      promotions: false,
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
      },
    },
    
    map: {
      defaultMapType: 'standard',
      showTraffic: true,
      showPublicTransit: true,
      offlineDownloads: false,
      autoDownloadMaps: false,
    },
    
    camera: {
      saveToGallery: true,
      includeLocation: true,
      imageQuality: 'high',
      autoBackup: false,
    },
    
    sync: {
      enabled: true,
      autoSync: true,
      syncOnWifiOnly: true,
    },
    
    performance: {
      enableAnimations: true,
      reducedMotion: false,
      lowDataMode: false,
      cacheSize: 'medium',
    },
    
    accessibility: {
      fontSize: 'medium',
      highContrast: false,
      screenReader: false,
      hapticFeedback: true,
    },
  };

  // ========================================================================
  // 基础设置管理
  // ========================================================================

  async getSettings(): Promise<ServiceResponse<AppSettings>> {
    try {
      const stored = await defaultStorage.getObject<AppSettings>(this.SETTINGS_KEY);
      
      if (stored) {
        // 合并默认设置以确保新增的设置项存在
        const settings = this.mergeWithDefaults(stored);
        return {
          success: true,
          data: settings,
        };
      }

      // 首次使用，返回默认设置
      await this.updateSettings(this.defaultSettings);
      return {
        success: true,
        data: this.defaultSettings,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取设置失败',
      };
    }
  }

  async updateSettings(settings: Partial<AppSettings>): Promise<ServiceResponse<void>> {
    try {
      const currentSettings = await this.getSettings();
      if (!currentSettings.success) {
        return currentSettings;
      }

      const updatedSettings = this.deepMerge(currentSettings.data, settings);
      
      // 验证设置
      const validation = await this.validateSettings(updatedSettings);
      if (!validation.success) {
        return {
          success: false,
          error: `设置验证失败: ${validation.data?.join(', ')}`,
        };
      }

      await defaultStorage.setObject(this.SETTINGS_KEY, updatedSettings);

      // 如果启用了自动同步，则同步设置
      if (updatedSettings.sync.enabled && updatedSettings.sync.autoSync) {
        await this.syncSettings();
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新设置失败',
      };
    }
  }

  async resetSettings(): Promise<ServiceResponse<void>> {
    try {
      await defaultStorage.setObject(this.SETTINGS_KEY, this.defaultSettings);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '重置设置失败',
      };
    }
  }

  // ========================================================================
  // 分类设置管理
  // ========================================================================

  async getSettingsCategories(): Promise<ServiceResponse<SettingsCategory[]>> {
    try {
      const settings = await this.getSettings();
      if (!settings.success) {
        return settings as any;
      }

      const categories: SettingsCategory[] = [
        {
          id: 'general',
          title: '通用',
          description: '语言、主题和基本设置',
          icon: 'settings',
          settings: [
            {
              id: 'language',
              type: 'select',
              title: '语言',
              description: '选择应用界面语言',
              value: settings.data.language,
              options: [
                { label: '简体中文', value: 'zh-CN' },
                { label: 'English', value: 'en-US' },
                { label: 'Bahasa Malaysia', value: 'ms-MY' },
                { label: 'ไทย', value: 'th-TH' },
              ],
            },
            {
              id: 'theme',
              type: 'select',
              title: '主题',
              description: '选择应用外观主题',
              value: settings.data.theme,
              options: [
                { label: '浅色', value: 'light' },
                { label: '深色', value: 'dark' },
                { label: '跟随系统', value: 'auto' },
              ],
            },
            {
              id: 'currency',
              type: 'select',
              title: '默认货币',
              value: settings.data.currency,
              options: [
                { label: '马来西亚林吉特 (MYR)', value: 'MYR' },
                { label: '美元 (USD)', value: 'USD' },
                { label: '人民币 (CNY)', value: 'CNY' },
                { label: '欧元 (EUR)', value: 'EUR' },
              ],
            },
          ],
        },
        {
          id: 'privacy',
          title: '隐私',
          description: '数据共享和隐私控制',
          icon: 'shield-checkmark',
          settings: [
            {
              id: 'shareLocation',
              type: 'toggle',
              title: '分享位置',
              description: '允许应用使用您的位置信息',
              value: settings.data.privacy.shareLocation,
            },
            {
              id: 'shareActivity',
              type: 'toggle',
              title: '分享活动',
              description: '在社区中分享您的旅行活动',
              value: settings.data.privacy.shareActivity,
            },
            {
              id: 'allowAnalytics',
              type: 'toggle',
              title: '使用分析',
              description: '帮助改进应用体验',
              value: settings.data.privacy.allowAnalytics,
            },
          ],
        },
        {
          id: 'notifications',
          title: '通知',
          description: '推送通知和提醒设置',
          icon: 'notifications',
          settings: [
            {
              id: 'enabled',
              type: 'toggle',
              title: '启用通知',
              value: settings.data.notifications.enabled,
            },
            {
              id: 'journeyReminders',
              type: 'toggle',
              title: '行程提醒',
              value: settings.data.notifications.journeyReminders,
              disabled: !settings.data.notifications.enabled,
            },
            {
              id: 'weatherAlerts',
              type: 'toggle',
              title: '天气警报',
              value: settings.data.notifications.weatherAlerts,
              disabled: !settings.data.notifications.enabled,
            },
          ],
        },
        {
          id: 'performance',
          title: '性能',
          description: '应用性能和数据使用',
          icon: 'speedometer',
          settings: [
            {
              id: 'enableAnimations',
              type: 'toggle',
              title: '启用动画',
              value: settings.data.performance.enableAnimations,
            },
            {
              id: 'lowDataMode',
              type: 'toggle',
              title: '低数据模式',
              description: '减少数据使用量',
              value: settings.data.performance.lowDataMode,
            },
            {
              id: 'cacheSize',
              type: 'select',
              title: '缓存大小',
              value: settings.data.performance.cacheSize,
              options: [
                { label: '小 (50MB)', value: 'small' },
                { label: '中 (100MB)', value: 'medium' },
                { label: '大 (200MB)', value: 'large' },
              ],
            },
          ],
        },
      ];

      return {
        success: true,
        data: categories,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取设置分类失败',
      };
    }
  }

  async updateCategorySetting(categoryId: string, settingId: string, value: any): Promise<ServiceResponse<void>> {
    try {
      const settings = await this.getSettings();
      if (!settings.success) {
        return settings;
      }

      const updatedSettings = { ...settings.data };

      // 根据分类和设置ID更新对应的值
      switch (categoryId) {
        case 'general':
          if (settingId in updatedSettings) {
            (updatedSettings as any)[settingId] = value;
          }
          break;
        case 'privacy':
          if (settingId in updatedSettings.privacy) {
            (updatedSettings.privacy as any)[settingId] = value;
          }
          break;
        case 'notifications':
          if (settingId in updatedSettings.notifications) {
            (updatedSettings.notifications as any)[settingId] = value;
          }
          break;
        case 'performance':
          if (settingId in updatedSettings.performance) {
            (updatedSettings.performance as any)[settingId] = value;
          }
          break;
        default:
          return {
            success: false,
            error: '未知的设置分类',
          };
      }

      return await this.updateSettings(updatedSettings);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新分类设置失败',
      };
    }
  }

  // ========================================================================
  // 设置同步
  // ========================================================================

  async syncSettings(): Promise<ServiceResponse<void>> {
    try {
      // 模拟设置同步
      await new Promise(resolve => setTimeout(resolve, 1000));

      const settings = await this.getSettings();
      if (!settings.success) {
        return settings;
      }

      // 更新最后同步时间
      const updatedSettings = {
        ...settings.data,
        sync: {
          ...settings.data.sync,
          lastSyncTime: new Date().toISOString(),
        },
      };

      await defaultStorage.setObject(this.SETTINGS_KEY, updatedSettings);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '同步设置失败',
      };
    }
  }

  async uploadSettings(): Promise<ServiceResponse<void>> {
    try {
      // 模拟上传设置到云端
      await new Promise(resolve => setTimeout(resolve, 2000));
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传设置失败',
      };
    }
  }

  async downloadSettings(): Promise<ServiceResponse<AppSettings>> {
    try {
      // 模拟从云端下载设置
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 返回当前设置作为示例
      const settings = await this.getSettings();
      return settings;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '下载设置失败',
      };
    }
  }

  // ========================================================================
  // 备份和恢复
  // ========================================================================

  async createBackup(): Promise<ServiceResponse<SettingsBackup>> {
    try {
      const settings = await this.getSettings();
      if (!settings.success) {
        return settings as any;
      }

      const backup: SettingsBackup = {
        version: '4.0.0',
        timestamp: new Date().toISOString(),
        settings: settings.data,
        deviceInfo: {
          platform: 'mobile',
          version: '4.0.0',
        },
      };

      // 保存备份
      const backupKey = `${this.BACKUP_PREFIX}${Date.now()}`;
      await defaultStorage.setObject(backupKey, backup);

      return {
        success: true,
        data: backup,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建备份失败',
      };
    }
  }

  async restoreFromBackup(backup: SettingsBackup): Promise<ServiceResponse<void>> {
    try {
      // 验证备份格式
      if (!backup.settings || !backup.version) {
        return {
          success: false,
          error: '备份格式无效',
        };
      }

      // 恢复设置
      await this.updateSettings(backup.settings);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '恢复备份失败',
      };
    }
  }

  async exportSettings(): Promise<ServiceResponse<string>> {
    try {
      const backup = await this.createBackup();
      if (!backup.success) {
        return backup as any;
      }

      return {
        success: true,
        data: JSON.stringify(backup.data, null, 2),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出设置失败',
      };
    }
  }

  async importSettings(data: string): Promise<ServiceResponse<void>> {
    try {
      const backup = JSON.parse(data) as SettingsBackup;
      return await this.restoreFromBackup(backup);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '导入设置失败',
      };
    }
  }

  // ========================================================================
  // 设置验证
  // ========================================================================

  async validateSettings(settings: Partial<AppSettings>): Promise<ServiceResponse<string[]>> {
    try {
      const errors: string[] = [];

      // 验证语言设置
      if (settings.language && !['zh-CN', 'en-US', 'ms-MY', 'th-TH'].includes(settings.language)) {
        errors.push('不支持的语言设置');
      }

      // 验证主题设置
      if (settings.theme && !['light', 'dark', 'auto'].includes(settings.theme)) {
        errors.push('不支持的主题设置');
      }

      // 验证通知时间格式
      if (settings.notifications?.quietHours) {
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(settings.notifications.quietHours.startTime)) {
          errors.push('免打扰开始时间格式无效');
        }
        if (!timeRegex.test(settings.notifications.quietHours.endTime)) {
          errors.push('免打扰结束时间格式无效');
        }
      }

      return {
        success: true,
        data: errors,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '验证设置失败',
      };
    }
  }

  async getDefaultSettings(): Promise<ServiceResponse<AppSettings>> {
    return {
      success: true,
      data: { ...this.defaultSettings },
    };
  }

  // ========================================================================
  // 辅助方法
  // ========================================================================

  private mergeWithDefaults(stored: AppSettings): AppSettings {
    return this.deepMerge(this.defaultSettings, stored);
  }

  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
}

// ============================================================================
// 导出服务实例
// ============================================================================

export const settingsService = new SettingsServiceImpl();
