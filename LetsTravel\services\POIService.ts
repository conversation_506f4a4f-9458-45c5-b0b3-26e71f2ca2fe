/**
 * Trekmate 4.0 - POI 服务
 * 提供地点搜索、附近POI查询和收藏功能的统一接口
 */

import { supabase } from './supabaseClient';
import type { POI, Location, SearchFilters, POIType } from '../types/CoreServices';

export interface POISearchParams {
  query?: string;
  location?: Location;
  radius?: number; // 单位：米
  filters?: SearchFilters;
  limit?: number;
}

export interface POIResponse {
  success: boolean;
  data?: POI[];
  error?: string;
  total?: number;
  hasMore?: boolean;
}

export interface NearbyPOIParams {
  location: Location;
  radius: number;
  category?: POIType;
  limit?: number;
}

// 扩展 POI 类型以包含距离属性
export interface POIWithDistance extends POI {
  distance?: number;
}

/**
 * POI 服务类
 */
export class POIService {
  /**
   * 搜索 POI
   */
  async searchPOIs(params: POISearchParams): Promise<POIResponse> {
    try {
      const { query, location, radius = 10000, filters, limit = 20 } = params;
      
      // 构建查询
      let queryBuilder = supabase
        .from('pois')
        .select('*')
        .limit(limit);

      // 如果有查询文本，使用文本搜索
      if (query && query.trim()) {
        queryBuilder = queryBuilder.or(`name.ilike.%${query}%,description.ilike.%${query}%,address.ilike.%${query}%`);
      }

      // 如果有分类过滤
      if (filters?.type && filters.type.length > 0) {
        queryBuilder = queryBuilder.in('category', filters.type);
      }

      // 如果有评分过滤
      if (filters?.rating && filters.rating > 0) {
        queryBuilder = queryBuilder.gte('rating', filters.rating);
      }

      const { data, error } = await queryBuilder;

      if (error) {
        console.error('POI 搜索错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // 转换数据格式
      let pois: POIWithDistance[] = (data || []).map(this.transformDatabasePOI);

      // 如果有位置，计算距离并排序
      if (location) {
        pois.forEach(poi => {
          if (poi.location) {
            poi.distance = this.calculateDistance(location, poi.location);
          }
        });
        
        // 过滤距离范围内的POI
        pois = pois.filter(poi => !poi.distance || poi.distance <= radius);
        
        // 按距离排序
        pois.sort((a, b) => (a.distance || 0) - (b.distance || 0));
      }

      return {
        success: true,
        data: pois,
        total: pois.length,
        hasMore: pois.length === limit,
      };
    } catch (error) {
      console.error('搜索 POI 失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '搜索失败',
      };
    }
  }

  /**
   * 获取附近的 POI
   */
  async getNearbyPOIs(params: NearbyPOIParams): Promise<POIResponse> {
    try {
      const { location, radius, category, limit = 20 } = params;
      
      return this.searchPOIs({
        location,
        radius,
        filters: category ? { 
          type: [category], 
          priceLevel: [], 
          rating: 0, 
          distance: radius / 1000, 
          openNow: false 
        } : undefined,
        limit,
      });
    } catch (error) {
      console.error('获取附近 POI 失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取附近地点失败',
      };
    }
  }

  /**
   * 获取 POI 详情
   */
  async getPOIDetails(poiId: string): Promise<{ success: boolean; data?: POI; error?: string }> {
    try {
      const { data, error } = await supabase
        .from('pois')
        .select('*')
        .eq('id', poiId)
        .single();

      if (error) {
        console.error('获取 POI 详情错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      const poi = this.transformDatabasePOI(data);

      return {
        success: true,
        data: poi,
      };
    } catch (error) {
      console.error('获取 POI 详情失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取详情失败',
      };
    }
  }

  /**
   * 添加到收藏
   */
  async addToFavorites(poiId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .insert({
          user_id: userId,
          poi_id: poiId,
          created_at: new Date().toISOString(),
        });

      if (error) {
        console.error('添加收藏错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return { success: true };
    } catch (error) {
      console.error('添加收藏失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '添加收藏失败',
      };
    }
  }

  /**
   * 从收藏中移除
   */
  async removeFromFavorites(poiId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', userId)
        .eq('poi_id', poiId);

      if (error) {
        console.error('移除收藏错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return { success: true };
    } catch (error) {
      console.error('移除收藏失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '移除收藏失败',
      };
    }
  }

  /**
   * 获取用户收藏的 POI
   */
  async getUserFavorites(userId: string): Promise<POIResponse> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select(`
          poi_id,
          created_at,
          pois (*)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('获取用户收藏错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      const pois: POI[] = (data || [])
        .filter(item => item.pois)
        .map(item => this.transformDatabasePOI(item.pois));

      return {
        success: true,
        data: pois,
        total: pois.length,
        hasMore: false,
      };
    } catch (error) {
      console.error('获取用户收藏失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取收藏失败',
      };
    }
  }

  /**
   * 转换数据库 POI 数据为前端格式
   */
  private transformDatabasePOI(data: any): POI {
    return {
      id: data.id,
      name: data.name,
      description: data.description || '',
      category: (data.category || 'other') as POIType,
      location: {
        latitude: data.latitude,
        longitude: data.longitude,
        address: data.address,
      },
      address: data.address,
      phone: data.phone,
      website: data.website,
      rating: data.rating,
      priceLevel: data.price_level || 1,
      openingHours: data.opening_hours ? JSON.parse(data.opening_hours) : [],
      photos: data.photos ? JSON.parse(data.photos) : [],
      tags: data.tags ? JSON.parse(data.tags) : [],
      isOpen: data.is_open,
      isFavorite: false, // 将在需要时设置
    };
  }

  /**
   * 计算两点之间的距离（米）
   */
  private calculateDistance(from: Location, to: Location): number {
    const R = 6371e3; // 地球半径（米）
    const φ1 = from.latitude * Math.PI / 180;
    const φ2 = to.latitude * Math.PI / 180;
    const Δφ = (to.latitude - from.latitude) * Math.PI / 180;
    const Δλ = (to.longitude - from.longitude) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }
}

// 导出单例实例
export const poiService = new POIService(); 