# ninja log v5
1	2619	7732460493175390	CMakeFiles/rnscreens.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	76d0209b488b5891
25	3051	7732460497470291	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	e1527ec3cd9bbf8d
16	4685	7732460513788726	CMakeFiles/rnscreens.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	902d76df81f4b00f
35	5079	7732460517839272	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	4c5f6896fc4dbfe3
9	6643	7732460533290960	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	6c3aff6c5de3e1f4
6646	6841	7732460535432458	../../../../build/intermediates/cxx/Debug/571h524k/obj/armeabi-v7a/librnscreens.so	8a514e4498a9f4a7
