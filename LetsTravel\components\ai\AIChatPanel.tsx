/**
 * Trekmate 4.0 - AI聊天面板组件
 * 提供与AI助手的对话界面
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { AI_ASSISTANT, COMMON } from '../../constants/Strings';
import { aiPlanService } from '../../services/ai/AIPlanService';
import type { AIContextData } from '../../services/ai/AIPlanService';
import type { Journey } from '../../types/CoreServices';

// ============================================================================
// 聊天消息类型
// ============================================================================

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
}

// ============================================================================
// AI聊天面板Props
// ============================================================================

interface AIChatPanelProps {
  journey?: Journey;
  context?: AIContextData;
  onClose?: () => void;
  style?: any;
}

// ============================================================================
// 消息气泡组件
// ============================================================================

interface MessageBubbleProps {
  message: ChatMessage;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.type === 'user';
  
  return (
    <View style={[
      styles.messageContainer,
      isUser ? styles.userMessageContainer : styles.assistantMessageContainer,
    ]}>
      {!isUser && (
        <View style={styles.assistantAvatar}>
          <Ionicons name="sparkles" size={16} color={colors.primary[500]} />
        </View>
      )}
      
      <View style={[
        styles.messageBubble,
        isUser ? styles.userMessageBubble : styles.assistantMessageBubble,
      ]}>
        {message.isTyping ? (
          <View style={styles.typingIndicator}>
            <ActivityIndicator size="small" color={colors.textSecondary} />
            <Text style={styles.typingText}>AI助手正在思考...</Text>
          </View>
        ) : (
          <Text style={[
            styles.messageText,
            isUser ? styles.userMessageText : styles.assistantMessageText,
          ]}>
            {message.content}
          </Text>
        )}
      </View>
      
      <Text style={styles.messageTime}>
        {message.timestamp.toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        })}
      </Text>
    </View>
  );
};

// ============================================================================
// AI聊天面板组件
// ============================================================================

export default function AIChatPanel({
  journey,
  context,
  onClose,
  style,
}: AIChatPanelProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'welcome',
      type: 'assistant',
      content: '您好！我是您的AI旅行助手。我可以帮您优化行程、推荐景点、回答旅行相关问题。有什么可以帮助您的吗？',
      timestamp: new Date(),
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  
  const scrollViewRef = useRef<ScrollView>(null);

  // 加载聊天建议
  useEffect(() => {
    loadChatSuggestions();
  }, []);

  const loadChatSuggestions = async () => {
    try {
      const response = await aiPlanService.getChatSuggestions([]);
      if (response.success && response.data) {
        setSuggestions(response.data);
      }
    } catch (error) {
      console.error('加载聊天建议失败:', error);
    }
  };

  // 发送消息
  const handleSendMessage = useCallback(async (text?: string) => {
    const messageText = text || inputText.trim();
    if (!messageText || isLoading) return;

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: messageText,
      timestamp: new Date(),
    };

    const typingMessage: ChatMessage = {
      id: `typing_${Date.now()}`,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      isTyping: true,
    };

    setMessages(prev => [...prev, userMessage, typingMessage]);
    setInputText('');
    setIsLoading(true);

    // 滚动到底部
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      const response = await aiPlanService.askAssistant(messageText, {
        ...context,
        userHistory: journey ? [journey] : [],
      });

      if (response.success && response.data) {
        const assistantMessage: ChatMessage = {
          id: `assistant_${Date.now()}`,
          type: 'assistant',
          content: response.data,
          timestamp: new Date(),
        };

        setMessages(prev => prev.slice(0, -1).concat(assistantMessage));
      } else {
        const errorMessage: ChatMessage = {
          id: `error_${Date.now()}`,
          type: 'assistant',
          content: '抱歉，我现在无法回答您的问题。请稍后再试。',
          timestamp: new Date(),
        };

        setMessages(prev => prev.slice(0, -1).concat(errorMessage));
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'assistant',
        content: '网络连接出现问题，请检查您的网络设置。',
        timestamp: new Date(),
      };

      setMessages(prev => prev.slice(0, -1).concat(errorMessage));
    } finally {
      setIsLoading(false);
      
      // 滚动到底部
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [inputText, isLoading, context, journey]);

  // 处理建议点击
  const handleSuggestionPress = useCallback((suggestion: string) => {
    handleSendMessage(suggestion);
  }, [handleSendMessage]);

  // 渲染头部
  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <View style={styles.aiAvatar}>
          <Ionicons name="sparkles" size={20} color={colors.primary[500]} />
        </View>
        <View>
          <Text style={styles.headerTitle}>{AI_ASSISTANT.AI_ASSISTANT}</Text>
          <Text style={styles.headerSubtitle}>
            {isLoading ? '正在输入...' : '在线'}
          </Text>
        </View>
      </View>
      
      {onClose && (
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>
      )}
    </View>
  );

  // 渲染快捷建议
  const renderSuggestions = () => {
    if (messages.length > 1 || suggestions.length === 0) return null;

    return (
      <View style={styles.suggestionsContainer}>
        <Text style={styles.suggestionsTitle}>您可以问我：</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.suggestionsList}>
            {suggestions.map((suggestion, index) => (
              <TouchableOpacity
                key={index}
                style={styles.suggestionChip}
                onPress={() => handleSuggestionPress(suggestion)}
              >
                <Text style={styles.suggestionText}>{suggestion}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  };

  // 渲染消息列表
  const renderMessages = () => (
    <ScrollView
      ref={scrollViewRef}
      style={styles.messagesContainer}
      showsVerticalScrollIndicator={false}
      onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
    >
      {messages.map((message) => (
        <MessageBubble key={message.id} message={message} />
      ))}
    </ScrollView>
  );

  // 渲染输入框
  const renderInput = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.inputContainer}
    >
      <View style={styles.inputRow}>
        <TextInput
          style={styles.textInput}
          placeholder={AI_ASSISTANT.ASK_ANYTHING}
          placeholderTextColor={colors.textSecondary}
          value={inputText}
          onChangeText={setInputText}
          multiline
          maxLength={500}
          editable={!isLoading}
        />
        
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!inputText.trim() || isLoading) && styles.sendButtonDisabled,
          ]}
          onPress={() => handleSendMessage()}
          disabled={!inputText.trim() || isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color={colors.surface} />
          ) : (
            <Ionicons name="send" size={20} color={colors.surface} />
          )}
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );

  return (
    <View style={[styles.container, style]}>
      {renderHeader()}
      {renderSuggestions()}
      {renderMessages()}
      {renderInput()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    ...shadows.sm,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[3],
  },
  aiAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  headerSubtitle: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  closeButton: {
    padding: spacing[1],
  },
  suggestionsContainer: {
    backgroundColor: colors.surface,
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  suggestionsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing[2],
    paddingHorizontal: spacing[4],
  },
  suggestionsList: {
    flexDirection: 'row',
    paddingHorizontal: spacing[4],
    gap: spacing[2],
  },
  suggestionChip: {
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
  },
  suggestionText: {
    fontSize: 14,
    color: colors.primary[700],
    fontWeight: '500',
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
  },
  messageContainer: {
    marginVertical: spacing[2],
  },
  userMessageContainer: {
    alignItems: 'flex-end',
  },
  assistantMessageContainer: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    gap: spacing[2],
  },
  assistantAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: spacing[1],
  },
  messageBubble: {
    maxWidth: '80%',
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
  },
  userMessageBubble: {
    backgroundColor: colors.primary[500],
  },
  assistantMessageBubble: {
    backgroundColor: colors.neutral[100],
  },
  messageText: {
    fontSize: 14,
    lineHeight: 20,
  },
  userMessageText: {
    color: colors.surface,
  },
  assistantMessageText: {
    color: colors.text,
  },
  messageTime: {
    fontSize: 11,
    color: colors.textSecondary,
    marginTop: spacing[1],
    alignSelf: 'flex-end',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
  typingText: {
    fontSize: 14,
    color: colors.textSecondary,
    fontStyle: 'italic',
  },
  inputContainer: {
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing[3],
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    fontSize: 16,
    color: colors.text,
    maxHeight: 100,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.neutral[300],
  },
});
