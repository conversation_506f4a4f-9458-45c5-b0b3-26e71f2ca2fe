{"artifacts": [{"path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/build/intermediates/cxx/Debug/6k1h1t2m/obj/armeabi-v7a/libexpo-gl.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_options", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 1, "file": 0, "line": 49, "parent": 0}, {"command": 2, "file": 0, "line": 39, "parent": 0}, {"command": 3, "file": 0, "line": 31, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 3, "fragment": "-O2"}, {"backtrace": 3, "fragment": "-fexceptions"}, {"backtrace": 3, "fragment": "-frtti"}, {"backtrace": 3, "fragment": "-Wall"}, {"backtrace": 3, "fragment": "-Wextra"}, {"backtrace": 3, "fragment": "-Wno-unused-parameter"}, {"backtrace": 3, "fragment": "-Wshorten-64-to-32"}, {"backtrace": 3, "fragment": "-Wstrict-prototypes"}, {"fragment": "-std=gnu++20"}], "defines": [{"define": "expo_gl_EXPORTS"}], "includes": [{"backtrace": 4, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 14], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "expo-gl::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\24\\liblog.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\24\\libGLESv3.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "expo-gl", "nameOnDisk": "libexpo-gl.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 14]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 7, 9, 11, 13]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLNativeApi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLNativeApi.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLImageUtils.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLImageUtils.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLNativeContext.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLNativeContext.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLContextManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLContextManager.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXWebGLMethods.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXWebGLMethods.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXWebGLRenderer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXWebGLRenderer.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXTypedArrayApi.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXTypedArrayApi.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/EXGLJniApi.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}