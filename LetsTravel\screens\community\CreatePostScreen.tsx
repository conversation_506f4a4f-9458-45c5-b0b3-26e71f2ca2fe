/**
 * Trekmate 4.0 - 发布帖子屏幕
 * 用户创建和编辑社区帖子的界面
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { COMMUNITY, COMMON } from '../../constants/Strings';
import SmartLocationInput from '../../components/forms/SmartLocationInput';
import { communityService } from '../../services/community/CommunityService';
import { useCurrentUser, useLoading } from '../../hooks/useStore';
import type { CreatePostScreenProps } from '../../types/Navigation';
import type { PostCreateData } from '../../services/community/CommunityService';
import type { Location } from '../../types/CoreServices';

// ============================================================================
// 发布帖子屏幕组件
// ============================================================================

export default function CreatePostScreen({ navigation, route }: CreatePostScreenProps) {
  const { postId, journeyId, activityId } = route.params || {};
  const user = useCurrentUser();
  const { setLoading } = useLoading();

  // 表单状态
  const [formData, setFormData] = useState<{
    content: string;
    images: string[];
    location: Location | null;
    tags: string[];
    visibility: 'public' | 'friends' | 'private';
  }>({
    content: '',
    images: [],
    location: null,
    tags: [],
    visibility: 'public',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [tagInput, setTagInput] = useState('');

  // 验证表单
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.content.trim()) {
      newErrors.content = COMMUNITY.CONTENT_REQUIRED;
    } else if (formData.content.length > 1000) {
      newErrors.content = COMMUNITY.CONTENT_TOO_LONG;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理发布
  const handlePublish = useCallback(async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const postData: PostCreateData = {
        content: formData.content.trim(),
        images: formData.images,
        location: formData.location,
        journeyId,
        activityId,
        tags: formData.tags,
        visibility: formData.visibility,
      };

      const response = await communityService.createPost(postData);

      if (response.success) {
        Alert.alert(
          COMMON.SUCCESS,
          COMMUNITY.POST_CREATED,
          [
            {
              text: COMMON.OK,
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert(COMMON.ERROR, response.error || COMMUNITY.POST_CREATE_ERROR);
      }
    } catch (error) {
      Alert.alert(COMMON.ERROR, COMMUNITY.NETWORK_ERROR);
    } finally {
      setLoading(false);
    }
  }, [formData, journeyId, activityId, navigation, setLoading]);

  // 处理添加标签
  const handleAddTag = useCallback(() => {
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag) && formData.tags.length < 10) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag],
      }));
      setTagInput('');
    }
  }, [tagInput, formData.tags]);

  // 处理删除标签
  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  }, []);

  // 处理添加图片
  const handleAddImage = useCallback(() => {
    // 这里应该打开图片选择器
    Alert.alert('提示', '图片选择功能将在后续版本中实现');
  }, []);

  // 处理删除图片
  const handleRemoveImage = useCallback((imageToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(img => img !== imageToRemove),
    }));
  }, []);

  // 渲染头部
  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Ionicons name="close" size={24} color={colors.text} />
      </TouchableOpacity>
      
      <Text style={styles.headerTitle}>
        {postId ? COMMUNITY.EDIT_POST : COMMUNITY.CREATE_POST}
      </Text>
      
      <TouchableOpacity
        style={[
          styles.publishButton,
          !formData.content.trim() && styles.publishButtonDisabled,
        ]}
        onPress={handlePublish}
        disabled={!formData.content.trim()}
      >
        <Text style={[
          styles.publishButtonText,
          !formData.content.trim() && styles.publishButtonTextDisabled,
        ]}>
          {COMMUNITY.POST}
        </Text>
      </TouchableOpacity>
    </View>
  );

  // 渲染内容输入
  const renderContentInput = () => (
    <View style={styles.section}>
      <TextInput
        style={[styles.contentInput, errors.content && styles.inputError]}
        placeholder={COMMUNITY.POST_CONTENT}
        placeholderTextColor={colors.textSecondary}
        value={formData.content}
        onChangeText={(text) => setFormData(prev => ({ ...prev, content: text }))}
        multiline
        maxLength={1000}
        textAlignVertical="top"
      />
      
      <View style={styles.contentFooter}>
        {errors.content && (
          <Text style={styles.errorText}>{errors.content}</Text>
        )}
        <Text style={styles.characterCount}>
          {formData.content.length}/1000
        </Text>
      </View>
    </View>
  );

  // 渲染图片区域
  const renderImages = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{COMMUNITY.ADD_PHOTOS}</Text>
      
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <View style={styles.imagesContainer}>
          {formData.images.map((image, index) => (
            <View key={index} style={styles.imageContainer}>
              <Image source={{ uri: image }} style={styles.image} />
              <TouchableOpacity
                style={styles.removeImageButton}
                onPress={() => handleRemoveImage(image)}
              >
                <Ionicons name="close-circle" size={20} color={colors.error} />
              </TouchableOpacity>
            </View>
          ))}
          
          {formData.images.length < 9 && (
            <TouchableOpacity style={styles.addImageButton} onPress={handleAddImage}>
              <Ionicons name="camera" size={24} color={colors.textSecondary} />
              <Text style={styles.addImageText}>添加照片</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>
    </View>
  );

  // 渲染位置选择
  const renderLocation = () => (
    <View style={styles.section}>
      <SmartLocationInput
        label={COMMUNITY.ADD_LOCATION}
        value={formData.location}
        onChange={(location) => setFormData(prev => ({ ...prev, location }))}
        placeholder="添加位置信息..."
      />
    </View>
  );

  // 渲染标签输入
  const renderTags = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{COMMUNITY.ADD_TAGS}</Text>
      
      {/* 已添加的标签 */}
      {formData.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {formData.tags.map((tag, index) => (
            <TouchableOpacity
              key={index}
              style={styles.tag}
              onPress={() => handleRemoveTag(tag)}
            >
              <Text style={styles.tagText}>#{tag}</Text>
              <Ionicons name="close" size={14} color={colors.primary[700]} />
            </TouchableOpacity>
          ))}
        </View>
      )}
      
      {/* 标签输入 */}
      <View style={styles.tagInputContainer}>
        <TextInput
          style={styles.tagInput}
          placeholder="输入标签..."
          placeholderTextColor={colors.textSecondary}
          value={tagInput}
          onChangeText={setTagInput}
          onSubmitEditing={handleAddTag}
          returnKeyType="done"
        />
        <TouchableOpacity
          style={[
            styles.addTagButton,
            !tagInput.trim() && styles.addTagButtonDisabled,
          ]}
          onPress={handleAddTag}
          disabled={!tagInput.trim()}
        >
          <Ionicons name="add" size={20} color={colors.primary[500]} />
        </TouchableOpacity>
      </View>
    </View>
  );

  // 渲染可见性设置
  const renderVisibility = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>可见性</Text>
      
      <View style={styles.visibilityContainer}>
        {[
          { value: 'public', label: '公开', icon: 'globe-outline', description: '所有人可见' },
          { value: 'friends', label: '朋友', icon: 'people-outline', description: '仅朋友可见' },
          { value: 'private', label: '私密', icon: 'lock-closed-outline', description: '仅自己可见' },
        ].map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.visibilityOption,
              formData.visibility === option.value && styles.visibilityOptionActive,
            ]}
            onPress={() => setFormData(prev => ({ 
              ...prev, 
              visibility: option.value as 'public' | 'friends' | 'private' 
            }))}
          >
            <Ionicons
              name={option.icon as any}
              size={20}
              color={
                formData.visibility === option.value
                  ? colors.primary[500]
                  : colors.textSecondary
              }
            />
            <View style={styles.visibilityOptionContent}>
              <Text style={[
                styles.visibilityOptionLabel,
                formData.visibility === option.value && styles.visibilityOptionLabelActive,
              ]}>
                {option.label}
              </Text>
              <Text style={styles.visibilityOptionDescription}>
                {option.description}
              </Text>
            </View>
            {formData.visibility === option.value && (
              <Ionicons name="checkmark" size={20} color={colors.primary[500]} />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {renderHeader()}
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderContentInput()}
          {renderImages()}
          {renderLocation()}
          {renderTags()}
          {renderVisibility()}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  publishButton: {
    backgroundColor: colors.primary[500],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
  },
  publishButtonDisabled: {
    backgroundColor: colors.neutral[200],
  },
  publishButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.surface,
  },
  publishButtonTextDisabled: {
    color: colors.textSecondary,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: colors.surface,
    marginBottom: spacing[3],
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[3],
  },
  contentInput: {
    fontSize: 16,
    color: colors.text,
    lineHeight: 24,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: colors.error,
  },
  contentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing[2],
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
  },
  characterCount: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  imagesContainer: {
    flexDirection: 'row',
    gap: spacing[3],
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.md,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.surface,
    borderRadius: 10,
  },
  addImageButton: {
    width: 80,
    height: 80,
    borderRadius: borderRadius.md,
    borderWidth: 2,
    borderColor: colors.border,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addImageText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: spacing[1],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[2],
    marginBottom: spacing[3],
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    gap: spacing[1],
  },
  tagText: {
    fontSize: 14,
    color: colors.primary[700],
    fontWeight: '500',
  },
  tagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[3],
  },
  tagInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    paddingVertical: spacing[3],
  },
  addTagButton: {
    padding: spacing[1],
  },
  addTagButtonDisabled: {
    opacity: 0.5,
  },
  visibilityContainer: {
    gap: spacing[3],
  },
  visibilityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[3],
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.md,
    gap: spacing[3],
  },
  visibilityOptionActive: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  visibilityOptionContent: {
    flex: 1,
  },
  visibilityOptionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: spacing[1],
  },
  visibilityOptionLabelActive: {
    color: colors.primary[700],
  },
  visibilityOptionDescription: {
    fontSize: 14,
    color: colors.textSecondary,
  },
});
