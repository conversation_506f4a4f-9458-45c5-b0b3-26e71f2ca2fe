/**
 * Trekmate 4.0 - 应用集成测试套件
 * 测试应用的完整功能流程和模块间集成
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';

// 导入主要组件和服务
import App from '../../App';
import { journeyService } from '../../services/journey/JourneyService';
import { exploreService } from '../../services/explore/ExploreService';
import { communityService } from '../../services/community/CommunityService';
import { aiPlanService } from '../../services/ai/AIPlanService';
import { performanceService } from '../../services/optimization/PerformanceService';
import { settingsService } from '../../services/settings/SettingsService';

// Mock 所有服务
jest.mock('../../services/journey/JourneyService');
jest.mock('../../services/explore/ExploreService');
jest.mock('../../services/community/CommunityService');
jest.mock('../../services/ai/AIPlanService');
jest.mock('../../services/optimization/PerformanceService');
jest.mock('../../services/settings/SettingsService');

// Mock React Navigation
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    setOptions: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
}));

// Mock Expo modules
jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  getCurrentPositionAsync: jest.fn(() => Promise.resolve({
    coords: {
      latitude: 3.1390,
      longitude: 101.6869,
      accuracy: 10,
    },
  })),
}));

jest.mock('expo-camera', () => ({
  Camera: {
    requestCameraPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  },
}));

describe('Trekmate 4.0 应用集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock 服务响应
    setupServiceMocks();
  });

  const setupServiceMocks = () => {
    // Journey Service Mocks
    (journeyService.getUserJourneys as jest.Mock).mockResolvedValue({
      success: true,
      data: [
        {
          id: 'journey_1',
          title: '吉隆坡3日游',
          description: '探索马来西亚首都',
          startDate: '2024-01-15',
          endDate: '2024-01-17',
          status: 'planning',
          activities: [],
          estimatedBudget: 1500,
          actualBudget: 0,
        },
      ],
    });

    (journeyService.createJourney as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        id: 'journey_new',
        title: '新行程',
        description: '测试行程',
        startDate: '2024-02-01',
        endDate: '2024-02-03',
        status: 'planning',
        activities: [],
        estimatedBudget: 1000,
        actualBudget: 0,
      },
    });

    // Explore Service Mocks
    (exploreService.searchPOIs as jest.Mock).mockResolvedValue({
      success: true,
      data: [
        {
          id: 'poi_1',
          name: '双子塔',
          category: 'landmark',
          location: {
            latitude: 3.1579,
            longitude: 101.7116,
            address: '双子塔, 吉隆坡',
          },
          rating: 4.5,
          description: '吉隆坡的标志性建筑',
        },
      ],
    });

    // Community Service Mocks
    (communityService.getFeed as jest.Mock).mockResolvedValue({
      success: true,
      data: [
        {
          id: 'post_1',
          authorId: 'user_1',
          authorName: '旅行达人',
          content: '分享我的吉隆坡之旅',
          tags: ['吉隆坡', '旅行'],
          likes: 25,
          comments: 5,
          shares: 3,
          isLiked: false,
          isBookmarked: false,
          visibility: 'public',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    });

    // AI Service Mocks
    (aiPlanService.getActivitySuggestions as jest.Mock).mockResolvedValue({
      success: true,
      data: [
        {
          id: 'suggestion_1',
          type: 'activity',
          title: '推荐参观双子塔',
          description: '吉隆坡必游景点',
          confidence: 0.9,
          data: {
            name: '双子塔',
            type: 'sightseeing',
            duration: 120,
          },
          reasoning: '基于您的兴趣推荐',
        },
      ],
    });

    // Performance Service Mocks
    (performanceService.getCurrentMetrics as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        memoryUsage: {
          used: 150,
          total: 512,
          percentage: 29.3,
        },
        renderTime: {
          average: 12.5,
          max: 25.0,
          min: 8.0,
        },
        networkRequests: {
          total: 50,
          successful: 48,
          failed: 2,
          averageResponseTime: 250,
        },
        storageUsage: {
          used: 1024,
          available: 2048,
          percentage: 50.0,
        },
      },
    });

    // Settings Service Mocks
    (settingsService.getSettings as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        language: 'zh-CN',
        theme: 'auto',
        currency: 'MYR',
        units: 'metric',
        privacy: {
          shareLocation: true,
          shareActivity: true,
          allowAnalytics: true,
          allowCrashReports: true,
        },
        notifications: {
          enabled: true,
          journeyReminders: true,
          weatherAlerts: true,
          communityUpdates: true,
          promotions: false,
          quietHours: {
            enabled: false,
            startTime: '22:00',
            endTime: '08:00',
          },
        },
        map: {
          defaultMapType: 'standard',
          showTraffic: true,
          showPublicTransit: true,
          offlineDownloads: false,
          autoDownloadMaps: false,
        },
        camera: {
          saveToGallery: true,
          includeLocation: true,
          imageQuality: 'high',
          autoBackup: false,
        },
        sync: {
          enabled: true,
          autoSync: true,
          syncOnWifiOnly: true,
        },
        performance: {
          enableAnimations: true,
          reducedMotion: false,
          lowDataMode: false,
          cacheSize: 'medium',
        },
        accessibility: {
          fontSize: 'medium',
          highContrast: false,
          screenReader: false,
          hapticFeedback: true,
        },
      },
    });
  };

  describe('应用启动和初始化', () => {
    it('应该成功启动应用', async () => {
      render(<App />);

      // 验证应用启动
      await waitFor(() => {
        expect(screen.getByText('Trekmate')).toBeTruthy();
      });
    });

    it('应该加载用户设置', async () => {
      render(<App />);

      await waitFor(() => {
        expect(settingsService.getSettings).toHaveBeenCalled();
      });
    });

    it('应该初始化性能监控', async () => {
      render(<App />);

      await waitFor(() => {
        expect(performanceService.getCurrentMetrics).toHaveBeenCalled();
      });
    });
  });

  describe('核心功能流程测试', () => {
    it('应该支持完整的行程创建流程', async () => {
      render(<App />);

      // 等待应用加载
      await waitFor(() => {
        expect(screen.getByText('Trekmate')).toBeTruthy();
      });

      // 模拟导航到行程页面
      const journeyTab = screen.getByText('行程');
      fireEvent.press(journeyTab);

      // 验证行程服务调用
      await waitFor(() => {
        expect(journeyService.getUserJourneys).toHaveBeenCalled();
      });

      // 模拟创建新行程
      const createButton = screen.getByText('创建行程');
      fireEvent.press(createButton);

      // 验证创建行程表单
      expect(screen.getByText('新建行程')).toBeTruthy();
    });

    it('应该支持POI搜索和探索', async () => {
      render(<App />);

      // 导航到探索页面
      const exploreTab = screen.getByText('探索');
      fireEvent.press(exploreTab);

      // 验证探索服务调用
      await waitFor(() => {
        expect(exploreService.searchPOIs).toHaveBeenCalled();
      });

      // 验证POI列表显示
      await waitFor(() => {
        expect(screen.getByText('双子塔')).toBeTruthy();
      });
    });

    it('应该支持社区动态浏览', async () => {
      render(<App />);

      // 导航到社区页面
      const communityTab = screen.getByText('社区');
      fireEvent.press(communityTab);

      // 验证社区服务调用
      await waitFor(() => {
        expect(communityService.getFeed).toHaveBeenCalled();
      });

      // 验证动态列表显示
      await waitFor(() => {
        expect(screen.getByText('分享我的吉隆坡之旅')).toBeTruthy();
      });
    });
  });

  describe('AI功能集成测试', () => {
    it('应该提供AI行程建议', async () => {
      render(<App />);

      // 导航到行程页面
      const journeyTab = screen.getByText('行程');
      fireEvent.press(journeyTab);

      // 模拟点击AI建议
      const aiButton = screen.getByText('AI建议');
      fireEvent.press(aiButton);

      // 验证AI服务调用
      await waitFor(() => {
        expect(aiPlanService.getActivitySuggestions).toHaveBeenCalled();
      });

      // 验证建议显示
      await waitFor(() => {
        expect(screen.getByText('推荐参观双子塔')).toBeTruthy();
      });
    });

    it('应该支持AI聊天功能', async () => {
      render(<App />);

      // 打开AI聊天
      const chatButton = screen.getByText('AI助手');
      fireEvent.press(chatButton);

      // 验证聊天界面
      expect(screen.getByText('AI助手')).toBeTruthy();
      expect(screen.getByPlaceholderText('问我任何关于旅行的问题...')).toBeTruthy();
    });
  });

  describe('性能和优化测试', () => {
    it('应该监控应用性能', async () => {
      render(<App />);

      await waitFor(() => {
        expect(performanceService.getCurrentMetrics).toHaveBeenCalled();
      });
    });

    it('应该支持性能优化建议', async () => {
      (performanceService.getOptimizationSuggestions as jest.Mock).mockResolvedValue({
        success: true,
        data: [
          {
            id: 'memory_optimization',
            category: 'performance',
            title: '内存优化',
            description: '当前内存使用较高',
            priority: 'high',
          },
        ],
      });

      render(<App />);

      // 导航到设置页面
      const settingsTab = screen.getByText('设置');
      fireEvent.press(settingsTab);

      // 查看性能设置
      const performanceSection = screen.getByText('性能');
      fireEvent.press(performanceSection);

      await waitFor(() => {
        expect(performanceService.getOptimizationSuggestions).toHaveBeenCalled();
      });
    });
  });

  describe('设置和配置测试', () => {
    it('应该支持设置修改', async () => {
      render(<App />);

      // 导航到设置页面
      const settingsTab = screen.getByText('设置');
      fireEvent.press(settingsTab);

      // 验证设置加载
      await waitFor(() => {
        expect(settingsService.getSettings).toHaveBeenCalled();
      });

      // 模拟修改设置
      const themeToggle = screen.getByText('深色模式');
      fireEvent.press(themeToggle);

      // 验证设置更新
      await waitFor(() => {
        expect(settingsService.updateSettings).toHaveBeenCalled();
      });
    });

    it('应该支持设置同步', async () => {
      render(<App />);

      // 导航到设置页面
      const settingsTab = screen.getByText('设置');
      fireEvent.press(settingsTab);

      // 模拟同步设置
      const syncButton = screen.getByText('同步设置');
      fireEvent.press(syncButton);

      await waitFor(() => {
        expect(settingsService.syncSettings).toHaveBeenCalled();
      });
    });
  });

  describe('错误处理和恢复测试', () => {
    it('应该处理网络错误', async () => {
      // Mock 网络错误
      (journeyService.getUserJourneys as jest.Mock).mockRejectedValue(
        new Error('网络连接失败')
      );

      render(<App />);

      const journeyTab = screen.getByText('行程');
      fireEvent.press(journeyTab);

      // 验证错误处理
      await waitFor(() => {
        expect(screen.getByText('网络连接失败')).toBeTruthy();
      });
    });

    it('应该处理服务错误', async () => {
      // Mock 服务错误
      (exploreService.searchPOIs as jest.Mock).mockResolvedValue({
        success: false,
        error: '服务暂时不可用',
      });

      render(<App />);

      const exploreTab = screen.getByText('探索');
      fireEvent.press(exploreTab);

      await waitFor(() => {
        expect(screen.getByText('服务暂时不可用')).toBeTruthy();
      });
    });

    it('应该支持离线模式', async () => {
      // Mock 离线状态
      (settingsService.getSettings as jest.Mock).mockResolvedValue({
        success: true,
        data: {
          ...settingsService.getSettings(),
          performance: {
            lowDataMode: true,
          },
        },
      });

      render(<App />);

      // 验证离线模式提示
      await waitFor(() => {
        expect(screen.getByText('离线模式')).toBeTruthy();
      });
    });
  });

  describe('数据持久化测试', () => {
    it('应该保存用户数据', async () => {
      render(<App />);

      // 模拟创建行程
      const journeyTab = screen.getByText('行程');
      fireEvent.press(journeyTab);

      const createButton = screen.getByText('创建行程');
      fireEvent.press(createButton);

      // 填写行程信息
      const titleInput = screen.getByPlaceholderText('输入行程标题');
      fireEvent.changeText(titleInput, '测试行程');

      const saveButton = screen.getByText('保存');
      fireEvent.press(saveButton);

      // 验证数据保存
      await waitFor(() => {
        expect(journeyService.createJourney).toHaveBeenCalledWith(
          expect.objectContaining({
            title: '测试行程',
          })
        );
      });
    });

    it('应该恢复用户数据', async () => {
      render(<App />);

      // 验证数据恢复
      await waitFor(() => {
        expect(journeyService.getUserJourneys).toHaveBeenCalled();
        expect(settingsService.getSettings).toHaveBeenCalled();
      });
    });
  });

  describe('用户体验测试', () => {
    it('应该提供流畅的导航体验', async () => {
      render(<App />);

      // 测试标签页切换
      const tabs = ['探索', '行程', '社区', '我的'];
      
      for (const tab of tabs) {
        const tabButton = screen.getByText(tab);
        fireEvent.press(tabButton);
        
        // 验证页面切换
        await waitFor(() => {
          expect(screen.getByText(tab)).toBeTruthy();
        });
      }
    });

    it('应该支持搜索功能', async () => {
      render(<App />);

      // 导航到探索页面
      const exploreTab = screen.getByText('探索');
      fireEvent.press(exploreTab);

      // 使用搜索功能
      const searchInput = screen.getByPlaceholderText('搜索景点、餐厅...');
      fireEvent.changeText(searchInput, '双子塔');

      // 验证搜索结果
      await waitFor(() => {
        expect(exploreService.searchPOIs).toHaveBeenCalledWith(
          expect.objectContaining({
            query: '双子塔',
          })
        );
      });
    });

    it('应该支持多语言切换', async () => {
      render(<App />);

      // 导航到设置页面
      const settingsTab = screen.getByText('设置');
      fireEvent.press(settingsTab);

      // 切换语言
      const languageOption = screen.getByText('English');
      fireEvent.press(languageOption);

      // 验证语言更新
      await waitFor(() => {
        expect(settingsService.updateSettings).toHaveBeenCalledWith(
          expect.objectContaining({
            language: 'en-US',
          })
        );
      });
    });
  });
});
