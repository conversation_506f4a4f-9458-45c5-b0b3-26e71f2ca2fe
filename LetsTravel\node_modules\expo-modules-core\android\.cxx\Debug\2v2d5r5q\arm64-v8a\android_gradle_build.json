{"buildFiles": ["C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\prefab\\arm64-v8a\\prefab\\lib\\aarch64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\fabric\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"expo-modules-core::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "expo-modules-core", "output": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\build\\intermediates\\cxx\\Debug\\2v2d5r5q\\obj\\arm64-v8a\\libexpo-modules-core.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1957b0db02baa50e26af4d5b92e700f6\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1957b0db02baa50e26af4d5b92e700f6\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "fabric::@3c04bbf757b97f4dae7c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "fabric", "output": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\arm64-v8a\\src\\fabric\\libfabric.a"}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}