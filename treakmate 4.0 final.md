# Trekmate 4.0 Personal Planner - 优化版集成开发计划

## 摘要

本计划基于初始版本，并深度融合了关于**工程化、协作流程、技术架构、用户体验、质量保证**的专业建议。旨在提供一份全面、具体、可执行的开发路线图，确保 Personal Planner 项目以业界领先的标准高质量交付。

## 核心开发原则

项目全程将遵循以下核心原则：

- **代码审查 (Code Review)**: 所有代码变更必须经过至少一位团队成员审查，确保质量与风格统一。
- **持续集成/部署 (CI/CD)**: 自动化测试、构建和部署流程，实现快速、可靠的交付。
- **API Mock 优先 (API Mock First)**: 前后端并行开发，通过 Mock 服务解除依赖，提升效率。
- **测试驱动 (Testing Driven)**: 单元、集成、E2E 测试三位一体，确保功能稳定与代码健壮。
- **用户体验至上 (UX First)**: 所有功能设计均以用户为中心，追求极致的易用性、性能和视觉体验。
- **无障碍设计 (Accessibility by Design)**: 从开发之初就融入 a11y 原则，确保产品对所有用户友好。

---

## 第一部分：分阶段详细开发计划 (4周)

### Phase 1：基础设施与智能搜索 (Week 1)

**Day 1：项目基础设施与协作环境**
- **目标**: 建立健壮、高效、自动化的开发与协作基础。
- **任务清单**:
    - [ ] 创建 Personal Planner 专用开发分支，并配置分支保护规则。
    - [ ] 配置 TypeScript 严格模式、ESLint + Prettier 代码风格检查与自动格式化。
    - [ ] 配置单元测试框架（Jest/Testing Library）与初始测试用例。
    - [ ] **【新增】** 配置 CI/CD 自动化流程（GitHub Actions）：自动运行测试、代码检查。
    - [ ] **【新增】** 配置 API Mock 服务（MSW），定义核心 API 的 Mock 数据。
    - [ ] **【新增】** 建立代码审查（Code Review）流程与规范。
- **验收标准**:
    - 开发环境、Mock、测试、CI/CD、代码风格检查全部可用。
    - 团队成员熟悉并能遵循协作流程。

**Day 2：智能搜索服务开发**
- **目标**: 构建高性能、高可用的核心智能搜索服务。
- **任务清单**:
    - [ ] 设计 `SmartSearchService` 架构，集成 Fuse.js 与 React Query。
    - [ ] 实现景点、酒店、餐厅搜索 API 集成（优先使用 Mock 数据）。
    - [ ] **【优化】** 实现精细化的搜索缓存机制（React Query）。
    - [ ] **【新增】** 实现完善的 API 错误处理与服务降级逻辑。
    - [ ] 编写服务单元测试（覆盖率 > 85%）。
- **验收标准**:
    - API 集成、缓存、降级、测试全部通过，Mock 服务可独立支持前端开发。

**Day 3：智能输入组件开发**
- **目标**: 开发具备实时反馈和良好体验的智能输入组件。
- **任务清单**:
    - [ ] 开发 `SmartAttractionInput`, `SmartHotelInput`, `SmartRestaurantInput` 组件。
    - [ ] 实现实时搜索、建议高亮、300ms 防抖优化。
    - [ ] **【新增】** 集成无障碍（a11y）支持（如 ARIA 属性）。
    - [ ] 编写组件单元测试（覆盖率 > 90%）。
- **验收标准**:
    - 输入组件功能完整，性能优良，符合 a11y 标准，测试通过。

**Day 4：搜索建议 UI 组件**
- **目标**: 开发交互流畅、信息清晰的搜索建议列表。
- **任务清单**:
    - [ ] 开发 `SearchSuggestionList` 组件。
    - [ ] 实现点击选择、键盘导航、移动端触摸优化。
    - [ ] 开发加载（Loading）、空（Empty）、错误（Error）状态。
    - [ ] **【新增】** 编写无障碍自动化测试用例（如键盘导航、屏幕阅读器）。
- **验收标准**:
    - 建议列表交互流畅，响应式设计良好，无障碍测试通过。

**Day 5：智能搜索集成测试**
- **目标**: 确保智能搜索模块端到端功能完整、性能达标。
- **任务清单**:
    - [ ] 集成所有智能输入与建议组件，形成完整功能。
    - [ ] 测试搜索性能、准确性、缓存机制、异常处理。
    - [ ] 编写集成测试用例（覆盖率 > 80%）。
- **验收标准**:
    - 智能搜索功能完整，性能指标（响应 < 200ms）达标，集成测试通过。

### Phase 2：增强活动编辑器与智能表单 (Week 2)

**Day 6：活动编辑器架构重构**
- **目标**: 重构活动编辑器，提升其扩展性、可维护性并集成智能功能。
- **任务清单**:
    - [ ] 分析现有 `ActivityEditor` 组件，保留全部现有功能。
    - [ ] 设计新的 `EnhancedActivityEditor` 架构（组件解耦、逻辑分离）。
    - [ ] **【新增】** 设计并引入状态管理方案（Zustand/Context），管理复杂表单状态。
    - [ ] 集成第一周开发的智能输入组件。
    - [ ] 编写重构后的架构测试。
- **验收标准**:
    - 100% 保留现有功能，新架构清晰，状态管理方案合理，测试通过。

**Day 7：智能表单字段开发**
- **目标**: 开发上下文感知、提升输入效率的智能表单字段。
- **任务清单**:
    - [ ] 开发 `SmartTimePicker`, `SmartLocationInput`, `SmartBudgetInput` 组件。
    - [ ] 实现上下文时间建议、地址自动补全、AI 预算估算功能。
    - [ ] **【优化】** 集成表单实时验证与友好的错误反馈。
    - [ ] 编写各智能表单字段的单元测试。
- **验收标准**:
    - 表单字段智能、体验优良、验证完善，测试通过。

**Day 8：活动类型系统优化**
- **目标**: 优化活动类型选择，使其更智能、更具扩展性。
- **任务清单**:
    - [ ] 重新设计活动类型数据结构，支持自定义。
    - [ ] 开发 `ActivityTypeSelector` 组件。
    - [ ] 实现类型切换时，表单字段的智能适配。
    - [ ] 编写类型系统的相关测试。
- **验收标准**:
    - 类型系统扩展性强，切换流畅，测试通过。

**Day 9：表单验证与数据处理**
- **目标**: 保证数据的一致性、完整性，并优化用户操作。
- **任务清单**:
    - [ ] 开发统一的 `ActivityValidation` 工具。
    - [ ] 实现智能化的、依赖于活动类型的表单验证。
    - [ ] **【新增】** 实现表单自动保存（Auto-save）机制，防止用户数据丢失。
    - [ ] 编写验证逻辑和数据处理的测试。
- **验收标准**:
    - 验证逻辑完善，数据处理一致，自动保存功能可靠，测试通过。

**Day 10：活动编辑器集成与 E2E 测试**
- **目标**: 全面测试增强后的活动编辑器，确保流程闭环。
- **任务清单**:
    - [ ] 集成所有编辑器与智能表单组件。
    - [ ] 测试完整的活动创建、编辑、保存、加载流程。
    - [ ] **【新增】** 引入 Playwright/Cypress，编写关键路径的端到端（E2E）测试。
- **验收标准**:
    - 编辑器功能完整，数据流正确，关键路径的 E2E 测试通过。

### Phase 3：AI 辅助系统与时间线优化 (Week 3)

**Day 11：AI 推荐服务开发**
- **目标**: 开发稳定、可靠、按需触发的 AI 推荐服务。
- **任务清单**:
    - [ ] 设计 `AIRecommendationService` 架构。
    - [ ] 实现上下文感知的推荐算法，并集成现有 AI 服务。
    - [ ] 实现 AI 推荐结果的缓存机制。
    - [ ] **【新增】** 设计并实现 AI 服务降级与回退机制（如服务不可用时，返回本地或缓存建议）。
    - [ ] 编写 AI 服务的单元测试。
- **验收标准**:
    - AI 推荐准确、响应快、降级机制可用，服务稳定，测试通过。

**Day 12：AI 建议 UI 组件开发**
- **目标**: 以非侵入、用户可控的方式展示 AI 建议。
- **任务清单**:
    - [ ] 开发 `AIAssistantSuggestions`, `AISuggestionCard` 组件。
    - [ ] 实现按需触发、建议收起/展开、选择与自定义功能。
    - [ ] **【新增】** 预留国际化（i18n）支持，对 UI 文本进行包裹。
    - [ ] 编写 UI 组件的单元测试和交互测试。
- **验收标准**:
    - AI 建议 UI 体验优良，a11y/i18n 支持，测试通过。

**Day 13：智能时间线编辑器**
- **目标**: 增强时间线编辑器的易用性和智能化。
- **任务清单**:
    - [ ] 100% 保留现有 `TimelineEditor` 功能。
    - [ ] 开发 `SmartTimelineView` 组件，优化拖拽体验。
    - [ ] 实现智能排序建议与时间冲突自动检测。
    - [ ] 编写时间线核心功能测试。
- **验收标准**:
    - 时间线编辑器功能增强，智能功能添加，交互流畅，测试通过。

**Day 14：时间线优化算法**
- **目标**: 开发实用的行程优化算法。
- **任务清单**:
    - [ ] 开发 `TimelineOptimizer` 算法。
    - [ ] 实现路线、时间、预算的综合优化建议。
    - [ ] 实现优化建议的应用与一键回退功能。
    - [ ] 编写优化算法的单元测试。
- **验收标准**:
    - 优化算法准确，建议实用，性能达标，测试通过。

**Day 15：Personal Planner 集成测试**
- **目标**: 全面集成所有核心模块，测试完整用户流程。
- **任务清单**:
    - [ ] 集成智能搜索、活动编辑器、AI 辅助、时间线四大模块。
    - [ ] 测试从零开始创建一份完整行程的端到端流程。
    - [ ] 编写覆盖核心规划流程的集成测试。
- **验收标准**:
    - 功能集成完整，用户流程顺畅，数据流无误，集成测试通过。

### Phase 4：优化、发布与用户体验提升 (Week 4)

**Day 16：用户体验与新手引导**
- **目标**: 全面打磨产品体验，降低新用户上手门槛。
- **任务清单**:
    - [ ] 优化全流程的响应速度和动画过渡效果。
    - [ ] 完善全局的错误处理与用户提示。
    - [ ] **【新增】** 设计并实现针对新用户的渐进式引导（Onboarding），如功能高亮、操作提示。
    - [ ] **【新增】** 集成全新的品牌视觉：应用 `welcomepage.png` 作为启动屏，并在应用内（如顶部导航栏、关于页面）全面更换为 `trekmate icon.png` 新 Logo。
    - [ ] 进行小范围用户体验（UAT）测试并收集反馈。
- **验收标准**:
    - 产品体验流畅、友好，引导清晰，UAT 反馈良好。

**Day 17：性能优化与监控**
- **目标**: 量化并提升应用性能，建立线上监控能力。
- **任务清单**:
    - [ ] **【新增】** 对大数据量列表（如推荐）采用虚拟滚动或懒加载。
    - [ ] **【新增】** 集成前端性能监控工具（Sentry Performance / Web Vitals）。
    - [ ] **【新增】** 使用 Lighthouse/PageSpeed 定期进行性能基准测试，量化优化效果。
    - [ ] 进行性能压力测试。
- **验收标准**:
    - 核心性能指标（LCP, FID, CLS）达标，监控系统完善，压力测试通过。

**Day 18：数据一致性和同步**
- **目标**: 确保多端、离线、并发场景下的数据正确性。
- **任务清单**:
    - [ ] 验证数据在不同设备间的保存、加载、离线同步。
    - [ ] 测试数据模型版本兼容性与并发编辑冲突处理。
    - [ ] 完善数据备份与恢复机制。
    - [ ] 进行数据一致性专项测试。
- **验收标准**:
    - 数据一致性、兼容性、同步、并发测试通过。

**Day 19：全面测试与质量保证**
- **目标**: 进行最终质量把关，确保发布版本稳定可靠。
- **任务清单**:
    - [ ] **【新增】** 执行自动化 UI 回归测试（Playwright/Cypress），防止功能回退。
    - [ ] 执行完整的回归、兼容性、安全性测试。
    - [ ] 最终用户验收测试（UAT）。
    - [ ] 完善所有功能的文档与代码注释。
- **验收标准**:
    - 所有测试通过，文档完整，代码质量达标，准备发布。

**Day 20：发布准备与部署**
- **目标**: 安全、顺利地将产品发布上线。
- **任务清单**:
    - [ ] 准备最终发布版本（Release Candidate）。
    - [ ] **【优化】** 配置生产环境的监控、日志与告警系统（如 Sentry）。
    - [ ] 准备并演练回滚方案。
    - [ ] 执行正式发布流程。
- **验收标准**:
    - 发布成功，配置验证通过，监控系统正常工作。

### Phase 5：安全性与数据保护强化

**安全开发原则**
- **目标**: 构建安全可靠的应用，保护用户数据和隐私。
- **任务清单**:
    - [ ] **API 密钥管理**: 使用 `react-native-keychain` 或环境变量安全存储 API 密钥，绝不硬编码。
    - [ ] **依赖安全扫描**: 定期运行 `npm audit` 或 `yarn audit`，及时更新有安全漏洞的依赖。
    - [ ] **数据加密**: 对敏感的本地存储数据进行加密处理。
    - [ ] **网络安全**: 
        - [ ] 实施 HTTPS 强制，验证证书。
        - [ ] 对 API 请求添加适当的认证和授权机制。
        - [ ] 实施请求频率限制，防止滥用。
    - [ ] **隐私合规**: 
        - [ ] 实施用户数据最小化原则。
        - [ ] 添加数据删除和导出功能。
        - [ ] 编写隐私政策和用户协议。
    - [ ] **代码安全审查**: 对关键安全代码进行专项审查。
- **验收标准**:
    - 安全扫描通过，数据保护机制完善，隐私合规要求满足。

### Phase 6：上线后运维与持续迭代

**长期维护与发展**
- **目标**: 建立可持续的产品运营和迭代机制。
- **任务清单**:
    - [ ] **用户反馈体系**: 
        - [ ] 建立应用内反馈渠道（反馈表单、评分系统）。
        - [ ] 监控应用商店评论和社区反馈。
        - [ ] 建立用户反馈分类和优先级处理机制。
    - [ ] **数据分析与监控**: 
        - [ ] 集成用户行为分析工具（如 Firebase Analytics）。
        - [ ] 监控核心业务指标（DAU、功能使用率、转化率）。
        - [ ] 建立性能监控和异常告警机制。
    - [ ] **版本迭代规划**: 
        - [ ] 制定 4.1, 4.2 版本的功能路线图。
        - [ ] 建立功能 A/B 测试框架。
        - [ ] 定期技术债务清理和依赖更新。
    - [ ] **团队知识管理**: 
        - [ ] 建立技术文档库和最佳实践分享。
        - [ ] 定期代码审查和技术分享会议。
        - [ ] 新团队成员的快速上手指南。
- **验收标准**:
    - 反馈渠道畅通，数据监控完善，迭代机制清晰，团队协作高效。

---

## 第二部分：前端专项开发计划

本部分为前端团队提供一份详细的任务指引，与主开发计划并行推进。

1.  **前端架构与基础设施**
    - [ ] 设计前端项目结构（components、hooks、services、types、utils 等）。
    - [ ] 定义全局样式与设计令牌（如 colors.ts, typography.ts）。
    - [ ] 配置 TypeScript、ESLint、Prettier。
    - [ ] 配置 Jest/Testing Library 单元测试。
    - [ ] （可选）配置 Storybook 用于组件文档与可视化开发。
    - [ ] 配置前端部分的 CI/CD 自动化流程。
    - **【新增】** **资产管理**:
        - [ ] 整理并优化项目中的图片资源，创建统一的资产目录。
        - [ ] 集成新的品牌 Logo (`trekmate icon.png`) 和启动屏 (`welcomepage.png`)。

2.  **UI 组件开发与页面搭建**
    - **页面骨架与路由**:
        - [ ] 设计 Personal Planner 单页多模块布局。
        - [ ] 配置页面入口与路由。
    - **核心功能组件**:
        - [ ] `FlightInputSection`：航班信息输入与展示。
        - [ ] `RecommendationSection`：智能推荐展示与选择。
        - [ ] `ItineraryManagerSection`：行程管理、拖拽排序、编辑。
        - [ ] `ToastNotification`：全局操作反馈。
    - **智能交互组件**:
        - [ ] `SmartAttractionInput`, `SmartHotelInput`, `SmartRestaurantInput`。
        - [ ] `SearchSuggestionList`（高亮、键盘/触摸支持）。
        - [ ] `Loading`, `Empty`, `Error` 状态组件。
    - **智能表单与编辑器**:
        - [ ] `SmartTimePicker`, `SmartLocationInput`, `SmartBudgetInput`。
        - [ ] `ActivityTypeSelector`, `ActivityValidation` 工具。
    - **AI 辅助与时间线**:
        - [ ] `AIAssistantSuggestions`, `AISuggestionCard`。
        - [ ] `SmartTimelineView`, `TimelineOptimizer`。

3.  **状态管理与数据流**
    - [ ] 确定并实施全局/局部状态管理方案（Zustand/Context）。
    - [ ] 使用 React Query/SWR 构建 API 服务层，管理数据获取、缓存、同步。
    - [ ] 全面集成 API Mock 服务，支持前端独立开发与测试。

4.  **响应式与无障碍**
    - [ ] 使用 Flexbox 构建响应式布局，适配不同尺寸的移动设备屏幕。
    - [ ] 组件级无障碍支持（`accessibilityLabel`, `accessibilityRole` 等属性）。
    - [ ] 适配移动端、平板，处理平台差异（iOS/Android）。

5.  **性能与体验优化**
    - [ ] 对长列表应用 `FlatList` 优化（`getItemLayout`, `keyExtractor`）或虚拟滚动。
    - [ ] 对所有输入实现防抖/节流。
    - [ ] 优化启动时间、内存使用，减少不必要的重渲染。
    - [ ] 集成前端性能监控（如 Sentry Performance）。

6.  **国际化与新手引导**
    - [ ] 使用 `i18next` 或类似库，为所有面向用户的文本预留 i18n 支持。
    - [ ] 设计并实现新手渐进式引导组件库。
    - **【增强】** **完整的国际化策略**:
        - [ ] 建立多语言资源文件管理体系（中文、英文、日文等）。
        - [ ] 实现动态语言切换功能，支持 RTL（从右到左）语言。
        - [ ] 适配不同地区的日期、时间、货币格式。
        - [ ] 考虑不同语言文本长度对 UI 布局的影响。
        - [ ] 建立翻译质量控制和更新流程。

7.  **前端测试与质量保证**
    - [ ] **单元测试** (Jest/Testing Library): 覆盖所有 `hooks`, `utils`, `services` 和复杂的组件逻辑。
    - [ ] **集成测试**: 测试组件间的交互与数据流动。
    - [ ] **E2E 测试** (Cypress/Playwright): 覆盖核心用户路径。
    - [ ] **自动化 UI 回归测试**: 防止视觉样式破坏。
    - [ ] **性能基准测试** (Lighthouse): 定期检查性能得分。

8.  **文档与交付**
    - [ ] 编写核心组件与 `hooks` 的使用文档。
    - [ ] 维护一份清晰的 API 文档与数据结构说明（与后端协作）。

9.  **发布与运维**
    - [ ] 优化前端构建与打包配置（如 code splitting）。
    - [ ] 配置生产环境的 Source Map 上传与监控。

---

## 第三部分：质量、测试与成功指标

### 风险管理与应急预案

**技术风险识别与缓解**
- **API 依赖风险**: 
    - [ ] 为关键第三方 API（如地图、天气）建立备选方案。
    - [ ] 实施 API 调用的熔断机制和降级策略。
- **性能风险**: 
    - [ ] 建立性能基准测试，定期监控关键指标。
    - [ ] 为大数据量场景准备优化方案（如虚拟滚动、分页加载）。
- **兼容性风险**: 
    - [ ] 建立设备兼容性测试矩阵（不同 iOS/Android 版本）。
    - [ ] 为老设备准备性能优化和功能降级方案。
- **数据安全风险**: 
    - [ ] 建立数据泄露应急响应流程。
    - [ ] 定期进行安全渗透测试。

**项目管理风险**
- **时间风险**: 
    - [ ] 为每个 Phase 预留 10-15% 的缓冲时间。
    - [ ] 识别关键路径上的依赖关系，提前规避阻塞。
- **人力风险**: 
    - [ ] 建立关键知识的文档化和备份机制。
    - [ ] 为核心开发人员建立替代方案。

### 测试策略

采用测试金字塔模型，明确各层职责与工具：

- **E2E Tests (15% - Playwright/Cypress)**: 覆盖核心用户流程，如"创建完整行程"、"AI 辅助规划"。
- **Integration Tests (25% - React Testing Library)**: 覆盖组件间的交互，如"智能搜索与建议列表"、"编辑器与智能表单"。
- **Unit Tests (60% - Jest)**: 覆盖独立的 `hooks`, `utils`, 算法（如 TimelineOptimizer），以及单个组件的逻辑。

### 质量与成功指标

**功能与技术指标**
- **搜索准确率**: > 95%
- **AI 建议采用率**: 30-50% (目标为有效辅助，非强制)
- **核心响应时间**: 搜索 < 200ms, AI 建议 < 2s
- **Web Vitals**: LCP < 2.5s, FID < 100ms, CLS < 0.1
- **测试覆盖率**: 单元 > 85%, 集成 > 75%
- **错误率**: 线上严重错误率 < 0.1%

**用户体验指标**
- **规划完成时间**: 相比旧版减少 40%
- **用户满意度**: > 4.5/5
- **学习成本**: 新用户首次使用即可完成核心规划任务
- **用户留存**: Personal Planner 功能使用频率提升 20% 

**【增强】** **更全面的成功指标体系**

**产品健康度指标**
- **功能采用率**: 
    - [ ] 智能搜索使用率 > 80%
    - [ ] AI 建议查看率 > 60%，采用率 > 30%
    - [ ] 时间线优化功能使用率 > 40%
- **用户参与度**: 
    - [ ] 日活跃用户 (DAU) 增长 > 25%
    - [ ] 用户会话时长增加 > 30%
    - [ ] 功能完成率 > 85%

**技术质量指标**
- **可靠性**: 
    - [ ] 应用崩溃率 < 0.1%
    - [ ] API 成功率 > 99.5%
    - [ ] 数据同步成功率 > 99%
- **可维护性**: 
    - [ ] 代码覆盖率保持 > 85%
    - [ ] 技术债务控制在可接受范围
    - [ ] 新功能开发速度提升 > 20%

**商业价值指标**
- **用户价值**: 
    - [ ] 行程规划成功率 > 90%
    - [ ] 用户推荐净推荐值 (NPS) > 50
    - [ ] 用户支持工单减少 > 30%
- **运营效率**: 
    - [ ] 开发交付周期缩短 > 25%
    - [ ] 生产环境问题解决时间 < 2小时
    - [ ] 团队开发效率提升 > 20%

---

# Trekmate 4.0 - 前端设计与开发指南 (React Native, 2025 风格)

## 1. 摘要

本指南旨在为 Trekmate Personal Planner 的前端开发提供一套清晰、现代的设计原则和技术实施方案。目标是利用 **React Native** 构建一个**简洁、美观、高效且符合2025年设计趋势**的用户界面，不依赖任何第三方组件库。

## 2. 核心设计原则

- **简洁主义 (Minimalism)**: "少即是多"。通过充足的留白、精炼的排版和有意义的动效来突出核心内容，避免任何不必要的装饰。
- **柔和的现代感 (Soft Modernism)**: 采用圆角、柔和的阴影和细腻的渐变，创造一个友好、亲切的数字环境。避免尖锐的边缘和过度的视觉刺激。
- **玻璃拟态点缀 (Glassmorphism Accent)**: 在模态框、浮动面板或顶层通知等关键元素上，策略性地使用模糊半透明背景，以创造深度和层次感。这通常需要借助原生模块。
- **流畅的交互 (Fluid Interactions)**: 所有动效和过渡都应有明确的目的、流畅且迅速。我们将利用 **React Native Reanimated** 来实现优雅的微交互。

## 3. React Native 实施指南

### 3.1. 主题配置 (`styles/theme.ts`)

我们将创建一个中心化的主题文件来定义全局样式，以确保视觉一致性。

**a. 色彩系统 (Colors)**
建议使用一个中性的基础色板，搭配一个充满活力的品牌色。

```typescript
// styles/theme.ts
export const colors = {
  brand: {
    primary: '#319795', // A vibrant teal
    primaryDark: '#2C7A7B',
  },
  background: {
    primary: '#F7FAFC', // Off-white for main background
    secondary: '#FFFFFF',
  },
  ui: {
    border: '#E2E8F0',
    hover: '#EDF2F7', // For touchable feedback
  },
  text: {
    primary: '#2D3748', // Dark gray, not pure black
    secondary: '#718096',
  },
};
```

**b. 字体排版 (Typography)**
选用现代、易读的无衬线字体，如 `Inter`，并定义标准尺寸。

```typescript
// styles/theme.ts (continued)
export const typography = {
  fonts: {
    heading: 'Inter-Bold',
    body: 'Inter-Regular',
  },
  fontSizes: {
    h1: 32,
    h2: 24,
    body: 16,
    caption: 12,
  },
};

export const theme = { colors, typography };
```

### 3.2. 布局策略 (Layout)

使用 React Native 内置的 `Flexbox` 是构建响应式、可维护的页面结构的唯一标准。

```jsx
// layouts/MainLayout.tsx
import React from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { theme } from '../styles/theme';

const MainLayout = ({ children }) => {
  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>{children}</View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});

export default MainLayout;
```

### 3.3. 核心组件示例

**a. 现代卡片 (Modern Card)**
一个自定义的 `Card` 组件，作为信息容器的基础。使用 `StyleSheet` 创建，并考虑了跨平台的阴影效果。

```jsx
// components/Card.tsx
import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { theme } from '../styles/theme';

export const Card = ({ children, style }) => {
  return <View style={[styles.card, style]}>{children}</View>;
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: theme.colors.background.secondary,
    borderRadius: 12,
    padding: 16,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 10,
      },
      android: {
        elevation: 5,
      },
    }),
  },
});
```

**b. 玻璃拟态视图 (Glassmorphism View)**
此效果需要一个第三方库，如 `@react-native-community/blur`。

```jsx
// components/GlassView.tsx
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { BlurView } from '@react-native-community/blur';

export const GlassView = ({ children, style }) => {
  return (
    <View style={[styles.container, style]}>
      <BlurView style={styles.absolute} blurType="light" blurAmount={10} />
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  absolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
});
```

### 3.4. 图标与动效 (Icons & Motion)

**a. 图标 (Icons)**
推荐使用 `react-native-vector-icons`，它是 React Native 生态中最流行的图标库。

```jsx
import Icon from 'react-native-vector-icons/Feather';
import { theme } from '../styles/theme';

// Usage
<Icon name="activity" size={24} color={theme.colors.brand.primary} />
```

**b. 动效 (Motion)**
利用 `react-native-reanimated` 实现列表项的优雅入场动画。使用 `EnteringExitingAnimations` 是现代、高效的方式。

```jsx
import React from 'react';
import { View, FlatList, Text } from 'react-native';
import Reanimated, { FadeInUp, FadeOutDown } from 'react-native-reanimated';
import { Card } from './Card'; // 之前定义的Card组件

function AnimatedList({ items }) {
  const renderItem = ({ item, index }) => (
    <Reanimated.View 
      entering={FadeInUp.delay(index * 100)} 
      exiting={FadeOutDown}
    >
      <Card style={{ marginBottom: 10 }}>
        <Text>{item.content}</Text>
      </Card>
    </Reanimated.View>
  );

  return (
    <FlatList
      data={items}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
    />
  );
}
```

本指南为起点，鼓励团队在此基础上不断探索和创新，共同打造出色的用户体验。

## 第四部分：构建 2025 风格界面的 UI/UX 深度指南

本部分旨在提供一套具体的前端开发策略，以实现前沿、美观且高用户体验的界面。

### 1. 核心工具集 (Minimalist Toolkit)

为实现高级效果，我们不依赖重型 UI 库，而是精选几个轻量、强大的工具：

- **动画**: `react-native-reanimated` (必备，用于实现流畅的 60fps 动画)
- **手势**: `react-native-gesture-handler` (与 Reanimated 结合，实现复杂的触摸交互)
- **图标**: `react-native-vector-icons` (提供丰富的图标选择)
- **触感**: `expo-haptics` (提供细腻的物理震动反馈)
- **渐变**: `expo-linear-gradient` (用于创建柔和的背景渐变)
- **模糊**: `@react-native-community/blur` (实现玻璃拟态效果的关键)

### 2. 设计与实现策略

**a. 柔和的阴影与层次感**

放弃生硬、边缘清晰的 `elevation` (Android) 或 `shadow` (iOS)，追求更柔和、弥散的效果。

*   **技巧**: 在 iOS 上，可以叠加多层 `shadow` 来模拟弥散效果。在 Android 上，虽然 `elevation` 限制较多，但可以通过在背景上放置一张微妙的阴影图片或使用渐变来模拟。

```jsx
// components/SoftShadowCard.tsx
const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    // iOS - 柔和阴影的关键
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.1,
    shadowRadius: 15,
    // Android - 基础 elevation
    elevation: 8,
  }
});
```

**b. 有意义的微交互 (Micro-interactions)**

为用户的每一个操作提供即时、优雅的反馈。

*   **按钮按压**: 使用 `Reanimated` 在按压时，让按钮轻微缩小 (scale: 0.98)，松开时回弹。
*   **列表加载**: 列表项使用 `Reanimated` 的 `FadeInUp` 或 `Layout` 动画，从下方柔和滑入，而不是突然出现。
*   **切换状态**: 图标或状态的改变（如"喜欢"按钮），应伴随一个小的、愉悦的动画（如缩放、旋转）。

```jsx
// components/InteractiveButton.tsx
import Reanimated, { useSharedValue, useAnimatedStyle, withSpring } from 'react-native-reanimated';
import { Pressable } from 'react-native';

const InteractiveButton = ({ children }) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  return (
    <Pressable
      onPressIn={() => (scale.value = withSpring(0.98))}
      onPressOut={() => (scale.value = withSpring(1))}
    >
      <Reanimated.View style={animatedStyle}>
        {children}
      </Reanimated.View>
    </Pressable>
  );
};
```

**c. 触感反馈 (Haptic Feedback)**

在关键交互点加入触感反馈，极大提升"真实感"。

*   **时机**:
    *   当一个列表被拖拽到顶部或底部时。
    *   当一个开关 (Switch) 被切换时。
    *   当一个重要的确认操作完成时。
    *   当一个错误发生时（使用 `impactAsync(Haptics.ImpactFeedbackStyle.Heavy)`）。

```jsx
import * as Haptics from 'expo-haptics';

// 在成功完成某个操作后
Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

// 在轻微的交互发生时，如选择一个列表项
Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
```

**d. 玻璃拟态 (Glassmorphism)**

策略性地在模态框、底部导航栏或浮动按钮上使用，创造高级感。

*   **实现**: 使用 `@react-native-community/blur` 的 `BlurView` 组件作为背景。
*   **要点**: 务必在模糊背景上添加一个半透明的白色或黑色叠加层，并为容器添加一个细微的边框，以增强轮廓感。

```jsx
// components/GlassyModal.tsx
// ...
<BlurView blurType="light" blurAmount={20} style={StyleSheet.absoluteFill} />
<View style={[StyleSheet.absoluteFill, { backgroundColor: 'rgba(255, 255, 255, 0.2)', borderWidth: 1, borderColor: 'rgba(255, 255, 255, 0.3)', borderRadius: 20 }]} />
// ...
```

**e. 排版层次 (Typographic Hierarchy)**

使用统一的排版规范来引导视觉流。

*   **建立规范**: 在 `styles/theme.ts` 中定义一个排版对象，包含字号、字重、行高。
*   **实践**:
    *   **标题 (Heading)**: 使用较粗的字重 (`Inter-Bold`, `700`)。
    *   **正文 (Body)**: 使用常规字重 (`Inter-Regular`, `400`)，并保证足够的行高（约字号的 1.5 倍）以提高可读性。
    *   **辅助文字 (Caption)**: 使用更细的字重 (`Inter-Light`, `300`) 和更小的字号。

### 3. 示例：重构一个"活动卡片"组件

**旧版 (Before):**

```jsx
<View style={styles.card}>
  <Text style={styles.title}>参观卢浮宫</Text>
  <Text style={styles.details}>上午 9:00 - 下午 1:00</Text>
</View>
```

**2025 风格版 (After):**

```jsx
import Reanimated, { FadeIn } from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import Icon from 'react-native-vector-icons/Feather';
import * as Haptics from 'expo-haptics';

// ...
<Pressable onPress={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)}>
  <Reanimated.View entering={FadeIn.duration(500)}>
    <SoftShadowCard>
      <LinearGradient colors={['#E0F7FA', '#FFFFFF']} style={styles.gradientBg}>
        <View style={styles.iconContainer}>
            <Icon name="palette" size={20} color={theme.colors.brand.primary} />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>参观卢浮宫</Text>
          <Text style={styles.details}>上午 9:00 - 下午 1:00</Text>
        </View>
      </LinearGradient>
    </SoftShadowCard>
  </Reanimated.View>
</Pressable>
```
在这个例子中，我们加入了：
1.  **触感反馈**: `Pressable` + `Haptics`。
2.  **入场动画**: `Reanimated.View` 的 `entering` 动画。
3.  **柔和阴影**: 使用了之前定义的 `SoftShadowCard`。
4.  **微妙渐变**: `LinearGradient` 让背景不那么单调。
5.  **清晰排版**: 标题和细节文字使用了不同的样式。
6.  **图标点缀**: 增加了视觉趣味性。

