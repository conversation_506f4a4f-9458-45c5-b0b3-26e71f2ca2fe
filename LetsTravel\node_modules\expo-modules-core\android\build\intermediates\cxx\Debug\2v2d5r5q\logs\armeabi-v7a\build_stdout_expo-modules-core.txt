ninja: Entering directory `C:\AppTest\letstravel3.131\LetsTravel\node_modules\expo-modules-core\android\.cxx\Debug\2v2d5r5q\armeabi-v7a'
[1/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/ObjectDeallocator.cpp.o
[2/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIDeallocator.cpp.o
[3/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/SharedRef.cpp.o
[4/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/TypedArray.cpp.o
[5/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSharedObject.cpp.o
[6/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/SharedObject.cpp.o
[7/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaReferencesCache.cpp.o
[8/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIFunctionBody.cpp.o
[9/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/EventEmitter.cpp.o
[10/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/Exceptions.cpp.o
[11/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptModuleObject.cpp.o
[12/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSReferencesCache.cpp.o
[13/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIInjector.cpp.o
[14/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptFunction.cpp.o
[15/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/ExpoModulesHostObject.cpp.o
[16/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/RuntimeHolder.cpp.o
[17/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/ExpectedType.cpp.o
[18/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JSIContext.cpp.o
[19/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JNIUtils.cpp.o
[20/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptTypedArray.cpp.o
[21/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptRuntime.cpp.o
[22/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptObject.cpp.o
[23/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptValue.cpp.o
[24/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaScriptWeakObject.cpp.o
[25/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/MethodMetadata.cpp.o
[26/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/AnyType.cpp.o
[27/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverterProvider.cpp.o
[28/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/NativeModule.cpp.o
[29/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/WeakRuntimeHolder.cpp.o
[30/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/LazyObject.cpp.o
[31/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSConstantsDecorator.cpp.o
[32/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/FrontendConverter.cpp.o
[33/47] Building CXX object CMakeFiles/expo-modules-core.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/JSIUtils.cpp.o
[34/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSDecoratorsBridgingObject.cpp.o
[35/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/types/JNIToJSIConverter.cpp.o
[36/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSObjectDecorator.cpp.o
[37/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/JavaCallback.cpp.o
[38/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSFunctionsDecorator.cpp.o
[39/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSPropertiesDecorator.cpp.o
[40/47] Building CXX object CMakeFiles/expo-modules-core.dir/src/main/cpp/decorators/JSClassesDecorator.cpp.o
[41/47] Building CXX object src/fabric/CMakeFiles/fabric.dir/a99b86dc36e202fccd96a7c556adf94f/expo-modules-core/common/cpp/fabric/ExpoViewEventEmitter.cpp.o
[42/47] Building CXX object src/fabric/CMakeFiles/fabric.dir/b2f2c17edf305786d5d85748423c3cf1/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewShadowNode.cpp.o
[43/47] Building CXX object src/fabric/CMakeFiles/fabric.dir/FabricComponentsRegistry.cpp.o
[44/47] Building CXX object src/fabric/CMakeFiles/fabric.dir/a99b86dc36e202fccd96a7c556adf94f/expo-modules-core/common/cpp/fabric/ExpoViewComponentDescriptor.cpp.o
[45/47] Building CXX object src/fabric/CMakeFiles/fabric.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/common/cpp/fabric/ExpoViewProps.cpp.o
[46/47] Linking CXX static library src\fabric\libfabric.a
[47/47] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\2v2d5r5q\obj\armeabi-v7a\libexpo-modules-core.so
