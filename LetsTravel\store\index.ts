/**
 * Trekmate 4.0 - 根状态存储
 * 整合所有状态存储，提供统一的状态管理接口
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { AppState } from 'react-native';

import type { UIState } from '../types/Store';
import { defaultStorage } from '../lib/storage/StorageAdapter';

// 导入各个状态存储
import { useUserStore, userSelectors } from './userStore';
import { useJourneyStore, journeySelectors } from './journeyStore';
import { useExploreStore, exploreSelectors } from './exploreStore';
import { useSettingsStore, settingsSelectors } from './settingsStore';

// ============================================================================
// UI状态存储
// ============================================================================

const initialUIState: Omit<UIState, 'actions'> = {
  app: {
    isModified: false,
  },
  navigation: {
    currentRoute: 'Splash',
    previousRoute: null,
    canGoBack: false,
  },
  modals: {
    isLoginModalOpen: false,
    isCreateJourneyModalOpen: false,
    isCreatePostModalOpen: false,
    isSettingsModalOpen: false,
  },
  loading: {
    global: false,
    overlay: false,
    message: null,
  },
  errors: {
    global: null,
    network: false,
    lastError: null,
  },
  success: {
    message: null,
    timestamp: null,
  },
  keyboard: {
    isVisible: false,
    height: 0,
  },
  network: {
    isConnected: true,
    type: null,
  },
};

export const useUIStore = create<UIState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      ...initialUIState,

      actions: {
        setModified: (isModified: boolean) => {
          set(draft => {
            draft.app.isModified = isModified;
          });
        },
        setCurrentRoute: (route: string) => {
          set(draft => {
            draft.navigation.previousRoute = draft.navigation.currentRoute;
            draft.navigation.currentRoute = route;
            draft.navigation.canGoBack = draft.navigation.previousRoute !== null;
          });
        },
        openModal: (modalName: keyof UIState['modals']) => {
          set(draft => {
            draft.modals[modalName] = true;
          });
        },
        closeModal: (modalName: keyof UIState['modals']) => {
          set(draft => {
            draft.modals[modalName] = false;
          });
        },
        closeAllModals: () => {
          set(draft => {
            Object.keys(draft.modals).forEach(key => {
              (draft.modals as any)[key] = false;
            });
          });
        },
        setGlobalLoading: (loading: boolean, message?: string) => {
          set(draft => {
            draft.loading.global = loading;
            draft.loading.message = message || null;
          });
        },
        setOverlayLoading: (loading: boolean) => {
          set(draft => {
            draft.loading.overlay = loading;
          });
        },
        setGlobalError: (error: string | null) => {
          set(draft => {
            draft.errors.global = error;
            if (error) {
              draft.errors.lastError = {
                message: error,
                timestamp: new Date().toISOString(),
              };
            }
          });
        },
        setNetworkError: (hasError: boolean) => {
          set(draft => {
            draft.errors.network = hasError;
          });
        },
        setSuccessMessage: (message: string) => {
          set(draft => {
            draft.success.message = message;
            draft.success.timestamp = new Date().toISOString();
          });

          setTimeout(() => {
            set(draft => {
              draft.success.message = null;
              draft.success.timestamp = null;
            });
          }, 3000);
        },
        clearMessages: () => {
          set(draft => {
            draft.errors.global = null;
            draft.success.message = null;
            draft.success.timestamp = null;
          });
        },
        setKeyboardState: (isVisible: boolean, height: number) => {
          set(draft => {
            draft.keyboard.isVisible = isVisible;
            draft.keyboard.height = height;
          });
        },
        setNetworkState: (isConnected: boolean, type: string | null) => {
          set(draft => {
            draft.network.isConnected = isConnected;
            draft.network.type = type;
          });
        },
      },
    }))
  )
);

// ============================================================================
// 根状态管理器
// ============================================================================

export class RootStoreManager {
  private static instance: RootStoreManager;

  private constructor() {}

  static getInstance(): RootStoreManager {
    if (!RootStoreManager.instance) {
      RootStoreManager.instance = new RootStoreManager();
    }
    return RootStoreManager.instance;
  }

  async initialize(): Promise<void> {
    try {
      console.log('🔄 初始化状态管理...');
      
      // 初始化用户认证状态
      await useUserStore.getState().actions.initializeAuth();
      
      // 加载设置
      await useSettingsStore.getState().actions.loadSettings();
      
      setupAppStateListener();
      console.log('✅ 状态管理初始化完成');
    } catch (error) {
      console.error('❌ 状态管理初始化失败:', error);
      throw error;
    }
  }

  async saveAll(): Promise<void> {
    try {
      await useSettingsStore.getState().actions.saveSettings();
      console.log('✅ 所有状态已保存');
    } catch (error) {
      console.error('❌ 保存状态失败:', error);
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    try {
      await defaultStorage.clear();
      useUserStore.getState().actions.logout();
      useSettingsStore.getState().actions.resetSettings();
      console.log('🗑️ 所有状态已清除');
    } catch (error) {
      console.error('❌ 清除状态失败:', error);
    }
  }

  getHealthStatus() {
    return {
      user: useUserStore.getState(),
      journey: useJourneyStore.getState(),
      explore: useExploreStore.getState(),
      settings: useSettingsStore.getState(),
      ui: useUIStore.getState(),
    };
  }

  async sync(): Promise<void> {
    try {
      console.log('🔄 开始同步...');
      const { syncWithService } = useJourneyStore.getState().actions;
      await syncWithService();
      useSettingsStore.getState().actions.updateSyncTime();
      console.log('✅ 同步完成');
    } catch (error) {
      console.error('❌ 同步失败:', error);
    }
  }
}

export const rootStoreManager = RootStoreManager.getInstance();

let isSaving = false;

export const setupAppStateListener = () => {
  AppState.addEventListener('change', (nextAppState) => {
    if (nextAppState.match(/inactive|background/)) {
      const state = useUIStore.getState();
      if (state.app.isModified && !isSaving) {
        isSaving = true;
        rootStoreManager.saveAll().finally(() => {
          isSaving = false;
        });
        console.log('App is going to background, saving state...');
      }
    }
  });
};

// ============================================================================
// 导出
// ============================================================================

export { useUserStore, useJourneyStore, useExploreStore, useSettingsStore };
export { userSelectors, journeySelectors, exploreSelectors, settingsSelectors };