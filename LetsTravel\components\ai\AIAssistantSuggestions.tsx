/**
 * Trekmate 4.0 - AI助手建议组件
 * 为行程编辑器提供智能建议和优化提示
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { AI_ASSISTANT, COMMON } from '../../constants/Strings';
import { aiPlanService } from '../../services/ai/AIPlanService';
import type { AISuggestion, AIOptimizationRequest } from '../../services/ai/AIPlanService';
import type { Journey, Activity, Location } from '../../types/CoreServices';

// ============================================================================
// AI助手建议Props
// ============================================================================

interface AIAssistantSuggestionsProps {
  journey?: Journey;
  currentActivity?: Activity;
  currentLocation?: Location;
  onSuggestionApply?: (suggestion: AISuggestion) => void;
  onOptimizeJourney?: (optimizedJourney: Journey) => void;
  style?: any;
}

// ============================================================================
// 建议卡片组件
// ============================================================================

interface SuggestionCardProps {
  suggestion: AISuggestion;
  onApply: (suggestion: AISuggestion) => void;
  onDismiss: (suggestionId: string) => void;
}

const SuggestionCard: React.FC<SuggestionCardProps> = ({
  suggestion,
  onApply,
  onDismiss,
}) => {
  const getIconName = (type: string) => {
    const iconMap: Record<string, string> = {
      activity: 'location',
      route: 'map',
      time: 'time',
      budget: 'wallet',
      poi: 'star',
    };
    return iconMap[type] || 'bulb';
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return colors.success;
    if (confidence >= 0.6) return colors.warning;
    return colors.neutral[400];
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return '高度推荐';
    if (confidence >= 0.6) return '建议考虑';
    return '可选建议';
  };

  return (
    <View style={styles.suggestionCard}>
      <View style={styles.suggestionHeader}>
        <View style={styles.suggestionIcon}>
          <Ionicons 
            name={getIconName(suggestion.type) as any} 
            size={20} 
            color={colors.primary[500]} 
          />
        </View>
        
        <View style={styles.suggestionInfo}>
          <Text style={styles.suggestionTitle}>{suggestion.title}</Text>
          <View style={styles.confidenceContainer}>
            <View 
              style={[
                styles.confidenceDot, 
                { backgroundColor: getConfidenceColor(suggestion.confidence) }
              ]} 
            />
            <Text style={styles.confidenceText}>
              {getConfidenceText(suggestion.confidence)}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity 
          style={styles.dismissButton}
          onPress={() => onDismiss(suggestion.id)}
        >
          <Ionicons name="close" size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.suggestionDescription}>
        {suggestion.description}
      </Text>
      
      {suggestion.reasoning && (
        <Text style={styles.suggestionReasoning}>
          💡 {suggestion.reasoning}
        </Text>
      )}
      
      <View style={styles.suggestionActions}>
        <TouchableOpacity 
          style={styles.applyButton}
          onPress={() => onApply(suggestion)}
        >
          <Text style={styles.applyButtonText}>应用建议</Text>
        </TouchableOpacity>
        
        {suggestion.alternatives && suggestion.alternatives.length > 0 && (
          <TouchableOpacity style={styles.alternativesButton}>
            <Text style={styles.alternativesButtonText}>
              查看其他选项 ({suggestion.alternatives.length})
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

// ============================================================================
// AI助手建议组件
// ============================================================================

export default function AIAssistantSuggestions({
  journey,
  currentActivity,
  currentLocation,
  onSuggestionApply,
  onOptimizeJourney,
  style,
}: AIAssistantSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [optimizing, setOptimizing] = useState(false);
  const [dismissedSuggestions, setDismissedSuggestions] = useState<Set<string>>(new Set());

  // 加载AI建议
  const loadSuggestions = useCallback(async () => {
    if (!journey && !currentLocation) return;

    try {
      setLoading(true);
      const allSuggestions: AISuggestion[] = [];

      // 获取活动建议
      if (currentLocation) {
        const activityResponse = await aiPlanService.getActivitySuggestions(currentLocation);
        if (activityResponse.success && activityResponse.data) {
          allSuggestions.push(...activityResponse.data);
        }
      }

      // 获取路线建议
      if (journey && journey.activities.length > 1) {
        const routeResponse = await aiPlanService.getRouteSuggestions(journey.activities);
        if (routeResponse.success && routeResponse.data) {
          allSuggestions.push(...routeResponse.data);
        }
      }

      // 获取时间建议
      if (journey) {
        const timeResponse = await aiPlanService.getTimeSuggestions(journey);
        if (timeResponse.success && timeResponse.data) {
          allSuggestions.push(...timeResponse.data);
        }

        // 获取预算建议
        const budgetResponse = await aiPlanService.getBudgetSuggestions(journey);
        if (budgetResponse.success && budgetResponse.data) {
          allSuggestions.push(...budgetResponse.data);
        }
      }

      // 过滤已忽略的建议
      const filteredSuggestions = allSuggestions.filter(
        suggestion => !dismissedSuggestions.has(suggestion.id)
      );

      // 按置信度排序
      filteredSuggestions.sort((a, b) => b.confidence - a.confidence);

      setSuggestions(filteredSuggestions.slice(0, 5)); // 最多显示5个建议
    } catch (error) {
      console.error('加载AI建议失败:', error);
    } finally {
      setLoading(false);
    }
  }, [journey, currentLocation, dismissedSuggestions]);

  useEffect(() => {
    loadSuggestions();
  }, [loadSuggestions]);

  // 应用建议
  const handleApplySuggestion = useCallback((suggestion: AISuggestion) => {
    onSuggestionApply?.(suggestion);
    
    // 从列表中移除已应用的建议
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
    
    Alert.alert(
      AI_ASSISTANT.SUGGESTION_APPLIED,
      `已应用建议：${suggestion.title}`,
      [{ text: COMMON.OK }]
    );
  }, [onSuggestionApply]);

  // 忽略建议
  const handleDismissSuggestion = useCallback((suggestionId: string) => {
    setDismissedSuggestions(prev => new Set(prev).add(suggestionId));
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
  }, []);

  // 优化整个行程
  const handleOptimizeJourney = useCallback(async () => {
    if (!journey) return;

    try {
      setOptimizing(true);
      
      const request: AIOptimizationRequest = {
        journey,
        preferences: {
          pace: 'moderate',
          budget: 'medium',
        },
      };

      const response = await aiPlanService.optimizeJourney(request);
      
      if (response.success && response.data) {
        onOptimizeJourney?.(response.data.optimizedJourney);
        
        Alert.alert(
          AI_ASSISTANT.OPTIMIZATION_COMPLETE,
          `行程已优化！预计可以提升${Math.round(response.data.improvements.experienceScore * 100)}%的体验质量。`,
          [{ text: COMMON.OK }]
        );
      } else {
        Alert.alert(COMMON.ERROR, response.error || AI_ASSISTANT.OPTIMIZATION_FAILED);
      }
    } catch (error) {
      Alert.alert(COMMON.ERROR, AI_ASSISTANT.NETWORK_ERROR);
    } finally {
      setOptimizing(false);
    }
  }, [journey, onOptimizeJourney]);

  // 渲染头部
  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <Ionicons name="sparkles" size={24} color={colors.primary[500]} />
        <Text style={styles.headerTitle}>{AI_ASSISTANT.SMART_SUGGESTIONS}</Text>
      </View>
      
      {journey && (
        <TouchableOpacity 
          style={[styles.optimizeButton, optimizing && styles.optimizeButtonDisabled]}
          onPress={handleOptimizeJourney}
          disabled={optimizing}
        >
          {optimizing ? (
            <ActivityIndicator size="small" color={colors.surface} />
          ) : (
            <Ionicons name="auto-fix" size={16} color={colors.surface} />
          )}
          <Text style={styles.optimizeButtonText}>
            {optimizing ? AI_ASSISTANT.OPTIMIZING : AI_ASSISTANT.OPTIMIZE_JOURNEY}
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  // 渲染加载状态
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.primary[500]} />
      <Text style={styles.loadingText}>{AI_ASSISTANT.ANALYZING}</Text>
    </View>
  );

  // 渲染空状态
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="checkmark-circle" size={48} color={colors.success} />
      <Text style={styles.emptyTitle}>{AI_ASSISTANT.NO_SUGGESTIONS}</Text>
      <Text style={styles.emptyText}>
        您的行程安排已经很棒了！继续添加活动以获得更多建议。
      </Text>
    </View>
  );

  // 渲染建议列表
  const renderSuggestions = () => (
    <ScrollView 
      style={styles.suggestionsList}
      showsVerticalScrollIndicator={false}
    >
      {suggestions.map((suggestion) => (
        <SuggestionCard
          key={suggestion.id}
          suggestion={suggestion}
          onApply={handleApplySuggestion}
          onDismiss={handleDismissSuggestion}
        />
      ))}
    </ScrollView>
  );

  return (
    <View style={[styles.container, style]}>
      {renderHeader()}
      
      {loading ? (
        renderLoading()
      ) : suggestions.length > 0 ? (
        renderSuggestions()
      ) : (
        renderEmpty()
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    ...shadows.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  optimizeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary[500],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    gap: spacing[1],
  },
  optimizeButtonDisabled: {
    backgroundColor: colors.neutral[300],
  },
  optimizeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.surface,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: spacing[8],
    gap: spacing[3],
  },
  loadingText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: spacing[8],
    paddingHorizontal: spacing[4],
    gap: spacing[3],
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  suggestionsList: {
    maxHeight: 400,
  },
  suggestionCard: {
    padding: spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  suggestionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  suggestionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing[3],
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[1],
  },
  confidenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  confidenceDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  confidenceText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  dismissButton: {
    padding: spacing[1],
  },
  suggestionDescription: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
    marginBottom: spacing[2],
  },
  suggestionReasoning: {
    fontSize: 13,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: spacing[3],
  },
  suggestionActions: {
    flexDirection: 'row',
    gap: spacing[3],
  },
  applyButton: {
    backgroundColor: colors.primary[500],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
  },
  applyButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.surface,
  },
  alternativesButton: {
    borderWidth: 1,
    borderColor: colors.primary[500],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
  },
  alternativesButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary[500],
  },
});
