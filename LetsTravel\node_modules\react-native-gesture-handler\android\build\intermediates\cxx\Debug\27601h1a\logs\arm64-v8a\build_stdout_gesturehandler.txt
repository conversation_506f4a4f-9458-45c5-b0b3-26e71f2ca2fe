ninja: Entering directory `C:\AppTest\letstravel3.131\LetsTravel\node_modules\react-native-gesture-handler\android\.cxx\Debug\27601h1a\arm64-v8a'
[1/2] Building CXX object CMakeFiles/gesturehandler.dir/cpp-adapter.cpp.o
[2/2] Linking CXX shared library C:\AppTest\letstravel3.131\LetsTravel\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\27601h1a\obj\arm64-v8a\libgesturehandler.so
