ninja: Entering directory `C:\AppTest\letstravel3.131\LetsTravel\node_modules\expo-av\android\.cxx\Debug\223m1n4a\armeabi-v7a'
[1/4] Building CXX object CMakeFiles/expo-av.dir/src/main/cpp/EXAV.cpp.o
[2/4] Building CXX object CMakeFiles/expo-av.dir/src/main/cpp/JPlayerData.cpp.o
[3/4] Building CXX object CMakeFiles/expo-av.dir/src/main/cpp/JAVManager.cpp.o
[4/4] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\223m1n4a\obj\armeabi-v7a\libexpo-av.so
