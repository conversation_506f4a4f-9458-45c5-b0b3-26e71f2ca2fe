/**
 * Trekmate 4.0 - 评论面板组件
 * 可重用的评论展示和输入组件，支持回复和嵌套评论
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { COMMUNITY, COMMON } from '../../constants/Strings';
import { communityService } from '../../services/community/CommunityService';
import { useCurrentUser } from '../../hooks/useStore';
import type { CommunityComment } from '../../services/community/CommunityService';

// ============================================================================
// 评论面板Props
// ============================================================================

interface CommentPanelProps {
  postId: string;
  onCommentCountChange?: (count: number) => void;
  style?: any;
}

interface CommentItemProps {
  comment: CommunityComment;
  onReply: (comment: CommunityComment) => void;
  onLike: (commentId: string) => void;
  onEdit?: (comment: CommunityComment) => void;
  onDelete?: (commentId: string) => void;
  level?: number; // 嵌套层级
}

// ============================================================================
// 评论项组件
// ============================================================================

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  onReply,
  onLike,
  onEdit,
  onDelete,
  level = 0,
}) => {
  const user = useCurrentUser();
  const isOwner = user?.id === comment.authorId;
  const maxLevel = 3; // 最大嵌套层级

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    return date.toLocaleDateString('zh-CN');
  };

  const handleMorePress = () => {
    const options = [];
    
    if (isOwner) {
      options.push(
        { text: COMMUNITY.EDIT_COMMENT, onPress: () => onEdit?.(comment) },
        { text: COMMUNITY.DELETE_COMMENT, onPress: () => onDelete?.(comment.id), style: 'destructive' }
      );
    } else {
      options.push({ text: '举报', onPress: () => Alert.alert('提示', '举报功能将在后续版本中实现') });
    }
    
    options.push({ text: COMMON.CANCEL, style: 'cancel' });

    Alert.alert('操作', '选择操作', options);
  };

  return (
    <View style={[
      styles.commentItem,
      level > 0 && styles.replyComment,
      { marginLeft: Math.min(level, maxLevel) * spacing[4] }
    ]}>
      {/* 用户头像和信息 */}
      <View style={styles.commentHeader}>
        <View style={styles.commentUserInfo}>
          <View style={styles.commentAvatar}>
            {comment.authorAvatar ? (
              <Text>👤</Text> // 简化的头像显示
            ) : (
              <Ionicons name="person-circle" size={32} color={colors.neutral[300]} />
            )}
          </View>
          
          <View style={styles.commentUserDetails}>
            <Text style={styles.commentAuthor}>{comment.authorName}</Text>
            <Text style={styles.commentTime}>{formatTime(comment.createdAt)}</Text>
          </View>
        </View>
        
        <TouchableOpacity style={styles.commentMoreButton} onPress={handleMorePress}>
          <Ionicons name="ellipsis-horizontal" size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      {/* 评论内容 */}
      <Text style={styles.commentContent}>{comment.content}</Text>
      
      {/* 操作按钮 */}
      <View style={styles.commentActions}>
        <TouchableOpacity
          style={[styles.commentAction, comment.isLiked && styles.commentActionActive]}
          onPress={() => onLike(comment.id)}
        >
          <Ionicons
            name={comment.isLiked ? "heart" : "heart-outline"}
            size={16}
            color={comment.isLiked ? colors.error : colors.textSecondary}
          />
          {comment.likes > 0 && (
            <Text style={[
              styles.commentActionText,
              comment.isLiked && styles.commentActionTextActive
            ]}>
              {comment.likes}
            </Text>
          )}
        </TouchableOpacity>
        
        {level < maxLevel && (
          <TouchableOpacity
            style={styles.commentAction}
            onPress={() => onReply(comment)}
          >
            <Ionicons name="chatbubble-outline" size={16} color={colors.textSecondary} />
            <Text style={styles.commentActionText}>{COMMUNITY.REPLY_COMMENT}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

// ============================================================================
// 评论面板组件
// ============================================================================

export default function CommentPanel({
  postId,
  onCommentCountChange,
  style,
}: CommentPanelProps) {
  const [comments, setComments] = useState<CommunityComment[]>([]);
  const [commentText, setCommentText] = useState('');
  const [replyingTo, setReplyingTo] = useState<CommunityComment | null>(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const user = useCurrentUser();

  // 加载评论
  const loadComments = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const response = await communityService.getComments(postId);
      
      if (response.success && response.data) {
        setComments(response.data);
        onCommentCountChange?.(response.data.length);
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [postId, onCommentCountChange]);

  useEffect(() => {
    loadComments();
  }, [loadComments]);

  // 发送评论
  const handleSendComment = useCallback(async () => {
    if (!commentText.trim() || !user) {
      if (!user) {
        Alert.alert('提示', '请先登录');
      }
      return;
    }

    try {
      const response = await communityService.createComment(postId, {
        content: commentText.trim(),
        parentCommentId: replyingTo?.id,
      });

      if (response.success && response.data) {
        setComments(prev => [...prev, response.data!]);
        setCommentText('');
        setReplyingTo(null);
        onCommentCountChange?.(comments.length + 1);
        Alert.alert(COMMON.SUCCESS, COMMUNITY.COMMENT_ADDED);
      } else {
        Alert.alert(COMMON.ERROR, response.error || COMMUNITY.COMMENT_ERROR);
      }
    } catch (error) {
      Alert.alert(COMMON.ERROR, COMMUNITY.NETWORK_ERROR);
    }
  }, [commentText, user, postId, replyingTo, comments.length, onCommentCountChange]);

  // 点赞评论
  const handleLikeComment = useCallback(async (commentId: string) => {
    try {
      const comment = comments.find(c => c.id === commentId);
      if (!comment) return;

      if (comment.isLiked) {
        await communityService.unlikeComment(commentId);
      } else {
        await communityService.likeComment(commentId);
      }

      setComments(prev => prev.map(c =>
        c.id === commentId
          ? {
              ...c,
              isLiked: !c.isLiked,
              likes: c.isLiked ? c.likes - 1 : c.likes + 1,
            }
          : c
      ));
    } catch (error) {
      Alert.alert(COMMON.ERROR, '操作失败');
    }
  }, [comments]);

  // 回复评论
  const handleReplyComment = useCallback((comment: CommunityComment) => {
    setReplyingTo(comment);
    setCommentText(`@${comment.authorName} `);
  }, []);

  // 编辑评论
  const handleEditComment = useCallback(async (comment: CommunityComment) => {
    Alert.prompt(
      COMMUNITY.EDIT_COMMENT,
      '修改评论内容',
      [
        { text: COMMON.CANCEL, style: 'cancel' },
        {
          text: COMMON.SAVE,
          onPress: async (newContent) => {
            if (!newContent?.trim()) return;
            
            try {
              const response = await communityService.updateComment(comment.id, newContent.trim());
              
              if (response.success && response.data) {
                setComments(prev => prev.map(c =>
                  c.id === comment.id ? response.data! : c
                ));
                Alert.alert(COMMON.SUCCESS, '评论已更新');
              } else {
                Alert.alert(COMMON.ERROR, response.error || '更新失败');
              }
            } catch (error) {
              Alert.alert(COMMON.ERROR, COMMUNITY.NETWORK_ERROR);
            }
          },
        },
      ],
      'plain-text',
      comment.content
    );
  }, []);

  // 删除评论
  const handleDeleteComment = useCallback(async (commentId: string) => {
    Alert.alert(
      COMMUNITY.DELETE_COMMENT,
      '确定要删除这条评论吗？',
      [
        { text: COMMON.CANCEL, style: 'cancel' },
        {
          text: COMMON.DELETE,
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await communityService.deleteComment(commentId);
              
              if (response.success) {
                setComments(prev => prev.filter(c => c.id !== commentId));
                onCommentCountChange?.(comments.length - 1);
                Alert.alert(COMMON.SUCCESS, '评论已删除');
              } else {
                Alert.alert(COMMON.ERROR, response.error || '删除失败');
              }
            } catch (error) {
              Alert.alert(COMMON.ERROR, COMMUNITY.NETWORK_ERROR);
            }
          },
        },
      ]
    );
  }, [comments.length, onCommentCountChange]);

  // 渲染评论项
  const renderComment = useCallback(({ item }: { item: CommunityComment }) => {
    // 计算嵌套层级
    const level = item.parentCommentId ? 1 : 0;
    
    return (
      <CommentItem
        comment={item}
        onReply={handleReplyComment}
        onLike={handleLikeComment}
        onEdit={handleEditComment}
        onDelete={handleDeleteComment}
        level={level}
      />
    );
  }, [handleReplyComment, handleLikeComment, handleEditComment, handleDeleteComment]);

  // 渲染评论输入框
  const renderCommentInput = () => (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.commentInputContainer}
    >
      {replyingTo && (
        <View style={styles.replyingToContainer}>
          <Text style={styles.replyingToText}>
            回复 @{replyingTo.authorName}
          </Text>
          <TouchableOpacity onPress={() => setReplyingTo(null)}>
            <Ionicons name="close" size={16} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      )}
      
      <View style={styles.commentInputRow}>
        <TextInput
          style={styles.commentInput}
          placeholder={COMMUNITY.COMMENT_PLACEHOLDER}
          placeholderTextColor={colors.textSecondary}
          value={commentText}
          onChangeText={setCommentText}
          multiline
          maxLength={500}
        />
        
        <TouchableOpacity
          style={[
            styles.sendButton,
            !commentText.trim() && styles.sendButtonDisabled,
          ]}
          onPress={handleSendComment}
          disabled={!commentText.trim()}
        >
          <Ionicons
            name="send"
            size={20}
            color={commentText.trim() ? colors.primary[500] : colors.textSecondary}
          />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );

  // 渲染空状态
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="chatbubbles-outline" size={48} color={colors.textSecondary} />
      <Text style={styles.emptyText}>{COMMUNITY.NO_COMMENTS}</Text>
      <Text style={styles.emptySubtext}>成为第一个评论的人</Text>
    </View>
  );

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          评论 ({comments.length})
        </Text>
      </View>
      
      <FlatList
        data={comments}
        renderItem={renderComment}
        keyExtractor={item => item.id}
        ListEmptyComponent={renderEmpty}
        refreshing={refreshing}
        onRefresh={() => loadComments(true)}
        showsVerticalScrollIndicator={false}
        style={styles.commentsList}
      />
      
      {renderCommentInput()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.surface,
  },
  header: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  commentsList: {
    flex: 1,
  },
  commentItem: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  replyComment: {
    backgroundColor: colors.neutral[50],
    borderLeftWidth: 3,
    borderLeftColor: colors.primary[200],
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  commentUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  commentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing[3],
  },
  commentUserDetails: {
    flex: 1,
  },
  commentAuthor: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[1],
  },
  commentTime: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  commentMoreButton: {
    padding: spacing[1],
  },
  commentContent: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.text,
    marginBottom: spacing[2],
  },
  commentActions: {
    flexDirection: 'row',
    gap: spacing[4],
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  commentActionActive: {
    // 激活状态样式
  },
  commentActionText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  commentActionTextActive: {
    color: colors.error,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginTop: spacing[3],
    marginBottom: spacing[1],
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  commentInputContainer: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.surface,
  },
  replyingToContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.primary[50],
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    marginBottom: spacing[2],
  },
  replyingToText: {
    fontSize: 14,
    color: colors.primary[700],
  },
  commentInputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing[3],
  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    fontSize: 16,
    color: colors.text,
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.neutral[100],
  },
});
