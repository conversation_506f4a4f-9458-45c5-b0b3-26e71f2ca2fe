/**
 * Trekmate 4.0 - 帖子详情屏幕
 * 展示单个帖子的详细信息和评论列表
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { COMMUNITY, COMMON } from '../../constants/Strings';
import PostCard from '../../components/community/PostCard';
import { communityService } from '../../services/community/CommunityService';
import { useCurrentUser, useLoading } from '../../hooks/useStore';
import type { PostDetailScreenProps } from '../../types/Navigation';
import type { CommunityPost, CommunityComment } from '../../services/community/CommunityService';

// ============================================================================
// 评论组件
// ============================================================================

interface CommentItemProps {
  comment: CommunityComment;
  onReply?: (comment: CommunityComment) => void;
  onLike?: (commentId: string) => void;
  onEdit?: (comment: CommunityComment) => void;
  onDelete?: (commentId: string) => void;
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  onReply,
  onLike,
  onEdit,
  onDelete,
}) => {
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    return date.toLocaleDateString('zh-CN');
  };

  return (
    <View style={[styles.commentItem, comment.parentCommentId && styles.replyComment]}>
      <View style={styles.commentHeader}>
        <View style={styles.commentUserInfo}>
          <Text style={styles.commentAuthor}>{comment.authorName}</Text>
          <Text style={styles.commentTime}>{formatTime(comment.createdAt)}</Text>
        </View>
        
        <TouchableOpacity style={styles.commentMoreButton}>
          <Ionicons name="ellipsis-horizontal" size={16} color={colors.textSecondary} />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.commentContent}>{comment.content}</Text>
      
      <View style={styles.commentActions}>
        <TouchableOpacity
          style={[styles.commentAction, comment.isLiked && styles.commentActionActive]}
          onPress={() => onLike?.(comment.id)}
        >
          <Ionicons
            name={comment.isLiked ? "heart" : "heart-outline"}
            size={16}
            color={comment.isLiked ? colors.error : colors.textSecondary}
          />
          <Text style={[
            styles.commentActionText,
            comment.isLiked && styles.commentActionTextActive
          ]}>
            {comment.likes > 0 ? comment.likes : ''}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.commentAction}
          onPress={() => onReply?.(comment)}
        >
          <Ionicons name="chatbubble-outline" size={16} color={colors.textSecondary} />
          <Text style={styles.commentActionText}>回复</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// ============================================================================
// 帖子详情屏幕组件
// ============================================================================

export default function PostDetailScreen({ navigation, route }: PostDetailScreenProps) {
  const { postId } = route.params;
  const user = useCurrentUser();
  const { setLoading } = useLoading();

  const [post, setPost] = useState<CommunityPost | null>(null);
  const [comments, setComments] = useState<CommunityComment[]>([]);
  const [commentText, setCommentText] = useState('');
  const [replyingTo, setReplyingTo] = useState<CommunityComment | null>(null);
  const [loadingComments, setLoadingComments] = useState(false);

  // 加载帖子详情
  const loadPost = useCallback(async () => {
    try {
      setLoading(true);
      const response = await communityService.getPost(postId);
      
      if (response.success && response.data) {
        setPost(response.data);
      } else {
        Alert.alert(COMMON.ERROR, response.error || '加载失败');
        navigation.goBack();
      }
    } catch (error) {
      Alert.alert(COMMON.ERROR, COMMUNITY.NETWORK_ERROR);
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  }, [postId, navigation, setLoading]);

  // 加载评论
  const loadComments = useCallback(async () => {
    try {
      setLoadingComments(true);
      const response = await communityService.getComments(postId);
      
      if (response.success && response.data) {
        setComments(response.data);
      }
    } catch (error) {
      console.error('加载评论失败:', error);
    } finally {
      setLoadingComments(false);
    }
  }, [postId]);

  useEffect(() => {
    loadPost();
    loadComments();
  }, [loadPost, loadComments]);

  // 处理发送评论
  const handleSendComment = useCallback(async () => {
    if (!commentText.trim() || !user) return;

    try {
      const response = await communityService.createComment(postId, {
        content: commentText.trim(),
        parentCommentId: replyingTo?.id,
      });

      if (response.success && response.data) {
        setComments(prev => [...prev, response.data!]);
        setCommentText('');
        setReplyingTo(null);
        
        // 更新帖子评论数
        if (post) {
          setPost(prev => prev ? { ...prev, comments: prev.comments + 1 } : null);
        }
      } else {
        Alert.alert(COMMON.ERROR, response.error || COMMUNITY.COMMENT_ERROR);
      }
    } catch (error) {
      Alert.alert(COMMON.ERROR, COMMUNITY.NETWORK_ERROR);
    }
  }, [commentText, user, postId, replyingTo, post]);

  // 处理点赞帖子
  const handleLikePost = useCallback(async (postId: string) => {
    if (!post) return;

    try {
      if (post.isLiked) {
        await communityService.unlikePost(postId);
      } else {
        await communityService.likePost(postId);
      }

      setPost(prev => prev ? {
        ...prev,
        isLiked: !prev.isLiked,
        likes: prev.isLiked ? prev.likes - 1 : prev.likes + 1,
      } : null);
    } catch (error) {
      Alert.alert(COMMON.ERROR, COMMUNITY.LIKE_ERROR);
    }
  }, [post]);

  // 处理收藏帖子
  const handleBookmarkPost = useCallback(async (postId: string) => {
    if (!post) return;

    try {
      if (post.isBookmarked) {
        await communityService.unbookmarkPost(postId);
      } else {
        await communityService.bookmarkPost(postId);
      }

      setPost(prev => prev ? {
        ...prev,
        isBookmarked: !prev.isBookmarked,
      } : null);
    } catch (error) {
      Alert.alert(COMMON.ERROR, '操作失败');
    }
  }, [post]);

  // 处理分享帖子
  const handleSharePost = useCallback(async (post: CommunityPost) => {
    try {
      await communityService.sharePost(post.id);
      setPost(prev => prev ? { ...prev, shares: prev.shares + 1 } : null);
      Alert.alert(COMMON.SUCCESS, '分享成功');
    } catch (error) {
      Alert.alert(COMMON.ERROR, COMMUNITY.SHARE_ERROR);
    }
  }, []);

  // 处理点赞评论
  const handleLikeComment = useCallback(async (commentId: string) => {
    try {
      const comment = comments.find(c => c.id === commentId);
      if (!comment) return;

      if (comment.isLiked) {
        await communityService.unlikeComment(commentId);
      } else {
        await communityService.likeComment(commentId);
      }

      setComments(prev => prev.map(c =>
        c.id === commentId
          ? {
              ...c,
              isLiked: !c.isLiked,
              likes: c.isLiked ? c.likes - 1 : c.likes + 1,
            }
          : c
      ));
    } catch (error) {
      Alert.alert(COMMON.ERROR, '操作失败');
    }
  }, [comments]);

  // 处理回复评论
  const handleReplyComment = useCallback((comment: CommunityComment) => {
    setReplyingTo(comment);
    setCommentText(`@${comment.authorName} `);
  }, []);

  // 渲染头部
  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Ionicons name="arrow-back" size={24} color={colors.text} />
      </TouchableOpacity>
      
      <Text style={styles.headerTitle}>动态详情</Text>
      
      <TouchableOpacity>
        <Ionicons name="share-outline" size={24} color={colors.text} />
      </TouchableOpacity>
    </View>
  );

  // 渲染评论输入框
  const renderCommentInput = () => (
    <View style={styles.commentInputContainer}>
      {replyingTo && (
        <View style={styles.replyingToContainer}>
          <Text style={styles.replyingToText}>
            回复 @{replyingTo.authorName}
          </Text>
          <TouchableOpacity onPress={() => setReplyingTo(null)}>
            <Ionicons name="close" size={16} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>
      )}
      
      <View style={styles.commentInputRow}>
        <TextInput
          style={styles.commentInput}
          placeholder={COMMUNITY.COMMENT_PLACEHOLDER}
          placeholderTextColor={colors.textSecondary}
          value={commentText}
          onChangeText={setCommentText}
          multiline
          maxLength={500}
        />
        
        <TouchableOpacity
          style={[
            styles.sendButton,
            !commentText.trim() && styles.sendButtonDisabled,
          ]}
          onPress={handleSendComment}
          disabled={!commentText.trim()}
        >
          <Ionicons
            name="send"
            size={20}
            color={commentText.trim() ? colors.primary[500] : colors.textSecondary}
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!post) {
    return (
      <SafeAreaView style={styles.container}>
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {renderHeader()}
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <PostCard
            post={post}
            onLike={handleLikePost}
            onBookmark={handleBookmarkPost}
            onShare={handleSharePost}
            onUserPress={(userId) => navigation.navigate('UserProfile', { userId })}
            onLocationPress={(location) => {
              if (location) {
                navigation.navigate('Map', {
                  initialLocation: {
                    latitude: location.latitude,
                    longitude: location.longitude,
                  },
                });
              }
            }}
            onJourneyPress={(journeyId) => navigation.navigate('JourneyDetail', { journeyId })}
          />
          
          {/* 评论区域 */}
          <View style={styles.commentsSection}>
            <Text style={styles.commentsSectionTitle}>
              评论 ({comments.length})
            </Text>
            
            {loadingComments ? (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>加载评论中...</Text>
              </View>
            ) : comments.length > 0 ? (
              comments.map((comment) => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  onReply={handleReplyComment}
                  onLike={handleLikeComment}
                />
              ))
            ) : (
              <View style={styles.noCommentsContainer}>
                <Text style={styles.noCommentsText}>{COMMUNITY.NO_COMMENTS}</Text>
              </View>
            )}
          </View>
        </ScrollView>
        
        {renderCommentInput()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  loadingText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  commentsSection: {
    backgroundColor: colors.surface,
    marginTop: spacing[2],
    paddingHorizontal: spacing[4],
    paddingTop: spacing[4],
  },
  commentsSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[4],
  },
  commentItem: {
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  replyComment: {
    marginLeft: spacing[6],
    paddingLeft: spacing[3],
    borderLeftWidth: 2,
    borderLeftColor: colors.primary[200],
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  commentUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
  commentAuthor: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  commentTime: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  commentMoreButton: {
    padding: spacing[1],
  },
  commentContent: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.text,
    marginBottom: spacing[2],
  },
  commentActions: {
    flexDirection: 'row',
    gap: spacing[4],
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  commentActionActive: {
    // 激活状态样式
  },
  commentActionText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  commentActionTextActive: {
    color: colors.error,
  },
  noCommentsContainer: {
    paddingVertical: spacing[8],
    alignItems: 'center',
  },
  noCommentsText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  commentInputContainer: {
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
  },
  replyingToContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.primary[50],
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    marginBottom: spacing[2],
  },
  replyingToText: {
    fontSize: 14,
    color: colors.primary[700],
  },
  commentInputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: spacing[3],
  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    fontSize: 16,
    color: colors.text,
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.neutral[100],
  },
});
