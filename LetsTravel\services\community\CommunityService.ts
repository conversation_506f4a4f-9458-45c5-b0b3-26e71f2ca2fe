/**
 * Trekmate 4.0 - 社区服务
 * 处理社区内容的发布、获取、互动等功能
 */

import { defaultStorage } from '../storage/StorageService';
import type { ServiceResponse } from '../../types/CoreServices';

// ============================================================================
// 社区数据类型定义
// ============================================================================

export interface CommunityPost {
  id: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  images?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
    poiId?: string;
  };
  journeyId?: string; // 关联的行程ID
  activityId?: string; // 关联的活动ID
  tags: string[];
  likes: number;
  comments: number;
  shares: number;
  isLiked: boolean;
  isBookmarked: boolean;
  visibility: 'public' | 'friends' | 'private';
  createdAt: string;
  updatedAt: string;
}

export interface CommunityComment {
  id: string;
  postId: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  content: string;
  parentCommentId?: string; // 用于回复评论
  likes: number;
  isLiked: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CommunityUser {
  id: string;
  username: string;
  displayName: string;
  avatar?: string;
  bio?: string;
  location?: string;
  followersCount: number;
  followingCount: number;
  postsCount: number;
  isFollowing: boolean;
  isFollower: boolean;
  joinedAt: string;
}

export interface PostCreateData {
  content: string;
  images?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address: string;
    poiId?: string;
  };
  journeyId?: string;
  activityId?: string;
  tags: string[];
  visibility: 'public' | 'friends' | 'private';
}

export interface PostUpdateData {
  content?: string;
  tags?: string[];
  visibility?: 'public' | 'friends' | 'private';
}

export interface CommentCreateData {
  content: string;
  parentCommentId?: string;
}

export interface FeedFilter {
  type?: 'all' | 'following' | 'nearby' | 'trending';
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // 米
  };
  tags?: string[];
  userId?: string;
  limit?: number;
  offset?: number;
}

// ============================================================================
// 社区服务接口
// ============================================================================

export interface CommunityService {
  // 帖子管理
  createPost(data: PostCreateData): Promise<ServiceResponse<CommunityPost>>;
  updatePost(postId: string, data: PostUpdateData): Promise<ServiceResponse<CommunityPost>>;
  deletePost(postId: string): Promise<ServiceResponse<void>>;
  getPost(postId: string): Promise<ServiceResponse<CommunityPost>>;
  getFeed(filter?: FeedFilter): Promise<ServiceResponse<CommunityPost[]>>;
  getUserPosts(userId: string, limit?: number, offset?: number): Promise<ServiceResponse<CommunityPost[]>>;
  
  // 互动功能
  likePost(postId: string): Promise<ServiceResponse<void>>;
  unlikePost(postId: string): Promise<ServiceResponse<void>>;
  bookmarkPost(postId: string): Promise<ServiceResponse<void>>;
  unbookmarkPost(postId: string): Promise<ServiceResponse<void>>;
  sharePost(postId: string): Promise<ServiceResponse<void>>;
  
  // 评论管理
  createComment(postId: string, data: CommentCreateData): Promise<ServiceResponse<CommunityComment>>;
  updateComment(commentId: string, content: string): Promise<ServiceResponse<CommunityComment>>;
  deleteComment(commentId: string): Promise<ServiceResponse<void>>;
  getComments(postId: string, limit?: number, offset?: number): Promise<ServiceResponse<CommunityComment[]>>;
  likeComment(commentId: string): Promise<ServiceResponse<void>>;
  unlikeComment(commentId: string): Promise<ServiceResponse<void>>;
  
  // 用户关系
  followUser(userId: string): Promise<ServiceResponse<void>>;
  unfollowUser(userId: string): Promise<ServiceResponse<void>>;
  getFollowers(userId: string, limit?: number, offset?: number): Promise<ServiceResponse<CommunityUser[]>>;
  getFollowing(userId: string, limit?: number, offset?: number): Promise<ServiceResponse<CommunityUser[]>>;
  
  // 搜索功能
  searchPosts(query: string, filter?: FeedFilter): Promise<ServiceResponse<CommunityPost[]>>;
  searchUsers(query: string, limit?: number): Promise<ServiceResponse<CommunityUser[]>>;
  
  // 内容审核
  reportPost(postId: string, reason: string): Promise<ServiceResponse<void>>;
  reportComment(commentId: string, reason: string): Promise<ServiceResponse<void>>;
  reportUser(userId: string, reason: string): Promise<ServiceResponse<void>>;
}

// ============================================================================
// 社区服务实现
// ============================================================================

class CommunityServiceImpl implements CommunityService {
  private readonly CACHE_PREFIX = 'community_cache_';
  private readonly CACHE_EXPIRY = 5 * 60 * 1000; // 5分钟

  // Mock 数据
  private mockPosts: CommunityPost[] = [
    {
      id: 'post_1',
      authorId: 'user_1',
      authorName: '旅行达人小王',
      authorAvatar: 'https://example.com/avatar1.jpg',
      content: '刚刚在吉隆坡双子塔拍的照片，夜景真的太美了！推荐大家晚上来看，灯光效果特别棒。',
      images: [
        'https://example.com/post1_1.jpg',
        'https://example.com/post1_2.jpg',
      ],
      location: {
        latitude: 3.1579,
        longitude: 101.7116,
        address: '双子塔, 吉隆坡',
        poiId: 'poi_1',
      },
      tags: ['吉隆坡', '双子塔', '夜景', '摄影'],
      likes: 128,
      comments: 23,
      shares: 15,
      isLiked: false,
      isBookmarked: false,
      visibility: 'public',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'post_2',
      authorId: 'user_2',
      authorName: '美食探索者',
      authorAvatar: 'https://example.com/avatar2.jpg',
      content: '在茨厂街发现了一家超棒的肉骨茶店！汤头浓郁，肉质鲜嫩，价格也很实惠。强烈推荐给来吉隆坡的朋友们！',
      images: ['https://example.com/post2_1.jpg'],
      location: {
        latitude: 3.1478,
        longitude: 101.6953,
        address: '茨厂街, 吉隆坡',
      },
      tags: ['美食', '肉骨茶', '茨厂街', '推荐'],
      likes: 89,
      comments: 31,
      shares: 8,
      isLiked: true,
      isBookmarked: true,
      visibility: 'public',
      createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'post_3',
      authorId: 'user_3',
      authorName: '背包客阿明',
      authorAvatar: 'https://example.com/avatar3.jpg',
      content: '分享一下我的3天吉隆坡行程安排，包含了所有必去景点和美食推荐。希望对大家有帮助！',
      journeyId: 'journey_1',
      tags: ['行程分享', '吉隆坡', '攻略', '3天'],
      likes: 156,
      comments: 42,
      shares: 28,
      isLiked: false,
      isBookmarked: false,
      visibility: 'public',
      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
    },
  ];

  private mockComments: CommunityComment[] = [
    {
      id: 'comment_1',
      postId: 'post_1',
      authorId: 'user_4',
      authorName: '摄影爱好者',
      content: '照片拍得真好！请问用的什么相机？',
      likes: 5,
      isLiked: false,
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'comment_2',
      postId: 'post_1',
      authorId: 'user_1',
      authorName: '旅行达人小王',
      content: '谢谢！用的iPhone 15 Pro Max，主要是时机选得好。',
      parentCommentId: 'comment_1',
      likes: 2,
      isLiked: false,
      createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    },
  ];

  // ========================================================================
  // 帖子管理
  // ========================================================================

  async createPost(data: PostCreateData): Promise<ServiceResponse<CommunityPost>> {
    try {
      const newPost: CommunityPost = {
        id: `post_${Date.now()}`,
        authorId: 'current_user', // 实际应用中从认证状态获取
        authorName: '当前用户',
        content: data.content,
        images: data.images,
        location: data.location,
        journeyId: data.journeyId,
        activityId: data.activityId,
        tags: data.tags,
        likes: 0,
        comments: 0,
        shares: 0,
        isLiked: false,
        isBookmarked: false,
        visibility: data.visibility,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      this.mockPosts.unshift(newPost);
      await this.updateCache();

      return {
        success: true,
        data: newPost,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建帖子失败',
      };
    }
  }

  async updatePost(postId: string, data: PostUpdateData): Promise<ServiceResponse<CommunityPost>> {
    try {
      const postIndex = this.mockPosts.findIndex(p => p.id === postId);
      if (postIndex === -1) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      const updatedPost = {
        ...this.mockPosts[postIndex],
        ...data,
        updatedAt: new Date().toISOString(),
      };

      this.mockPosts[postIndex] = updatedPost;
      await this.updateCache();

      return {
        success: true,
        data: updatedPost,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新帖子失败',
      };
    }
  }

  async deletePost(postId: string): Promise<ServiceResponse<void>> {
    try {
      const postIndex = this.mockPosts.findIndex(p => p.id === postId);
      if (postIndex === -1) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      this.mockPosts.splice(postIndex, 1);
      await this.updateCache();

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除帖子失败',
      };
    }
  }

  async getPost(postId: string): Promise<ServiceResponse<CommunityPost>> {
    try {
      const post = this.mockPosts.find(p => p.id === postId);
      if (!post) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      return {
        success: true,
        data: post,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取帖子失败',
      };
    }
  }

  async getFeed(filter?: FeedFilter): Promise<ServiceResponse<CommunityPost[]>> {
    try {
      await this.loadCache();

      let filteredPosts = [...this.mockPosts];

      // 应用过滤器
      if (filter) {
        if (filter.type === 'following') {
          // 模拟关注用户的帖子
          filteredPosts = filteredPosts.filter(post => 
            ['user_1', 'user_2'].includes(post.authorId)
          );
        }

        if (filter.type === 'nearby' && filter.location) {
          // 模拟附近的帖子
          filteredPosts = filteredPosts.filter(post => {
            if (!post.location) return false;
            // 简化的距离计算
            const distance = Math.sqrt(
              Math.pow(post.location.latitude - filter.location!.latitude, 2) +
              Math.pow(post.location.longitude - filter.location!.longitude, 2)
            ) * 111000; // 粗略转换为米
            return distance <= filter.location!.radius;
          });
        }

        if (filter.tags && filter.tags.length > 0) {
          filteredPosts = filteredPosts.filter(post =>
            filter.tags!.some(tag => post.tags.includes(tag))
          );
        }

        if (filter.userId) {
          filteredPosts = filteredPosts.filter(post => post.authorId === filter.userId);
        }
      }

      // 分页
      const limit = filter?.limit || 20;
      const offset = filter?.offset || 0;
      const paginatedPosts = filteredPosts.slice(offset, offset + limit);

      return {
        success: true,
        data: paginatedPosts,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取动态失败',
      };
    }
  }

  async getUserPosts(userId: string, limit = 20, offset = 0): Promise<ServiceResponse<CommunityPost[]>> {
    return this.getFeed({ userId, limit, offset });
  }

  // ========================================================================
  // 互动功能
  // ========================================================================

  async likePost(postId: string): Promise<ServiceResponse<void>> {
    try {
      const post = this.mockPosts.find(p => p.id === postId);
      if (!post) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      if (!post.isLiked) {
        post.likes++;
        post.isLiked = true;
        await this.updateCache();
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '点赞失败',
      };
    }
  }

  async unlikePost(postId: string): Promise<ServiceResponse<void>> {
    try {
      const post = this.mockPosts.find(p => p.id === postId);
      if (!post) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      if (post.isLiked) {
        post.likes--;
        post.isLiked = false;
        await this.updateCache();
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '取消点赞失败',
      };
    }
  }

  async bookmarkPost(postId: string): Promise<ServiceResponse<void>> {
    try {
      const post = this.mockPosts.find(p => p.id === postId);
      if (!post) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      post.isBookmarked = true;
      await this.updateCache();

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '收藏失败',
      };
    }
  }

  async unbookmarkPost(postId: string): Promise<ServiceResponse<void>> {
    try {
      const post = this.mockPosts.find(p => p.id === postId);
      if (!post) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      post.isBookmarked = false;
      await this.updateCache();

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '取消收藏失败',
      };
    }
  }

  async sharePost(postId: string): Promise<ServiceResponse<void>> {
    try {
      const post = this.mockPosts.find(p => p.id === postId);
      if (!post) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      post.shares++;
      await this.updateCache();

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '分享失败',
      };
    }
  }

  // ========================================================================
  // 评论管理
  // ========================================================================

  async createComment(postId: string, data: CommentCreateData): Promise<ServiceResponse<CommunityComment>> {
    try {
      const post = this.mockPosts.find(p => p.id === postId);
      if (!post) {
        return {
          success: false,
          error: '帖子不存在',
        };
      }

      const newComment: CommunityComment = {
        id: `comment_${Date.now()}`,
        postId,
        authorId: 'current_user',
        authorName: '当前用户',
        content: data.content,
        parentCommentId: data.parentCommentId,
        likes: 0,
        isLiked: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      this.mockComments.push(newComment);
      post.comments++;
      await this.updateCache();

      return {
        success: true,
        data: newComment,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建评论失败',
      };
    }
  }

  async updateComment(commentId: string, content: string): Promise<ServiceResponse<CommunityComment>> {
    try {
      const commentIndex = this.mockComments.findIndex(c => c.id === commentId);
      if (commentIndex === -1) {
        return {
          success: false,
          error: '评论不存在',
        };
      }

      const updatedComment = {
        ...this.mockComments[commentIndex],
        content,
        updatedAt: new Date().toISOString(),
      };

      this.mockComments[commentIndex] = updatedComment;

      return {
        success: true,
        data: updatedComment,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新评论失败',
      };
    }
  }

  async deleteComment(commentId: string): Promise<ServiceResponse<void>> {
    try {
      const commentIndex = this.mockComments.findIndex(c => c.id === commentId);
      if (commentIndex === -1) {
        return {
          success: false,
          error: '评论不存在',
        };
      }

      const comment = this.mockComments[commentIndex];
      const post = this.mockPosts.find(p => p.id === comment.postId);
      
      this.mockComments.splice(commentIndex, 1);
      if (post) {
        post.comments--;
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除评论失败',
      };
    }
  }

  async getComments(postId: string, limit = 20, offset = 0): Promise<ServiceResponse<CommunityComment[]>> {
    try {
      const comments = this.mockComments
        .filter(c => c.postId === postId)
        .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
        .slice(offset, offset + limit);

      return {
        success: true,
        data: comments,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取评论失败',
      };
    }
  }

  async likeComment(commentId: string): Promise<ServiceResponse<void>> {
    try {
      const comment = this.mockComments.find(c => c.id === commentId);
      if (!comment) {
        return {
          success: false,
          error: '评论不存在',
        };
      }

      if (!comment.isLiked) {
        comment.likes++;
        comment.isLiked = true;
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '点赞评论失败',
      };
    }
  }

  async unlikeComment(commentId: string): Promise<ServiceResponse<void>> {
    try {
      const comment = this.mockComments.find(c => c.id === commentId);
      if (!comment) {
        return {
          success: false,
          error: '评论不存在',
        };
      }

      if (comment.isLiked) {
        comment.likes--;
        comment.isLiked = false;
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '取消点赞评论失败',
      };
    }
  }

  // ========================================================================
  // 用户关系 (简化实现)
  // ========================================================================

  async followUser(userId: string): Promise<ServiceResponse<void>> {
    return { success: true };
  }

  async unfollowUser(userId: string): Promise<ServiceResponse<void>> {
    return { success: true };
  }

  async getFollowers(userId: string, limit = 20, offset = 0): Promise<ServiceResponse<CommunityUser[]>> {
    return { success: true, data: [] };
  }

  async getFollowing(userId: string, limit = 20, offset = 0): Promise<ServiceResponse<CommunityUser[]>> {
    return { success: true, data: [] };
  }

  // ========================================================================
  // 搜索功能 (简化实现)
  // ========================================================================

  async searchPosts(query: string, filter?: FeedFilter): Promise<ServiceResponse<CommunityPost[]>> {
    try {
      const results = this.mockPosts.filter(post =>
        post.content.toLowerCase().includes(query.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );

      return {
        success: true,
        data: results,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '搜索失败',
      };
    }
  }

  async searchUsers(query: string, limit = 20): Promise<ServiceResponse<CommunityUser[]>> {
    return { success: true, data: [] };
  }

  // ========================================================================
  // 内容审核 (简化实现)
  // ========================================================================

  async reportPost(postId: string, reason: string): Promise<ServiceResponse<void>> {
    return { success: true };
  }

  async reportComment(commentId: string, reason: string): Promise<ServiceResponse<void>> {
    return { success: true };
  }

  async reportUser(userId: string, reason: string): Promise<ServiceResponse<void>> {
    return { success: true };
  }

  // ========================================================================
  // 缓存管理
  // ========================================================================

  private async loadCache(): Promise<void> {
    try {
      const cached = await defaultStorage.getObject<{
        posts: CommunityPost[];
        comments: CommunityComment[];
        timestamp: number;
      }>(`${this.CACHE_PREFIX}data`);

      if (cached && Date.now() - cached.timestamp < this.CACHE_EXPIRY) {
        this.mockPosts = cached.posts;
        this.mockComments = cached.comments;
      }
    } catch (error) {
      console.warn('加载社区缓存失败:', error);
    }
  }

  private async updateCache(): Promise<void> {
    try {
      await defaultStorage.setObject(`${this.CACHE_PREFIX}data`, {
        posts: this.mockPosts,
        comments: this.mockComments,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.warn('更新社区缓存失败:', error);
    }
  }
}

// ============================================================================
// 导出服务实例
// ============================================================================

export const communityService = new CommunityServiceImpl();
