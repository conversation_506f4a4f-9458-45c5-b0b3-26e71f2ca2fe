{"buildFiles": ["C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\.cxx\\Debug\\6k1h1t2m\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\.cxx\\Debug\\6k1h1t2m\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\.cxx\\Debug\\6k1h1t2m\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\.cxx\\Debug\\6k1h1t2m\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"expo-gl::@6890427a1f51a3e7e1df": {"artifactName": "expo-gl", "abi": "armeabi-v7a", "output": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\build\\intermediates\\cxx\\Debug\\6k1h1t2m\\obj\\armeabi-v7a\\libexpo-gl.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so"]}}}