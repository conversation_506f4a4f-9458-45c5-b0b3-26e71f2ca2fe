[{"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-av/android/.cxx/Debug/223m1n4a/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_av_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon/callinvoker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -fstack-protector-all -fno-limit-debug-info  -fPIC -std=gnu++20 -o CMakeFiles\\expo-av.dir\\src\\main\\cpp\\EXAV.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-av\\android\\src\\main\\cpp\\EXAV.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-av\\android\\src\\main\\cpp\\EXAV.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-av/android/.cxx/Debug/223m1n4a/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_av_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon/callinvoker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -fstack-protector-all -fno-limit-debug-info  -fPIC -std=gnu++20 -o CMakeFiles\\expo-av.dir\\src\\main\\cpp\\JPlayerData.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-av\\android\\src\\main\\cpp\\JPlayerData.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-av\\android\\src\\main\\cpp\\JPlayerData.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-av/android/.cxx/Debug/223m1n4a/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_av_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon/callinvoker -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -fstack-protector-all -fno-limit-debug-info  -fPIC -std=gnu++20 -o CMakeFiles\\expo-av.dir\\src\\main\\cpp\\JAVManager.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-av\\android\\src\\main\\cpp\\JAVManager.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-av\\android\\src\\main\\cpp\\JAVManager.cpp"}]