{"buildFiles": ["C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\571h524k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\571h524k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\571h524k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\571h524k\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\571h524k\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\571h524k\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"artifactName": "rnscreens", "abi": "armeabi-v7a", "output": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\571h524k\\obj\\armeabi-v7a\\librnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1957b0db02baa50e26af4d5b92e700f6\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}}}