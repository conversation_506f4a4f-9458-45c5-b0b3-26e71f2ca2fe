[{"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/.cxx/Debug/571h524k/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/.cxx/Debug/571h524k/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/.cxx/Debug/571h524k/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\jni-adapter.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/.cxx/Debug/571h524k/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\NativeProxy.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/.cxx/Debug/571h524k/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\OnLoad.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp"}]