/**
 * Trekmate 4.0 - 社区功能集成测试
 * 测试完整的社区功能流程，包括发布、互动、搜索等
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';

import CommunityScreen from '../../screens/tabs/CommunityScreen';
import CreatePostScreen from '../../screens/community/CreatePostScreen';
import PostDetailScreen from '../../screens/community/PostDetailScreen';
import CommunitySearchScreen from '../../screens/community/CommunitySearchScreen';
import CommunityFeed from '../../components/community/CommunityFeed';
import PostCard from '../../components/community/PostCard';
import CommentPanel from '../../components/community/CommentPanel';
import { communityService } from '../../services/community/CommunityService';

// Mock 导航
const mockNavigate = jest.fn();
const mockGoBack = jest.fn();

const mockNavigation = {
  navigate: mockNavigate,
  goBack: mockGoBack,
  setOptions: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  isFocused: jest.fn(() => true),
};

const mockRoute = {
  params: {},
  key: 'test-key',
  name: 'Community',
};

// Mock 社区服务
jest.mock('../../services/community/CommunityService');

describe('社区功能集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock 社区服务响应
    (communityService.getFeed as jest.Mock).mockResolvedValue({
      success: true,
      data: [
        {
          id: 'post_1',
          authorId: 'user_1',
          authorName: '旅行达人小王',
          content: '刚刚在吉隆坡双子塔拍的照片，夜景真的太美了！',
          images: ['https://example.com/image1.jpg'],
          location: {
            latitude: 3.1579,
            longitude: 101.7116,
            address: '双子塔, 吉隆坡',
          },
          tags: ['吉隆坡', '双子塔', '夜景'],
          likes: 128,
          comments: 23,
          shares: 15,
          isLiked: false,
          isBookmarked: false,
          visibility: 'public',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    });

    (communityService.createPost as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        id: 'post_new',
        authorId: 'current_user',
        authorName: '当前用户',
        content: '测试帖子内容',
        tags: ['测试'],
        likes: 0,
        comments: 0,
        shares: 0,
        isLiked: false,
        isBookmarked: false,
        visibility: 'public',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    });

    (communityService.getPost as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        id: 'post_1',
        authorId: 'user_1',
        authorName: '旅行达人小王',
        content: '刚刚在吉隆坡双子塔拍的照片，夜景真的太美了！',
        images: ['https://example.com/image1.jpg'],
        location: {
          latitude: 3.1579,
          longitude: 101.7116,
          address: '双子塔, 吉隆坡',
        },
        tags: ['吉隆坡', '双子塔', '夜景'],
        likes: 128,
        comments: 23,
        shares: 15,
        isLiked: false,
        isBookmarked: false,
        visibility: 'public',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    });

    (communityService.getComments as jest.Mock).mockResolvedValue({
      success: true,
      data: [
        {
          id: 'comment_1',
          postId: 'post_1',
          authorId: 'user_2',
          authorName: '摄影爱好者',
          content: '照片拍得真好！请问用的什么相机？',
          likes: 5,
          isLiked: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    });

    (communityService.likePost as jest.Mock).mockResolvedValue({ success: true });
    (communityService.bookmarkPost as jest.Mock).mockResolvedValue({ success: true });
    (communityService.sharePost as jest.Mock).mockResolvedValue({ success: true });
    (communityService.createComment as jest.Mock).mockResolvedValue({
      success: true,
      data: {
        id: 'comment_new',
        postId: 'post_1',
        authorId: 'current_user',
        authorName: '当前用户',
        content: '测试评论',
        likes: 0,
        isLiked: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    });

    (communityService.searchPosts as jest.Mock).mockResolvedValue({
      success: true,
      data: [
        {
          id: 'post_search',
          authorId: 'user_1',
          authorName: '旅行达人',
          content: '搜索结果帖子',
          tags: ['搜索', '测试'],
          likes: 10,
          comments: 2,
          shares: 1,
          isLiked: false,
          isBookmarked: false,
          visibility: 'public',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
    });
  });

  const renderWithNavigation = (component: React.ReactElement) => {
    return render(
      <NavigationContainer>
        {component}
      </NavigationContainer>
    );
  };

  describe('社区动态流', () => {
    it('应该正确加载和显示社区动态', async () => {
      renderWithNavigation(
        <CommunityScreen navigation={mockNavigation as any} route={mockRoute as any} />
      );

      // 验证页面标题
      expect(screen.getByText('社区')).toBeTruthy();

      // 验证过滤器选项
      expect(screen.getByText('全部')).toBeTruthy();
      expect(screen.getByText('关注')).toBeTruthy();
      expect(screen.getByText('附近')).toBeTruthy();
      expect(screen.getByText('热门')).toBeTruthy();

      // 验证动态加载
      await waitFor(() => {
        expect(communityService.getFeed).toHaveBeenCalled();
      });

      // 验证帖子内容显示
      await waitFor(() => {
        expect(screen.getByText('旅行达人小王')).toBeTruthy();
        expect(screen.getByText('刚刚在吉隆坡双子塔拍的照片，夜景真的太美了！')).toBeTruthy();
      });
    });

    it('应该支持过滤器切换', async () => {
      renderWithNavigation(
        <CommunityScreen navigation={mockNavigation as any} route={mockRoute as any} />
      );

      // 点击关注过滤器
      fireEvent.press(screen.getByText('关注'));

      // 验证过滤器状态更新
      await waitFor(() => {
        expect(communityService.getFeed).toHaveBeenCalledWith(
          expect.objectContaining({ type: 'following' })
        );
      });

      // 点击附近过滤器
      fireEvent.press(screen.getByText('附近'));

      await waitFor(() => {
        expect(communityService.getFeed).toHaveBeenCalledWith(
          expect.objectContaining({ 
            type: 'nearby',
            location: expect.any(Object)
          })
        );
      });
    });
  });

  describe('帖子互动功能', () => {
    it('应该支持点赞功能', async () => {
      const mockPost = {
        id: 'post_1',
        authorId: 'user_1',
        authorName: '测试用户',
        content: '测试帖子',
        tags: [],
        likes: 10,
        comments: 5,
        shares: 2,
        isLiked: false,
        isBookmarked: false,
        visibility: 'public' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      render(
        <PostCard
          post={mockPost}
          onLike={jest.fn()}
          onComment={jest.fn()}
          onShare={jest.fn()}
          onBookmark={jest.fn()}
        />
      );

      // 查找并点击点赞按钮
      const likeButton = screen.getByText('10');
      fireEvent.press(likeButton.parent);

      // 验证点赞状态更新
      expect(screen.getByText('10')).toBeTruthy();
    });

    it('应该支持评论功能', async () => {
      renderWithNavigation(
        <PostDetailScreen 
          navigation={mockNavigation as any} 
          route={{ ...mockRoute, params: { postId: 'post_1' } } as any} 
        />
      );

      // 验证帖子详情加载
      await waitFor(() => {
        expect(communityService.getPost).toHaveBeenCalledWith('post_1');
        expect(communityService.getComments).toHaveBeenCalledWith('post_1');
      });

      // 验证评论显示
      await waitFor(() => {
        expect(screen.getByText('摄影爱好者')).toBeTruthy();
        expect(screen.getByText('照片拍得真好！请问用的什么相机？')).toBeTruthy();
      });

      // 测试添加评论
      const commentInput = screen.getByPlaceholderText('写下你的评论...');
      fireEvent.changeText(commentInput, '这是一条测试评论');

      const sendButton = screen.getByTestId('send-button') || 
                        screen.getAllByRole('button').find(btn => 
                          btn.props.children?.props?.name === 'send'
                        );
      
      if (sendButton) {
        fireEvent.press(sendButton);

        await waitFor(() => {
          expect(communityService.createComment).toHaveBeenCalledWith('post_1', {
            content: '这是一条测试评论',
            parentCommentId: undefined,
          });
        });
      }
    });
  });

  describe('帖子发布功能', () => {
    it('应该能够创建新帖子', async () => {
      renderWithNavigation(
        <CreatePostScreen 
          navigation={mockNavigation as any} 
          route={mockRoute as any} 
        />
      );

      // 验证页面标题
      expect(screen.getByText('发布动态')).toBeTruthy();

      // 填写帖子内容
      const contentInput = screen.getByPlaceholderText('分享你的旅行见闻...');
      fireEvent.changeText(contentInput, '这是一条测试帖子');

      // 添加标签
      const tagInput = screen.getByPlaceholderText('输入标签...');
      fireEvent.changeText(tagInput, '测试');
      
      const addTagButton = screen.getAllByRole('button').find(btn => 
        btn.props.children?.props?.name === 'add'
      );
      if (addTagButton) {
        fireEvent.press(addTagButton);
      }

      // 发布帖子
      const publishButton = screen.getByText('发布');
      fireEvent.press(publishButton);

      // 验证发布API调用
      await waitFor(() => {
        expect(communityService.createPost).toHaveBeenCalledWith(
          expect.objectContaining({
            content: '这是一条测试帖子',
            tags: ['测试'],
            visibility: 'public',
          })
        );
      });

      // 验证导航返回
      expect(mockGoBack).toHaveBeenCalled();
    });

    it('应该验证必填字段', async () => {
      renderWithNavigation(
        <CreatePostScreen 
          navigation={mockNavigation as any} 
          route={mockRoute as any} 
        />
      );

      // 尝试发布空内容
      const publishButton = screen.getByText('发布');
      
      // 发布按钮应该是禁用状态
      expect(publishButton.props.disabled).toBe(true);

      // 验证没有调用发布API
      expect(communityService.createPost).not.toHaveBeenCalled();
    });
  });

  describe('搜索功能', () => {
    it('应该支持帖子搜索', async () => {
      renderWithNavigation(
        <CommunitySearchScreen navigation={mockNavigation as any} route={mockRoute as any} />
      );

      // 验证搜索界面
      expect(screen.getByPlaceholderText('搜索帖子、用户或话题...')).toBeTruthy();

      // 验证搜索标签页
      expect(screen.getByText('帖子')).toBeTruthy();
      expect(screen.getByText('用户')).toBeTruthy();
      expect(screen.getByText('话题')).toBeTruthy();

      // 执行搜索
      const searchInput = screen.getByPlaceholderText('搜索帖子、用户或话题...');
      fireEvent.changeText(searchInput, '测试搜索');

      // 验证搜索API调用
      await waitFor(() => {
        expect(communityService.searchPosts).toHaveBeenCalledWith('测试搜索');
      }, { timeout: 1000 });

      // 验证搜索结果显示
      await waitFor(() => {
        expect(screen.getByText('搜索结果帖子')).toBeTruthy();
      });
    });

    it('应该显示热门标签', async () => {
      renderWithNavigation(
        <CommunitySearchScreen navigation={mockNavigation as any} route={mockRoute as any} />
      );

      // 验证热门标签显示
      expect(screen.getByText('热门话题')).toBeTruthy();
      expect(screen.getByText('#吉隆坡')).toBeTruthy();
      expect(screen.getByText('#美食')).toBeTruthy();

      // 点击热门标签
      fireEvent.press(screen.getByText('#吉隆坡'));

      // 验证搜索输入更新
      const searchInput = screen.getByPlaceholderText('搜索帖子、用户或话题...');
      expect(searchInput.props.value).toBe('吉隆坡');
    });
  });

  describe('评论面板功能', () => {
    it('应该正确显示和管理评论', async () => {
      render(
        <CommentPanel
          postId="post_1"
          onCommentCountChange={jest.fn()}
        />
      );

      // 验证评论加载
      await waitFor(() => {
        expect(communityService.getComments).toHaveBeenCalledWith('post_1');
      });

      // 验证评论显示
      await waitFor(() => {
        expect(screen.getByText('摄影爱好者')).toBeTruthy();
        expect(screen.getByText('照片拍得真好！请问用的什么相机？')).toBeTruthy();
      });

      // 测试评论输入
      const commentInput = screen.getByPlaceholderText('写下你的评论...');
      fireEvent.changeText(commentInput, '测试评论');

      const sendButton = screen.getAllByRole('button').find(btn => 
        btn.props.children?.props?.name === 'send'
      );
      
      if (sendButton) {
        fireEvent.press(sendButton);

        await waitFor(() => {
          expect(communityService.createComment).toHaveBeenCalledWith('post_1', {
            content: '测试评论',
            parentCommentId: undefined,
          });
        });
      }
    });
  });

  describe('错误处理', () => {
    it('应该正确处理网络错误', async () => {
      // Mock 网络错误
      (communityService.getFeed as jest.Mock).mockRejectedValue(
        new Error('网络连接失败')
      );

      renderWithNavigation(
        <CommunityScreen navigation={mockNavigation as any} route={mockRoute as any} />
      );

      // 验证错误处理
      await waitFor(() => {
        expect(communityService.getFeed).toHaveBeenCalled();
      });

      // 可以添加错误状态的UI验证
    });

    it('应该正确处理API错误响应', async () => {
      // Mock API错误响应
      (communityService.createPost as jest.Mock).mockResolvedValue({
        success: false,
        error: '内容不能为空',
      });

      renderWithNavigation(
        <CreatePostScreen 
          navigation={mockNavigation as any} 
          route={mockRoute as any} 
        />
      );

      // 填写内容并尝试发布
      const contentInput = screen.getByPlaceholderText('分享你的旅行见闻...');
      fireEvent.changeText(contentInput, '测试内容');

      const publishButton = screen.getByText('发布');
      fireEvent.press(publishButton);

      // 验证错误处理
      await waitFor(() => {
        expect(communityService.createPost).toHaveBeenCalled();
      });

      // 验证没有导航返回
      expect(mockGoBack).not.toHaveBeenCalled();
    });
  });
});
