import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Platform,
  View,
  Text,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Animated,
  Image,
  KeyboardAvoidingView,
  Modal,
  TouchableWithoutFeedback,
  Alert,
  Dimensions,
  ActivityIndicator,
  Keyboard,
  FlatList,
  Linking // <-- Import Linking
} from 'react-native';

// 修复类型导入 - 使用React.RefObject
import type { ScrollView as ScrollViewComponent } from 'react-native';
import { useNavigation } from '@react-navigation/native';
// 修复导入错误：useFocusEffect 应该从 @react-navigation/native 导入
// import { useFocusEffect } from '@react-navigation/native';

// 创建一个模拟的 useFocusEffect hook
const useFocusEffect = (callback: () => void) => {
  useEffect(() => {
    callback();
  }, []);
};
import { Ionicons } from '@expo/vector-icons';

// 修复 expo-av 和 expo-location 导入
import * as Location from 'expo-location';
import Markdown from 'react-native-markdown-display'; // <-- Import Markdown

// 使用类型断言来避免TypeScript错误，保持所有功能完整
const ExpoAV = require('expo-av') as any;
const Audio = ExpoAV.Audio;
const InterruptionModeAndroid = ExpoAV.InterruptionModeAndroid || { DuckOthers: 0, DoNotMix: 1 };
const InterruptionModeIOS = ExpoAV.InterruptionModeIOS || { MixWithOthers: 0, DuckOthers: 1, DoNotMix: 2 };

// 从环境变量获取API密钥，提供硬编码作为fallback
const API_KEY = process.env.EXPO_PUBLIC_GOOGLE_API_KEY || 'AIzaSyCOZCtQqRQ1YiRyjeY_AiNdhWohfGeP804';
const OPENWEATHER_API_KEY = process.env.EXPO_PUBLIC_OPENWEATHER_API_KEY || '********************************';
const OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY || '********************************************************************************************************************************************************************';

// 修复 ImagePicker 类型导入
interface ImagePickerAsset {
  uri: string;
  width: number;
  height: number;
  type?: 'image' | 'video';
  fileName?: string;
  fileSize?: number;
}

// 尝试导入图片选择器模块
let ImagePicker: any = null;
try {
  ImagePicker = require('expo-image-picker');
} catch (error) {
  console.warn('Warning: expo-image-picker could not be loaded', error);
}

// 尝试导入FileSystem模块
let FileSystem: any = null;
try {
  FileSystem = require('expo-file-system');
} catch (error) {
  console.warn('Warning: expo-file-system could not be loaded', error);
}

// 定义消息类型
interface Message {
  id: number;
  text: string;
  sender: 'user' | 'gemini';
  timestamp: number;
  imageUri?: string;
}

// --- 新增：地理距离计算和分级辅助函数 ---
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // 地球半径 (km)
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a =
    0.5 - Math.cos(dLat) / 2 +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    (1 - Math.cos(dLon)) / 2;
  return R * 2 * Math.asin(Math.sqrt(a)); // 返回公里数
}

function getDistanceCategory(distanceKm: number): string {
  if (distanceKm <= 3) { // <-- 修改阈值为 3
    return `步行距离 (约 ${distanceKm.toFixed(1)} km)`;
  } else if (distanceKm <= 15) { // <-- 修改阈值为 15
    return `短途车程 (约 ${distanceKm.toFixed(1)} km)`;
  } else {
    return `稍远距离 (约 ${distanceKm.toFixed(1)} km)`;
  }
}
// --- 辅助函数结束 ---

// --- 新增：直接搜索附近地点的函数 ---
const searchNearbyPlacesAPI = async (
  query: string, // 查询关键词，例如 "food", "attractions"
  location: Location.LocationObject,
  apiKey: string,
  radius: number = 3000, // 默认搜索半径 3 公里
  addLogFunc?: (message: string) => void
): Promise<any[] | null> => {
  const log = addLogFunc || console.log;
  const lat = location.coords.latitude;
  const lng = location.coords.longitude;

  log(`Places API (NearbySearch): Searching for '${query}' near ${lat},${lng} within ${radius}m radius`);

  // 使用 nearbySearch API
  // 注意：keyword 参数在 nearbySearch 中效果可能不如指定 type 好，但更通用
  // 也可以考虑根据 query 映射到 Places API 的 type (e.g., 'food' -> 'restaurant')
  const requestUrl = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat}%2C${lng}&radius=${radius}&keyword=${encodeURIComponent(query)}&key=${apiKey}`;
  log(`Places API (NearbySearch): Request URL: ${requestUrl.substring(0, 200)}...`);

  try {
    const response = await fetch(requestUrl);
    const data = await response.json();

    if (data.status === 'OK') {
      log(`Places API (NearbySearch): Found ${data.results.length} results for '${query}'.`);
      // 返回结果数组，包含 place_id, name, geometry, types 等
      return data.results;
    } else if (data.status === 'ZERO_RESULTS') {
      log(`Places API (NearbySearch): No results found for '${query}' near the location.`);
      return []; // 返回空数组表示未找到
    } else {
      log(`Places API (NearbySearch): Error - Status: ${data.status}, ErrorMessage: ${data.error_message || 'N/A'}`);
      return null; // 返回 null 表示查询出错
    }
  } catch (error) {
    log(`Places API (NearbySearch): Network or fetch error: ${error}`);
    return null;
  }
};
// --- 辅助函数结束 ---

// --- Search Nearby Places using Places API (New) - Hybrid Type/Text Search --- ADD targetLocationName
const searchNearbyPlacesAPI_New = async (
  keyword: string, // Keyword from AI intent or text input
  userLocation: Location.LocationObject, // User's current location
  apiKey: string,
  radius: number = 3000, 
  addLogFunc?: (message: string) => void,
  maxResults: number = 10, 
  targetLocationName?: string // <-- NEW: Optional parameter for specific location search
): Promise<any[] | null> => {
  const log = addLogFunc || console.log;
  const lat = userLocation?.coords?.latitude; // Handle potentially null userLocation
  const lng = userLocation?.coords?.longitude;

  let endpoint = '';
  let requestBody: any = {};
  let searchMode = 'nearby'; // Default to nearby
  let searchTermForLog = keyword;

  // --- Determine Search Mode based on targetLocationName --- 
  if (targetLocationName && targetLocationName.trim().length > 0) {
    // **Specific Location Search using Text Search**
    searchMode = 'text_specific_location';
    endpoint = 'https://places.googleapis.com/v1/places:searchText';
    searchTermForLog = `${keyword} in ${targetLocationName}`; // Combine keyword and location for search
    log(`Places API (New - ${searchMode.toUpperCase()}): Searching for "${searchTermForLog}".`);
    
    requestBody = {
      textQuery: searchTermForLog,
      maxResultCount: maxResults,
      languageCode: "zh-CN", // Request Chinese names
      // No locationRestriction or locationBias needed for specific city search
    };

  } else {
    // **Nearby Search (Original Logic - slightly adapted)**
    searchMode = 'nearby';
    const placeType = mapKeywordToPlaceType(keyword);
    
    if (placeType && lat !== undefined && lng !== undefined) {
       // Nearby Search using Type
       searchMode = 'nearby_type';
       endpoint = 'https://places.googleapis.com/v1/places:searchNearby';
       searchTermForLog = placeType;
       log(`Places API (New - ${searchMode.toUpperCase()}): Mapped keyword "${keyword}" to type '${placeType}'. Searching nearby ${lat},${lng}.`);
       requestBody = {
         includedPrimaryTypes: [placeType],
         locationRestriction: {
           circle: {
             center: { latitude: lat, longitude: lng },
             radius: radius
           }
         },
         maxResultCount: maxResults,
         languageCode: "zh-CN",
       };
    } else if (keyword && keyword.trim().length > 0 && keyword !== 'general search' && lat !== undefined && lng !== undefined) {
       // Nearby Search using Text (if type mapping failed but keyword exists)
       searchMode = 'nearby_text';
       endpoint = 'https://places.googleapis.com/v1/places:searchText'; // Use Text Search but with location bias
       searchTermForLog = keyword;
       log(`Places API (New - ${searchMode.toUpperCase()}): Keyword "${keyword}" not mapped to type. Using Text Search biased near ${lat},${lng}.`);
       requestBody = {
         textQuery: keyword,
         locationBias: { // Use locationBias to influence results towards the user's area
           circle: {
             center: { latitude: lat, longitude: lng },
             radius: radius
           }
         },
         maxResultCount: maxResults,
         languageCode: "zh-CN",
       };
    } else {
       log(`Places API (New - Nearby): Skipping call due to invalid keyword or missing location: Keyword="${keyword}", Location=${lat},${lng}`);
       return []; // Return empty if no valid search can be performed
    }
  } // --- End of Search Mode Determination ---

  if (!endpoint) {
      log(`Places API (New): ERROR - Endpoint not set for search mode: ${searchMode}`);
      return null; // Indicate an error
  }

  log(`Places API (New - ${searchMode.toUpperCase()}): Preparing request for '${searchTermForLog}'...`);

  const headers = {
    'Content-Type': 'application/json',
    'X-Goog-Api-Key': apiKey,
    'X-Goog-FieldMask': 'places.id,places.displayName' 
  };

  try {
    log(`Places API (New - ${searchMode.toUpperCase()}): Sending request to ${endpoint}...`);
    const response = await fetch(endpoint, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(requestBody)
    });
    const data = await response.json();

    if (response.ok && data.places) {
      log(`Places API (New - ${searchMode.toUpperCase()}): Found ${data.places.length} results for '${searchTermForLog}'.`);
      const mappedResults = data.places.map((place: any) => ({
        place_id: place.id,
        name: place.displayName?.text || '未知地点' // Use display name
      }));
      return mappedResults;
    } else if (response.ok && !data.places) {
        log(`Places API (New - ${searchMode.toUpperCase()}): No results found (empty places array) for '${searchTermForLog}'.`);
        return []; // Return empty array for no results
    } else {
      const errorDetails = data.error ? JSON.stringify(data.error) : await response.text();
      log(`Places API (New - ${searchMode.toUpperCase()}): Error ${response.status} during search for '${searchTermForLog}' - ${errorDetails}`);
      return null; // Return null on API error status
    }
  } catch (error) {
    log(`Places API (New - ${searchMode.toUpperCase()}): Network/fetch error during search for '${searchTermForLog}': ${error}`);
    return null; // Return null on network/fetch error
  }
};

// --- Helper Function for Keyword Extraction ---
const extractPlacesKeyword = (query: string): string => {
  if (!query) return '';
  
  // --- NEW: Check for weather-related keywords first ---
  const weatherKeywords = [
    'weather', 'forecast', 'temperature', 'rain', 'snow', 'sunny', 'cloudy', 'storm', 'humidity', 'climate',
    '天气', '预报', '气温', '下雨', '下雪', '晴天', '多云', '阴天', '风暴', '湿度', '气候'
  ];
  
  const lowerQuery = query.toLowerCase();
  for (const keyword of weatherKeywords) {
    if (lowerQuery.includes(keyword)) {
      console.log(`Keyword Extraction: Detected weather-related keyword "${keyword}" in "${query}", returning 'weather'`);
      return 'weather'; // Special keyword indicating this is a weather query
    }
  }
  
  // Define mappings (simple example, can be expanded)
  const keywordMappings: { [key: string]: string[] } = {
    'restaurant': ['food', 'restaurant', 'eat', 'makan', '餐', '吃', '饭店', '馆'],
    'attraction': ['attraction', 'sightseeing', 'tourist', '景点', '玩', '逛'],
    'hotel': ['hotel', 'accommodation', 'inn', 'motel', '住', '酒店', '旅馆'],
    'cafe': ['cafe', 'coffee', '咖啡'],
    'bar': ['bar', 'pub', '酒'],
    'park': ['park', 'garden', '公园'],
    'museum': ['museum', 'gallery', '博物馆', '美术馆'],
    // Specific Cuisines (add more as needed)
    'indian restaurant': ['indian', '印度'],
    'chinese restaurant': ['chinese', '中餐'],
    'malay restaurant': ['malay', '马来'],
    'thai restaurant': ['thai', '泰国'],
    'japanese restaurant': ['japanese', '日本', '寿司', '拉面'],
    'italian restaurant': ['italian', '意大利'],
    // Generic fallback for location-related words
    'point_of_interest': ['nearby', 'around me', 'local', '附近', '身边', '这里']
  };

  // Find the first matching keyword category
  for (const apiKeyWord in keywordMappings) {
    if (keywordMappings[apiKeyWord].some(userWord => lowerQuery.includes(userWord))) {
      // Found a match, return the API-friendly keyword
      console.log(`Keyword Extraction: Input "${query}" mapped to "${apiKeyWord}"`);
      return apiKeyWord;
    }
  }

  // If no specific category matches, clean up the original query a bit
  const cleanedQuery = query.replace(/nearby|around me|附近|身边|local|推荐|更多|什么|好的|any|more|recommend|some|good|please|find/gi, '').trim();
  console.log(`Keyword Extraction: No specific category found for "${query}", using cleaned query: "${cleanedQuery || query}"`);
  // Return the cleaned query, or the original if cleaning resulted in empty string
  return cleanedQuery || query; 
};

// --- API Function: Speech-to-Text --- 
// ... (speechToText function remains the same) ...

// --- Basic Weather Interfaces (Moved BEFORE helper functions) ---
interface CurrentWeatherData {
  weather: { id: number; main: string; description: string; icon: string }[];
  main: { temp: number; feels_like: number; temp_min: number; temp_max: number; pressure: number; humidity: number };
  wind: { speed: number; deg: number };
  clouds: { all: number };
  dt: number;
  sys: { country: string; sunrise: number; sunset: number };
  timezone: number;
  id: number;
  name: string; // City name
  cod: number;
}

interface ForecastData {
  cod: string;
  message: number;
  cnt: number; // Number of timestamps returned
  list: {
    dt: number;
    main: { temp: number; feels_like: number; temp_min: number; temp_max: number; pressure: number; sea_level: number; grnd_level: number; humidity: number; temp_kf: number };
    weather: { id: number; main: string; description: string; icon: string }[];
    clouds: { all: number };
    wind: { speed: number; deg: number; gust: number };
    visibility: number;
    pop: number; // Probability of precipitation
    sys: { pod: string };
    dt_txt: string;
  }[];
  city: {
    id: number;
    name: string;
    coord: { lat: number; lon: number };
    country: string;
    population: number;
    timezone: number;
    sunrise: number;
    sunset: number;
  };
}

// --- NEW: OpenWeatherMap API Helper Functions ---
const callOpenWeatherAPI = async (url: string, apiName: string, addLogFunc?: (message: string) => void): Promise<any | null> => {
  const log = addLogFunc || console.log;
  log(`OpenWeather (${apiName}): Calling API - ${url.replace(OPENWEATHER_API_KEY, '****')}`); // Mask API key in log
  try {
    const response = await fetch(url);
    const data = await response.json();
    
    // Check for OpenWeather specific errors (cod != 200)
    if (data.cod && data.cod !== 200 && data.cod !== '200') { // API uses number or string for cod
        log(`OpenWeather (${apiName}): API Error - Code ${data.cod}, Message: ${data.message}`);
        return null;
    }
    
    log(`OpenWeather (${apiName}): API call successful.`);
    return data;
  } catch (error) {
    log(`OpenWeather (${apiName}): Network or fetch error - ${error}`);
    return null;
  }
};

const getCurrentWeatherData = async (
  location: { lat: number; lon: number } | { cityName: string }, 
  apiKey: string,
  addLogFunc?: (message: string) => void
): Promise<CurrentWeatherData | null> => {
  const log = addLogFunc || console.log;
  let url = '';
  const units = 'metric'; // Use Celsius
  const lang = 'zh_cn'; // Request Chinese description if available

  if ('lat' in location) {
    url = `https://api.openweathermap.org/data/2.5/weather?lat=${location.lat}&lon=${location.lon}&appid=${apiKey}&units=${units}&lang=${lang}`;
  } else if (location.cityName) {
    url = `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(location.cityName)}&appid=${apiKey}&units=${units}&lang=${lang}`;
  } else {
    log('getCurrentWeatherData: Invalid location provided.');
    return null;
  }

  return callOpenWeatherAPI(url, 'CurrentWeather', addLogFunc);
};

const getForecastData = async (
  location: { lat: number; lon: number } | { cityName: string }, 
  apiKey: string,
  addLogFunc?: (message: string) => void
): Promise<ForecastData | null> => {
  const log = addLogFunc || console.log;
  let url = '';
  const units = 'metric'; // Use Celsius
  const lang = 'zh_cn'; // Request Chinese description if available
  // const cnt = 16; // Optional: Limit number of 3-hour forecasts (e.g., 16 = 2 days)

  if ('lat' in location) {
    url = `https://api.openweathermap.org/data/2.5/forecast?lat=${location.lat}&lon=${location.lon}&appid=${apiKey}&units=${units}&lang=${lang}`;
  } else if (location.cityName) {
    url = `https://api.openweathermap.org/data/2.5/forecast?q=${encodeURIComponent(location.cityName)}&appid=${apiKey}&units=${units}&lang=${lang}`;
  } else {
    log('getForecastData: Invalid location provided.');
    return null;
  }

  return callOpenWeatherAPI(url, 'Forecast', addLogFunc);
};

// --- NEW: Bad Weather Check Function ---
const checkBadWeather = (weatherData: CurrentWeatherData | null): string | null => {
  if (!weatherData || !weatherData.weather || weatherData.weather.length === 0) {
    return null;
  }

  const condition = weatherData.weather[0];
  const code = condition.id;
  const main = condition.main.toLowerCase();
  const temp = weatherData.main.temp;
  // const windSpeed = weatherData.wind.speed; // Optional: Add wind check

  let warning: string | null = null;

  // Prioritize significant conditions first
  if (code >= 200 && code < 300) warning = "注意，有雷暴！"; // Thunderstorm
  else if (code >= 600 && code < 700) warning = "提醒一下，外面在下雪。"; // Snow
  else if (code >= 500 && code < 600) warning = "注意，正在下雨。"; // Rain
  else if (code >= 300 && code < 400) warning = "有点毛毛雨。"; // Drizzle
  else if (code === 781 || code === 900) warning = "警告：有龙卷风风险！"; // Tornado
  else if (code === 741 || code === 701) warning = "注意，有雾。"; // Fog/Mist
  else if (code >= 700 && code < 800) warning = "注意，天气状况不佳 (如沙尘、火山灰等)。"; // Atmosphere general
  else if (temp <= 0) warning = "天气寒冷，注意保暖！"; // Cold Temperature
  // else if (temp >= 35) warning = "天气炎热，小心中暑！"; // Hot Temperature (Optional)
  // else if (windSpeed > 15) warning = "风力较强，请注意！"; // Strong Wind (Optional, adjust threshold)

  return warning;
};

// --- NEW: AI Weather Formatter Function ---
const formatWeatherResponseWithAI = async (
  weatherData: CurrentWeatherData | ForecastData | null,
  forecastType: 'current' | 'forecast' | 'today' | 'tomorrow' | string, 
  locationName: string, 
  targetLanguageCode: string,
  addLogFunc?: (message: string) => void
): Promise<string> => {
  const log = addLogFunc || console.log;
  const fallbackResponse = "抱歉，我现在无法获取或解读天气信息。";

  if (!weatherData) {
    log('formatWeatherResponseWithAI: No weather data provided.');
    return fallbackResponse;
  }

  log(`formatWeatherResponseWithAI: Formatting ${forecastType} weather for ${locationName} in ${targetLanguageCode}`);
  const weatherJsonString = JSON.stringify(weatherData);
  // Simple check for excessively large JSON to avoid huge prompts
  if (weatherJsonString.length > 15000) { 
      log('formatWeatherResponseWithAI: Weather data too large, returning fallback.');
      // Potentially try to summarize forecast data before sending?
      return fallbackResponse;
  }

  let queryTypeDescription = '当前';
  if (forecastType === 'forecast' || forecastType === 'tomorrow' || forecastType === 'today') {
      queryTypeDescription = '接下来' // Simple description for prompt
  }

  const prompt = `你是一位友好的旅行助手。
请根据以下 OpenWeatherMap API 返回的 JSON 数据，为用户总结一下 "${locationName}" ${queryTypeDescription}的天气情况。
用自然、对话式的语气，就像和朋友聊天一样。优先提及体感温度、是否下雨/下雪、风力等关键信息。
如果数据是预报 (forecast)，请重点总结接下来一两天的情况。
请仅用语言代码 "${targetLanguageCode}" 回复。

天气数据 (JSON): 
${weatherJsonString}

总结回复:`;

  try {
    const requestBody = {
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: { 
        temperature: 0.7, // Slightly higher temp for more natural language
        maxOutputTokens: 500, 
      }
    };

    log('formatWeatherResponseWithAI: Sending request to Gemini...');
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash-lite:generateContent?key=${API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      log(`formatWeatherResponseWithAI: API Error ${response.status} - ${errorText}`);
      return fallbackResponse;
    }

    const data = await response.json();
    log(`formatWeatherResponseWithAI: Response received.`);

    if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
      const formattedText = data.candidates[0].content.parts[0].text;
      log(`formatWeatherResponseWithAI: Formatted text: ${formattedText.substring(0, 100)}...`);
      return formattedText;
    } else {
      log('formatWeatherResponseWithAI: Could not extract text from API response.');
      return fallbackResponse;
    }

  } catch (error) {
    log(`formatWeatherResponseWithAI: Network or fetch error: ${error}`);
    return fallbackResponse;
  }
};

// --- NEW: AI Function for Intent Recognition & Entity Extraction (Using compatible model) ---
interface ExtractedInfo {
  placeKeyword: string;
  isNearby: boolean;
}

// 定义新的意图识别结果类型
interface ExtractedIntentInfo {
  intent: 'initial_search' | 'more_recommendations' | 'details_about_place' | 'clarification' | 'generic_query' | 'context_switch' | 'weather_query' | 'unknown'; // <-- ADD weather_query
  placeKeyword?: string; // For initial_search
  placeName?: string;    // For details_about_place
  isNearby?: boolean;    // For initial_search
  // --- Weather specific entities ---
  locationName?: string; // For weather_query (e.g., 'Beijing')
  forecastType?: 'current' | 'forecast' | 'today' | 'tomorrow'; // For weather_query
}

// 修改 extractPlaceInfoWithAI 函数，使其具有上下文感知能力并返回结构化意图
const extractPlaceInfoWithAI = async (
  recognizedText: string,
  historyContext: Message[], // <-- NEW: Pass conversation history
  searchContext: { keyword: string | null, location: Location.LocationObject | null, isNearbyQuery: boolean } | null, // <-- MODIFIED: Add isNearbyQuery
  addLogFunc?: (message: string) => void
): Promise<ExtractedIntentInfo> => {
  const log = addLogFunc || console.log;
  log(`AI Intent: Analyzing query: "${recognizedText}"`);
  // 定义默认/回退结果
  const fallbackResult: ExtractedIntentInfo = { intent: 'unknown' }; 

  // --- 构建包含上下文的 Prompt --- 
  let prompt = `You are an intent recognition system for a travel assistant.
Analyze the latest "User Query" based on the "Conversation History" and the "Current Search Context".
Determine the user's primary intent AND extract relevant entities based on the options below. Prioritize extracting specific location names (cities, countries, famous landmarks) as 'locationName' when mentioned.

INTENT OPTIONS & ENTITIES:

- initial_search: User wants to find places/locations. 
  - REQUIRED: Extract 'placeKeyword' (e.g., food, hotel, attraction, cafe, 美食, 景点).
  - OPTIONAL: Extract 'locationName' if a specific city/country/landmark is mentioned (e.g., Tokyo, Japan, Eiffel Tower).
  - OPTIONAL: Extract 'isNearby' (boolean: true if query implies searching near current location like 'nearby', 'around here', '附近'). Defaults to false if 'locationName' is present.

- more_recommendations: User asks for more results related to the Current Search Context (keyword: ${searchContext?.keyword || 'None'}). If 'isNearbyQuery' is true, this implies more *nearby* results.

- details_about_place: User asks for details about a *specific named place* mentioned recently (e.g., 'tell me more about that restaurant', 'PC南西餐怎么样'). Extract 'placeName'.

- weather_query: User *specifically* asks about weather conditions or forecast. 
  - REQUIRED: Determine 'forecastType' ('current', 'forecast', 'today', 'tomorrow', default: 'current').
  - OPTIONAL: Extract 'locationName' if a specific city/place is mentioned (e.g., London, 北京). Default to current location if none.

- clarification: Query is ambiguous, needs more info, or asks about the assistant.
- generic_query: General knowledge, small talk NOT related to travel/places/weather.
- context_switch: User clearly changes topic away from the Current Search Context.
- unknown: If none of the above fit.

Conversation History (Last few turns):
${historyContext.map(msg => `${msg.sender === 'user' ? 'User' : 'AI'}: ${stripAllTags(msg.text)}`).join('\n') || 'None'} 

Current Search Context:
Keyword: ${searchContext?.keyword || 'None'}
Location Set: ${searchContext?.location ? 'Yes' : 'No'}
Is Nearby Query: ${searchContext?.isNearbyQuery === true}

User Query: "${recognizedText}"

Respond ONLY with a valid JSON object containing the determined "intent" and ALL relevant extracted entities ('placeKeyword', 'placeName', 'isNearby', 'locationName', 'forecastType'). Set 'isNearby' to false if 'locationName' is extracted for an initial_search.
Example 1 (Weather in City): {"intent": "weather_query", "locationName": "London", "forecastType": "forecast"}
Example 2 (Nearby Place Search): {"intent": "initial_search", "placeKeyword": "cafe", "isNearby": true}
Example 3 (Specific Location Search): {"intent": "initial_search", "placeKeyword": "attractions", "locationName": "Tokyo", "isNearby": false}
Example 4 (Specific Location Food): {"intent": "initial_search", "placeKeyword": "美食", "locationName": "日本", "isNearby": false} 
Example 5 (Weather Current Location): {"intent": "weather_query", "forecastType": "current"}`; 

  // --- Call Gemini API --- 
  try {
    const requestBody = {
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: { 
        temperature: 0.2, // Slightly lower temperature for more predictable intent classification
        maxOutputTokens: 150, 
      }
    };

    log('AI Intent: Sending request to Gemini (using gemini-2.0-flash-lite)...');
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash-lite:generateContent?key=${API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      log(`AI Intent: API Error ${response.status} - ${errorText}`);
      return { ...fallbackResult, intent: 'unknown' }; // Return unknown on API error
    }

    const data = await response.json();
    log('AI Intent: Response received: ' + JSON.stringify(data).substring(0, 150));

    // --- Extract Text and Parse Structured JSON --- 
    if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
      const rawTextResponse = data.candidates[0].content.parts[0].text;
      log(`AI Intent: Raw text response received: "${rawTextResponse}"`);
      
      const jsonMatch = rawTextResponse.match(/\{.*\}/s);
      let jsonStringToParse: string | null = null;
      
      if (jsonMatch && jsonMatch[0]) {
          jsonStringToParse = jsonMatch[0];
          log(`AI Intent: Extracted potential JSON string: ${jsonStringToParse}`);
      } else {
          log('AI Intent: Could not find JSON block in the raw text response.');
      }

      if (jsonStringToParse) {
          try {
            const parsedResult = JSON.parse(jsonStringToParse);
            if (typeof parsedResult.intent === 'string') { 
              log(`AI Intent: Successfully parsed intent: ${parsedResult.intent}`);
              // Default forecastType if intent is weather_query but type is missing
              let forecastTypeDefaulted: ExtractedIntentInfo['forecastType'] = typeof parsedResult.forecastType === 'string' ? parsedResult.forecastType.trim() : undefined;
              if (parsedResult.intent === 'weather_query' && !forecastTypeDefaulted) {
                  forecastTypeDefaulted = 'current'; // Default to current if type missing for weather
              }
              return {
                intent: parsedResult.intent,
                placeKeyword: typeof parsedResult.placeKeyword === 'string' ? parsedResult.placeKeyword.toLowerCase().trim() : undefined,
                placeName: typeof parsedResult.placeName === 'string' ? parsedResult.placeName.trim() : undefined,
                isNearby: typeof parsedResult.isNearby === 'boolean' ? parsedResult.isNearby : (parsedResult.intent === 'initial_search' && (recognizedText.toLowerCase().includes('nearby') || recognizedText.includes('附近'))), 
                locationName: typeof parsedResult.locationName === 'string' ? parsedResult.locationName.trim() : undefined,
                forecastType: forecastTypeDefaulted, // Use potentially defaulted type
              };
            } else {
              log('AI Intent: Parsed JSON lacks mandatory "intent" field.');
            }
          } catch (parseError) {
            log(`AI Intent: Failed to parse extracted JSON string: ${parseError}. String was: ${jsonStringToParse}`);
          }
      }
    } else {
      log('AI Intent: Could not extract text part from API response.');
    }

  } catch (error) {
    log(`AI Intent: Network or fetch error: ${error}`);
  }

  log('AI Intent: Returning fallback result (unknown intent).');
  return { ...fallbackResult, intent: 'unknown' };
};

// --- NEW: Helper function to add SSML lang tags for English text ---
const addSSMLLangTags = (text: string): string => {
  if (!text) return '';
  // Regex to find potential English words/phrases (sequences of Latin letters, spaces, hyphens, apostrophes)
  // It avoids tagging numbers or isolated letters.
  const englishRegex = /([a-zA-Z][a-zA-Z '-]{1,})/g;
  
  try {
    return text.replace(englishRegex, (match) => {
      // Basic check to avoid tagging very short sequences like 'a' or 'I'
      if (match.trim().length <= 2 && !match.includes(' ')) {
          return match; // Don't tag isolated letters or short acronyms like 'AI'
      }
      // Check if already tagged (simple check)
      if (text.includes(`<lang xml:lang="en-US">${match}</lang>`)) {
           return match;
      }
      return `<lang xml:lang="en-US">${match}</lang>`;
    });
  } catch (error) {
    console.error("Error adding SSML tags:", error);
    return text; // Return original text on error
  }
};

// --- NEW: Helper to map common keywords to Google Places API (New) Types ---
const mapKeywordToPlaceType = (keyword: string): string | null => {
  const lowerKeyword = keyword.toLowerCase().trim();

  // --- Cuisine mappings -> restaurant ---
  if ([
    'food', 'restaurant', 'eat', 'makan', '餐', '吃', '饭店', '馆', '美食',
    'western food', 'western', '西餐', 
    'italian food', 'italian', '意大利',
    'japanese food', 'japanese', 'sushi', 'ramen', '日料', '寿司', '拉面',
    'chinese food', 'chinese', '中餐',
    'indian food', 'indian', '印度',
    'malay food', 'malay', '马来',
    'thai food', 'thai', '泰国',
    // Add more cuisines...
  ].some(term => lowerKeyword.includes(term))) {
    return 'restaurant';
  }

  // --- Other common types ---
  // Use switch for exact or near-exact matches after cuisine check
  switch (lowerKeyword) {
    case 'attraction':
    case 'sightseeing':
    case 'tourist':
    case '景点':
    case '玩':
    case '逛':
      return 'tourist_attraction';
    case 'hotel':
    case 'accommodation':
    case 'inn':
    case 'motel':
    case '住':
    case '酒店':
    case '旅馆':
      return 'lodging';
    case 'cafe':
    case 'coffee':
    case '咖啡':
      return 'cafe';
    case 'bar':
    case 'pub':
    case '酒':
      return 'bar';
    case 'park':
    case 'garden':
    case '公园':
      return 'park';
    case 'museum':
    case 'gallery':
    case '博物馆':
    case '美术馆':
      return 'museum';
    case 'shopping mall':
    case 'mall':
    case '购物中心':
    case '商场':
      return 'shopping_mall';
    case 'supermarket':
    case 'grocery':
    case '超市':
    case '杂货店':
      return 'supermarket';
    case 'pharmacy':
    case 'drugstore':
    case '药店':
      return 'pharmacy';
    case 'bank':
    case '银行':
      return 'bank';
    // Add more specific types...
    default:
      console.log(`mapKeywordToPlaceType: No specific type mapping found for keyword: "${keyword}"`);
      return null; 
  }
};

// --- NEW: AI Function for Language Detection ---
const detectLanguageWithAI = async (
  text: string,
  addLogFunc?: (message: string) => void
): Promise<string> => {
  const log = addLogFunc || console.log;
  const fallbackLangCode = 'en-US'; // Default language if detection fails
  log(`Language Detection: Analyzing text: "${text.substring(0, 50)}..."`);

  if (!text || text.trim().length === 0) {
    log('Language Detection: Input text is empty, returning fallback.');
    return fallbackLangCode;
  }

  // Simple prompt asking for BCP-47 code
  const prompt = `Identify the PRIMARY BCP-47 language code of the following user query. Focus on the language the user is primarily writing in, rather than specific place names or keywords. Respond ONLY with the language code (e.g., 'en-US', 'zh-CN', 'ms-MY'). Text: "${text}"`;

  try {
    const requestBody = {
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: { 
        temperature: 0.1, // Low temperature for deterministic output
        maxOutputTokens: 10 // Limit output tokens
      }
    };

    log('Language Detection: Sending request to Gemini...');
    const response = await fetch(
      // Using flash model for speed and cost efficiency
      `https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash-lite:generateContent?key=${API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      log(`Language Detection: API Error ${response.status} - ${errorText}`);
      return fallbackLangCode;
    }

    const data = await response.json();
    log(`Language Detection: Raw API Response: ${JSON.stringify(data).substring(0, 100)}...`);

    if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
      const detectedCode = data.candidates[0].content.parts[0].text.trim();
      // Basic validation of the format (e.g., xx-XX)
      if (/^[a-z]{2,3}(-[A-Z]{2,3})?$/.test(detectedCode)) { 
        log(`Language Detection: Detected code: ${detectedCode}`);
        return detectedCode;
      } else {
        log(`Language Detection: Invalid code format received: "${detectedCode}". Returning fallback.`);
        return fallbackLangCode;
      }
    } else {
      log('Language Detection: Could not extract text part from API response. Returning fallback.');
      return fallbackLangCode;
    }

  } catch (error) {
    log(`Language Detection: Network or fetch error: ${error}`);
    return fallbackLangCode;
  }
};

// 获取AI响应API (Gemini) - 优化无结果处理 + 类型检查 + 区分来源 + 优化语音Prompt + 多语言支持 + 上下文意图感知
const getAIResponse = async (
  contextMessages: Message[], 
  userInput: string, 
  targetLanguageCode: string, 
  intentInfo: ExtractedIntentInfo, // <-- NEW: Pass detected intent
  currentLocation?: Location.LocationObject | null,
  addLogFunc?: (message: string) => void, 
  placesList?: any[] | null, // <-- NOTE: This is now a SUBSET of places for this turn
  source: 'text' | 'voice' = 'text'
): Promise<string> => {
  const log = addLogFunc || console.log;
  
  try {
    // Log with intent information
    log(`为查询获取AI响应: "${userInput}" (来源: ${source}, 目标语言: ${targetLanguageCode}, 意图: ${intentInfo.intent}) ${placesList ? `(附带 ${placesList.length} 个地点)` : '(无地点提供)'} ${intentInfo.isNearby === true ? '(Focus: Nearby)' : ''}`); // Log nearby focus
    
    // Filter history for empty messages AFTER stripping tags
    const history = contextMessages
      .map(msg => ({
        role: msg.sender === 'user' ? 'user' : 'model',
        // Ensure msg.text is treated as string, default to empty if null/undefined
        parts: [{ text: stripAllTags(msg.text || '') }] 
      }))
      .filter(msg => msg.parts[0].text.trim().length > 0);
    
    log(`Filtered history contains ${history.length} valid messages.`); // Use the defined log function
    
    let promptText = ``; // Start with an empty prompt, build based on intent
    const baseInstruction = `Act as a friendly, knowledgeable, and conversational local travel guide. Your user is currently interacting via ${source === 'voice' ? 'voice command' : 'text chat'}.`;
    const nearbyFocusInstruction = intentInfo.isNearby === true ? " The user's current search focus is finding places *nearby* their location." : "";

    // --- Build Prompt based on Intent --- 
    switch (intentInfo.intent) {
      case 'initial_search':
        // Handle Initial Search (Both Nearby and Specific Location)
        if (placesList && placesList.length > 0) {
          // *** Case 1: Search successful (Nearby or Specific Location) ***
          const placeNames = placesList.map(p => p?.name || '未知地点').join(', ');
          const locationContext = intentInfo.locationName ? `in ${intentInfo.locationName}` : 'nearby'; // Determine context for prompt
          if (source === 'text') {
            promptText = `${baseInstruction} The user asked about "${userInput}" ${locationContext}. I found these places: ${placeNames}. Recommend a few (2-3) diverse options from this list in a helpful, human-like way. Briefly mention why each might be good. Ask if they'd like more details on one or perhaps other types of places ${intentInfo.locationName ? `in ${intentInfo.locationName}` : 'nearby'}. Use Markdown bold ONLY for the exact place names.`;
          } else { // voice
            promptText = `${baseInstruction} The user asked about "${userInput}" ${locationContext}. I found: ${placeNames}. Recommend one or two naturally. Keep it concise for listening. Ask about more details or other options ${intentInfo.locationName ? `in ${intentInfo.locationName}` : 'nearby'}. Use natural language only.`;
          }
        } else {
          // *** Case 2: Search Failed (Places list is empty or null) ***
          if (intentInfo.locationName && intentInfo.isNearby === false) {
            // **Subcase 2a: Failed search within a SPECIFIC location**
             const failedKeyword = intentInfo.placeKeyword || 'specific places'; // Use keyword or generic term
             if (source === 'text') {
                promptText = `${baseInstruction} The user asked about "${userInput}" (specifically related to "${failedKeyword}" in ${intentInfo.locationName}). My specific search within ${intentInfo.locationName} didn't return results right now. 

However, I can still tell you general information about ${intentInfo.locationName}! Would you like to hear about its main attractions, typical weather, or perhaps look for a different type of place there, like restaurants or hotels?`;
             } else { // voice
                promptText = `${baseInstruction} My search for "${failedKeyword}" in ${intentInfo.locationName} didn't find specific results just now. But I can tell you more about ${intentInfo.locationName} itself, like its main sights, or search for hotels there instead. What would you prefer? Use natural language only.`;
             }
          } else {
            // **Subcase 2b: Failed NEARBY search (original logic, slightly refined)**
             const failedKeyword = intentInfo.placeKeyword || 'that';
             if (source === 'text') {
                promptText = `${baseInstruction} The user asked about "${userInput}" nearby, but my specific search for '${failedKeyword}' didn't find exact matches right now. Perhaps try searching for a broader category or a different keyword for this area?`;
             } else { // voice
                promptText = `${baseInstruction} Unfortunately, my search for '${failedKeyword}' near you didn't find specific matches just now. Maybe try asking for restaurants or parks nearby instead? Use natural language only.`;
             }
          }
        }
        break;

      case 'more_recommendations':
         if (placesList && placesList.length > 0) {
          const placeNames = placesList.map(p => p?.name || '未知地点').join(', ');
          if (source === 'text') {
             promptText = `${baseInstruction} The user asked for more recommendations based on their previous search for '${intentInfo.placeKeyword || 'places'}'. Here are some other options I found: ${placeNames}. Present these additional options conversationally. Ask if any catch their eye or if they're looking for something else now. Use Markdown bold ONLY for the exact place names from the new list.`;
          } else { // voice
             promptText = `${baseInstruction} Sure, the user wants more recommendations for '${intentInfo.placeKeyword || 'places'}'. Here are a few more I found: ${placeNames}. Briefly present one or two of these naturally. Keep it conversational and suitable for listening. Ask if they want details or are done with this search. Use natural language only, no Markdown or SSML tags.`;
          }
        } else { // No more recommendations in cache
          if (source === 'text') {
             promptText = `${baseInstruction} The user asked for more recommendations, but I've already shown all the nearby places I found for '${intentInfo.placeKeyword || 'their initial search'}'. Let them know politely that these are all the current recommendations for that specific search. Ask if they'd like to search for something different.`;
          } else { // voice
             promptText = `${baseInstruction} Okay, the user wants more recommendations, but I've shared all the nearby options I found for '${intentInfo.placeKeyword || 'that'}'. Politely inform them that's all for this search, and ask if they want to look for something else. Keep it natural and brief. Use natural language only, no Markdown or SSML tags.`;
          }
        }
        break;
        
      // case 'details_about_place': // TODO: Add specific prompt logic here
      //   promptText = `${baseInstruction} The user is asking for details about "${intentInfo.placeName}". Provide helpful details based on previous context or general knowledge.`;
      //   break;
        
      case 'clarification':
        promptText = `${baseInstruction} The user's request ("${userInput}") wasn't entirely clear in the current context. Ask a friendly clarifying question to understand what they need.`;
        break;
        
      case 'generic_query':
      case 'context_switch':
      default: // Handle unknown as generic too
        if (source === 'text') {
           promptText = `You are a friendly travel assistant.
First, evaluate if the user's query "${userInput}" is related to travel (destinations, planning, local info, transport, accommodation, tips, culture, food related to travel etc.).

IF the query IS travel-related:
Answer it helpfully and conversationally from the perspective of a travel expert.

IF the query IS NOT travel-related:
Politely acknowledge the question briefly. Then, gently state that your main function is to assist with travel-related topics and ask if there is anything travel-related you can help with. **DO NOT provide a detailed answer to the non-travel-related question.**

Respond ONLY in the language indicated by this code: ${targetLanguageCode}.`;
        } else { // voice - Similar logic, emphasize brevity and natural language for non-travel related decline
           promptText = `You are a friendly travel assistant speaking to the user.
Evaluate if the user's query "${userInput}" is travel-related.

IF travel-related: Answer helpfully and concisely, suitable for listening.

IF NOT travel-related: Briefly acknowledge it politely (e.g., "That's an interesting question!"). Then gently explain you specialize in travel help (e.g., "As a travel guide, I'm best at..." ). Ask if they have any travel questions. **DO NOT answer the non-travel question in detail.** Use natural spoken language only.

Respond ONLY in the language indicated by this code: ${targetLanguageCode}.`;
        }
        break;
    }
    
    log(`AI Prompt (${source} - Intent: ${intentInfo.intent}): ${promptText.substring(0, 150)}...`);

    // --- Add Language Instruction (Redundant now as included in prompts above, but keep for safety) --- 
    promptText += `\n\nPlease respond ONLY in the language indicated by this code: ${targetLanguageCode}. Do not include the language code in your response.`;
    log(`Appended language instruction for code: ${targetLanguageCode}`);

    // --- Call Gemini API --- 
    // Use filtered history
    const finalPromptText = promptText.trim(); // Ensure prompt isn't just whitespace
    if (finalPromptText.length === 0) {
        log('getAIResponse Error: Final prompt text is empty after processing.');
        return "抱歉，处理请求时出现内部错误 (空提示)";
    }
    const contents = [...history, { role: 'user', parts: [{ text: finalPromptText }] }]; 
    
    // Add a check for empty contents array (unlikely but possible)
    if (contents.length === 0 || (contents.length === 1 && !contents[0].parts[0].text.trim())) {
        log('getAIResponse Error: Contents array is effectively empty before API call.');
        return "抱歉，没有有效的对话内容可以发送给AI。";
    }
    
    const requestBody = {
      contents,
      generationConfig: { temperature: 0.8, topK: 40, topP: 0.9, maxOutputTokens: 1024 } 
    };
    
    log('Sending request to Gemini API...');
    const response = await fetch(
      `https://generativelanguage.googleapis.com/v1/models/gemini-2.0-flash-lite:generateContent?key=${API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      const errorData = await response.text();
      log(`获取AI响应API错误: ${response.status} - ${errorData}`); // 使用 log
      // Return specific error with status code
      return `抱歉，AI 服务暂时遇到了问题 (状态码: ${response.status})。请稍后再试。`; 
    }

    const data = await response.json();
    log('获取AI响应API响应:' + JSON.stringify(data).substring(0, 200) + '...');

    if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
      const aiText = data.candidates[0].content.parts[0].text;
      // Add a final check for empty response text from AI
      if (!aiText.trim()) {
           log('AI 服务返回了空文本响应。');
           return "抱歉，AI 服务返回了空回复。";
      }
      return aiText;
    } else {
      log('无法从API响应中提取文本'); // 使用 log
      return "抱歉，AI 服务返回了无效的响应格式。"; // More specific error
    }

  } catch (error) {
    // 确保 catch 块也能访问到 log
    const log = addLogFunc || console.log; 
    log(`获取AI响应API调用失败: ${error instanceof Error ? error.message : error}`);
    if (error instanceof Error && error.stack) {
         log(`getAIResponse error stack: ${error.stack}`);
    }
    // Return specific network/fetch error message
    return "抱歉，连接 AI 服务时似乎遇到了网络问题。请检查您的网络连接。";
  }
}; // 确保函数有返回值 (No default return needed now)

// --- NEW: Function to call OpenAI GPT-4o Vision API ---
// --- Modified: Added userLocation parameter and updated prompt ---
const getGPT4oVisionResponse = async (
  promptText: string,
  imageBase64: string,
  apiKey: string,
  userLocation: Location.LocationObject | null, // <-- ADDED userLocation
  addLogFunc?: (message: string) => void
): Promise<string | null> => {
  const log = addLogFunc || console.log;
  const visionApiEndpoint = 'https://api.openai.com/v1/chat/completions';

  // --- Construct Location Context String ---
  let locationContextString = "User's current location is unknown.";
  if (userLocation) {
    locationContextString = `User is currently near latitude ${userLocation.coords.latitude.toFixed(4)}, longitude ${userLocation.coords.longitude.toFixed(4)}.`;
    // Optionally add accuracy if needed: ` (Accuracy: ${userLocation.coords.accuracy?.toFixed(1)}m)`
    // You could also reverse geocode here to get city/country, but that adds another API call/complexity.
  }
  log(`Location context for Vision prompt: ${locationContextString}`);

  // --- Construct Enhanced Prompt ---
  const enhancedPrompt = `You are a helpful travel assistant analyzing an image provided by the user.
${locationContextString}

User's query/comment about the image: "${promptText}"

Instructions for analyzing the image:
1.  Identify the main subject, landmarks, or buildings in the image.
2.  **Prioritize using the user's location information:** Try to determine if the identified subject is located near the user's current coordinates. Search for potential matches in the vicinity first (e.g., within the same city or region if identifiable).
3.  **If a likely match is found near the user's location:** State the identified place and mention its location relative to the user (e.g., "This looks like [Place Name] in [City/Region], which is near your current location."). Provide relevant information or answer the user's query based on this identification.
4.  **If no match is found nearby OR the subject is clearly famous and located elsewhere:** Identify the subject and its known location, even if it's far from the user. (e.g., "This appears to be the Eiffel Tower in Paris, France.").
5.  **If the location cannot be determined reliably from the image and context:** Describe what you see in the image and acknowledge you cannot pinpoint the exact location based on the provided information. Ask the user for more details if needed.
6.  Address the user's original query ("${promptText}") based on your analysis.
7.  Respond in a natural, conversational tone.`;

  log(`Calling OpenAI Vision API: Endpoint=${visionApiEndpoint}, Prompt (start): "${enhancedPrompt.substring(0, 100)}...", Image Base64 Length: ${imageBase64.length}`);

  // Determine image format (simple guess based on typical mobile outputs)
  const imageFormat = imageBase64.startsWith('/9j/') ? 'jpeg' : 'png'; // Basic check for JPEG

  try {
    const requestBody = {
      model: "gpt-4o-mini", // Use the specified GPT-4o mini model
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: enhancedPrompt }, // <-- Use the enhanced prompt
            {
              type: "image_url",
              image_url: {
                url: `data:image/${imageFormat};base64,${imageBase64}`,
              }
            }
          ]
        }
      ],
      max_tokens: 500 // Limit response length
    };

    log('OpenAI Vision API: Sending request...');
    const response = await fetch(visionApiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}` // Fixed template literal syntax
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorData = await response.text();
      log(`OpenAI Vision API Error: ${response.status} - ${errorData}`); // Fixed template literal syntax
      return `抱歉，处理图像时遇到了 OpenAI API 错误 (状态码: ${response.status})。请稍后再试。`; // Fixed template literal syntax
    }

    const data = await response.json();
    log('OpenAI Vision API Response Received: ' + JSON.stringify(data).substring(0, 150) + '...');

    if (data.choices && data.choices[0]?.message?.content) {
      const aiText = data.choices[0].message.content;
      log(`OpenAI Vision API Success. Response length: ${aiText.length}`); // Fixed template literal syntax
      return aiText;
    } else {
      log('OpenAI Vision API: Could not extract content from response.');
      return "抱歉，OpenAI API 返回了无效的响应格式。";
    }

  } catch (error) {
    log(`OpenAI Vision API: Network or fetch error: ${error}`); // Fixed template literal syntax
    if (error instanceof Error && error.stack) {
       log(`OpenAI Vision API error stack: ${error.stack}`); // Fixed template literal syntax
    }
    return "抱歉，连接 OpenAI 服务时似乎遇到了网络问题。请检查您的网络连接。";
  }
}; // --- END NEW FUNCTION ---

// 文本转语音API (Text-to-Speech) - 优化 SSML 检测 + 多语言支持
const textToSpeechAPI = async (
  ssmlOrText: string,
  languageCode: string = 'cmn-CN' // <-- NEW: Language code parameter, default to Chinese
): Promise<string | null> => {
  // Add detailed logging at the very beginning
  console.log(`TTS API Input Received: "${ssmlOrText}" (Length: ${ssmlOrText?.length || 0}, Target Lang: ${languageCode})`);
  
  try {
    // Use Regex for SSML detection (case-insensitive check for <speak or <lang)
    const ssmlRegex = /<speak|<lang /i;
    const isSSML = ssmlRegex.test(ssmlOrText);
    
    // Log the result of the detection
    console.log(`TTS API: Input detected as ${isSSML ? 'SSML' : 'TEXT'}`);
    
    // Use console.log as addLog is not available here
    console.log(`将 ${isSSML ? 'SSML' : '文本'} 转换为语音: "${ssmlOrText.substring(0,100)}..." (Lang: ${languageCode})`);
    
    // --- Select Voice Name based on Language Code --- << NEW
    let voiceName = 'en-US-Standard-A'; // Default English voice
    let ssmlGender: 'FEMALE' | 'MALE' | 'NEUTRAL' = 'FEMALE'; // Default
    
    if (languageCode.startsWith('zh') || languageCode.startsWith('cmn')) { // Chinese (Mandarin)
      voiceName = 'cmn-CN-Standard-A'; 
      ssmlGender = 'FEMALE';
    } else if (languageCode.startsWith('ms')) { // Malay
      voiceName = 'ms-MY-Standard-A'; // Example, check Google TTS docs for availability
      ssmlGender = 'FEMALE';
    } else if (languageCode.startsWith('en')) { // English (Default US, could add more specific like en-GB)
      voiceName = 'en-US-Standard-A'; 
      ssmlGender = 'FEMALE';
    } // Add more language codes and voice names as needed
    
    console.log(`TTS API: Selected Voice Name: ${voiceName} for Language: ${languageCode}`);
    
    const requestBody = {
      input: isSSML 
        ? { ssml: ssmlOrText } 
        : { text: ssmlOrText },
      voice: {
        languageCode: languageCode, // Use the parameter
        name: voiceName,            // Use the selected voice name
        ssmlGender: ssmlGender      // Use the selected gender
      },
      audioConfig: {
        audioEncoding: 'MP3'
      }
    };

    // Ensure SSML input is wrapped in <speak> tags
    if (requestBody.input.ssml) { 
        let ssmlInput = requestBody.input.ssml;
        ssmlInput = ssmlInput.trim(); 
        // Check if it *doesn't* start/end with <speak> OR if it's just a fragment containing <lang>
        if (!ssmlInput.startsWith('<speak>') || !ssmlInput.endsWith('</speak>')) {
           // Wrap only if the tags are missing or incomplete
           requestBody.input.ssml = `<speak>${ssmlInput.replace(/^<speak>/i, '').replace(/<\/speak>$/i, '')}</speak>`; // Remove existing partial tags before wrapping
           console.log('TTS API: Automatically wrapped/corrected input in <speak> tags.');
        } else {
            requestBody.input.ssml = ssmlInput; 
        }
        // Log the final SSML being sent
        console.log(`TTS API: Final SSML payload: ${requestBody.input.ssml.substring(0, 150)}...`);
    }

    // Send request to Google Cloud TTS API
    console.log('TTS API: Sending request...'); 
    const response = await fetch(
      `https://texttospeech.googleapis.com/v1/text:synthesize?key=${API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      const errorData = await response.text();
      // Use console.error
      console.error('文本转语音API错误:', errorData);
      return null;
    }

    const data = await response.json();
    // Use console.log
    console.log('文本转语音API响应已收到');
    
    if (data && data.audioContent) {
      return data.audioContent;
    } else {
      // Use console.error
      console.error('无法从API响应中提取音频数据');
      return null;
    }
  } catch (error) {
    // Use console.error
    console.error('文本转语音API调用失败:', error);
    return null;
  }
};

// 模拟响应
const mockResponse = (query: string, imageAttached?: boolean) => {
  // 图片响应
  if (imageAttached) {
    return "I can see the image you've shared. Based on what I observe:\n\n**Analysis of your image:**\n- This appears to be a travel-related scene\n- The location seems to be in an interesting area\n- The lighting and composition are good\n\nWould you like recommendations related to this location or information about similar destinations? I can help with identifying landmarks, suggesting nearby attractions, or finding similar places to visit.";
  }

  // 常规响应
  const responses: Record<string, string> = {
    'Recommend travel destinations': 'Here are some travel destinations worth considering:\n\n1. **Bali - Indonesia**\nBeautiful beaches, unique culture, and rich natural landscapes make it ideal for relaxation and exploration.\n\n2. **Kyoto - Japan**\nHome to over 1,600 temples and shrines, it\'s a treasure of Japanese traditional culture.\n\n3. **Barcelona - Spain**\nGaudí\'s architectural masterpieces, Mediterranean cuisine, and vibrant city atmosphere.\n\n4. **Maldives**\nOverwater villas and stunning marine life, perfect for luxury and tranquility.\n\n5. **Santorini - Greece**\nIconic white and blue buildings, plus incredible Aegean Sea sunsets.\n\nDo you have a specific region or type of travel in mind? I can provide more targeted recommendations.',
    
    'Trip planning': 'Here\'s a sample three-day travel itinerary:\n\n**Day 1: Exploration & Culture**\n- **Morning**: Visit local museums or historical landmarks\n- **Afternoon**: Explore the city center, experience local culture\n- **Evening**: Dinner at a local specialty restaurant\n\n**Day 2: Nature & Adventure**\n- **Morning**: Outdoor activities (hiking, cycling, or water sports)\n- **Afternoon**: Visit natural attractions or parks\n- **Evening**: Experience local nightlife or cultural performances\n\n**Day 3: Relaxation & Souvenirs**\n- **Morning**: Leisurely breakfast followed by free time or shopping\n- **Afternoon**: Visit final attractions or enjoy spa services\n- **Evening**: Farewell dinner, try any remaining local cuisine\n\nWould you like a detailed plan for a specific destination? Please let me know where you plan to go.',
    
    'Local cuisine': 'Exploring local cuisine is one of the best ways to understand a culture. Here are specialty foods from several travel destinations:\n\n**Southeast Asia**\n- Thailand: Tom Yum Soup, Pad Thai, and Mango Sticky Rice\n- Vietnam: Pho, Spring Rolls, and Banh Mi sandwiches\n- Malaysia: Satay, Nyonya cuisine, and Bak Kut Teh\n\n**Europe**\n- Italy: Unique pasta from each region, pizza, and tiramisu\n- Spain: Paella, tapas, and Iberian ham\n- France: Croissants, steak, cheese, and elegant desserts\n\n**Asia**\n- Japan: Sushi, ramen, and tempura\n- China: Specialty cuisines from various provinces such as Sichuan, Cantonese, Shandong, etc.\n- India: Various curries, naan bread, and spice-rich dishes\n\nTrying street food is often an excellent way to experience the most authentic flavors. Which region\'s cuisine are you particularly interested in?'
  };
  
  // 关键词匹配
  for (const key in responses) {
    const lowerQuery = query.toLowerCase();
    const lowerKey = key.toLowerCase();
    if (lowerQuery.includes(lowerKey) || 
        (lowerKey.includes('destination') && lowerQuery.includes('recommend')) ||
        (lowerKey.includes('trip') && lowerQuery.includes('planning')) ||
        (lowerKey.includes('cuisine') && lowerQuery.includes('food'))) {
      return responses[key];
    }
  }
  
  // 通用响应
  return `About "${query}", I can provide the following information:\n\nAs your travel assistant, I can help you with:\n\n• Recommending popular or off-the-beaten-path destinations worldwide\n• Planning itineraries and optimizing your travel time\n• Introducing local cuisine and cultural specialties\n• Providing accommodation and transportation advice\n• Sharing budget travel tips\n\nPlease tell me which specific aspect you're interested in, and I'll provide more relevant information.`;
};

// 获取当前日期
const getCurrentDate = () => {
  const now = new Date();
  return `${now.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}`;
};

// 打字动画组件
const TypingAnimation = () => {
  const [dot1] = useState(new Animated.Value(0));
  const [dot2] = useState(new Animated.Value(0));
  const [dot3] = useState(new Animated.Value(0));

  useEffect(() => {
    const animateDot = (dot: any, delay: number) => {
      Animated.sequence([
        Animated.timing(dot, {
          toValue: 1,
          duration: 400,
          delay,
          useNativeDriver: true
        }),
        Animated.timing(dot, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true
        })
      ]).start();
    };

    const startAnimation = () => {
      animateDot(dot1, 0);
      animateDot(dot2, 200);
      animateDot(dot3, 400);
      
      setTimeout(startAnimation, 1200);
    };

    startAnimation();
    
    return () => {
      dot1.stopAnimation();
      dot2.stopAnimation();
      dot3.stopAnimation();
    };
  }, [dot1, dot2, dot3]);

  return (
    <View style={styles.typingIndicator}>
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot1.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot2.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.typingDot,
          {
            transform: [
              {
                translateY: dot3.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -4]
                })
              }
            ]
          }
        ]}
      />
    </View>
  );
};

// 新增：调用 Google Places API 获取地点详情
const getPlaceDetails = async (
  placeName: string,
  userLocation: Location.LocationObject | null,
  apiKey: string,
  addLogFunc?: (message: string) => void
): Promise<{ details: any; distanceKm: number | null; placeId: string | null } | null> => { // <-- 修改返回类型
  const log = addLogFunc || console.log;
  if (!userLocation) {
    log('Places API: User location not available.');
    return null;
  }

  log(`Places API: Searching for details for "${placeName}" near ${userLocation.coords.latitude},${userLocation.coords.longitude}`);

  try {
    // 1. Find Place ID - 使用坐标偏向，不再硬编码区域名称
    const searchInput = placeName; // 直接使用 AI 提供的名称
    const radius = 2000; // 将半径调回 2000 米，更强调邻近性
    const findPlaceUrl = `https://maps.googleapis.com/maps/api/place/findplacefromtext/json?input=${encodeURIComponent(searchInput)}&inputtype=textquery&locationbias=circle:${radius}@${userLocation.coords.latitude},${userLocation.coords.longitude}&key=${apiKey}`;
    log(`Places API: Find Place URL: ${findPlaceUrl.substring(0, 150)}...`);
    const findPlaceResponse = await fetch(findPlaceUrl);
    const findPlaceData = await findPlaceResponse.json();

    let placeId: string | null = null; // <-- 声明 placeId 变量
    if (findPlaceData.status === 'OK' && findPlaceData.candidates && findPlaceData.candidates.length > 0) {
      placeId = findPlaceData.candidates[0].place_id; // <-- 获取 placeId
      log(`Places API: Found place_id: ${placeId}`);
    } else {
      log(`Places API: Could not find place_id for "${placeName}". Status: ${findPlaceData.status}`);
      // 即使找不到 Place ID，仍然可以尝试使用名称进行详情查询（如果API支持），或者直接返回null
      // 为了简化，如果找不到 Place ID，我们这里先返回 null
      return null;
    }

    // 2. Get Place Details using place_id
    const fields = 'name,formatted_address,international_phone_number,opening_hours,rating,types,geometry';
    const detailsUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=${fields}&key=${apiKey}`;
    const detailsResponse = await fetch(detailsUrl);
    const detailsData = await detailsResponse.json();

    if (detailsData.status !== 'OK' || !detailsData.result) {
      log(`Places API: Could not get details for place_id ${placeId}. Status: ${detailsData.status}`);
      return null;
    }

    // ---> 新增：计算距离 <-----
    let distanceKm: number | null = null;
    if (detailsData.result.geometry && detailsData.result.geometry.location) {
      const placeLat = detailsData.result.geometry.location.lat;
      const placeLng = detailsData.result.geometry.location.lng;
      distanceKm = calculateDistance(
        userLocation.coords.latitude,
        userLocation.coords.longitude,
        placeLat,
        placeLng
      );
      log(`Places API: Calculated distance for "${detailsData.result.name}": ${distanceKm.toFixed(2)} km`);
    } else {
      log(`Places API: Geometry data missing for "${detailsData.result.name}", cannot calculate distance.`);
    }

    log(`Places API: Successfully fetched details for "${detailsData.result.name}"`);
    return { details: detailsData.result, distanceKm, placeId }; // <-- 在返回值中包含 placeId

  } catch (error) {
    log(`Places API: Error fetching place details: ${error}`);
    return null;
  }
};

// --- 优化：根据 AI 提及内容生成摘要和地图链接 ---
const formatSummaryWithLinks = (
  aiSummaryText: string,
  placesResults: any[], // 仍然需要原始 Places API 结果来查找 place_id
  addLogFunc?: (message: string) => void
): string => {
  const log = addLogFunc || console.log;
  const cleanedSummary = aiSummaryText.trim();

  // 1. 从 AI 回复中提取加粗的地点名称
  const boldNamePattern = /\*\*(.*?)\*\*/g;
  const aiMentionedNames = new Set<string>();
  let match;
  while ((match = boldNamePattern.exec(cleanedSummary)) !== null) {
    const name = match[1]?.trim();
    if (name && name.length > 1) { // 简单过滤掉空的或太短的名称
      aiMentionedNames.add(name);
    }
  }

  // 如果 AI 没有提及任何加粗名称，或者没有 Places API 结果，直接返回原始摘要
  if (aiMentionedNames.size === 0 || !placesResults || placesResults.length === 0) {
    log('formatSummaryWithLinks: No bold names found in AI text or no places results. Returning raw summary.');
    // 考虑是否移除AI文本中可能存在的地点列表部分，但这比较复杂
    // 暂时返回完整清理后的摘要
    return cleanedSummary;
  }

  log(`formatSummaryWithLinks: AI mentioned names (bold): ${[...aiMentionedNames].join(', ')}`);

  // 2. 将提取的名称与 placesResults 匹配，并保留顺序（尽量）
  const matchedPlaces: { name: string; place_id: string | null }[] = [];
  const foundInPlacesAPI = new Set<string>(); // 跟踪哪些API结果已被匹配

  // 优先按 AI 提及的顺序查找
  aiMentionedNames.forEach(aiName => {
    const lowerAiName = aiName.toLowerCase();
    const foundPlace = placesResults.find(p => 
      p.name && 
      p.name.toLowerCase() === lowerAiName && 
      !foundInPlacesAPI.has(p.place_id) // 确保每个API结果只用一次
    );

    if (foundPlace && foundPlace.place_id) {
      matchedPlaces.push({ name: aiName, place_id: foundPlace.place_id });
      foundInPlacesAPI.add(foundPlace.place_id);
      log(`formatSummaryWithLinks: Matched "${aiName}" with place_id: ${foundPlace.place_id}`);
    } else {
      log(`formatSummaryWithLinks: Could not find matching place_id for AI mentioned name: "${aiName}"`);
    }
  });
  
  // 如果没有匹配到任何地点，也只返回摘要
  if (matchedPlaces.length === 0) {
    log('formatSummaryWithLinks: No matches found between AI names and Places API results.');
    return cleanedSummary;
  }

  // 3. 构建最终输出：AI摘要 + 匹配到的地点列表
  // 考虑是否从 cleanedSummary 中移除 AI 可能自己生成的列表，以避免重复
  // 简化处理：暂时直接附加我们的列表
  let result = `${cleanedSummary}\n\n📍 附近推荐 (基于AI提及)：\n`;
  matchedPlaces.forEach((place, idx) => {
    const name = place.name; // 使用 AI 提及的名称（可能与 API 略有不同，但更符合上下文）
    const encoded = encodeURIComponent(name);
    const url = `https://www.google.com/maps/search/?api=1&query=${encoded}${place.place_id ? `&query_place_id=${place.place_id}` : ''}`;
    result += `${idx + 1}. **${name}** ([地图](${url}))\n`;
  });

  return result.trim();
};

// --- NEW: Utility to strip Markdown --- 
const stripMarkdown = (text: string): string => {
  if (!text) return '';
  // Remove bold (**text**)
  let cleaned = text.replace(/\*\*(.*?)\*\*/g, '$1');
  // Remove links ([text](url))
  cleaned = cleaned.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1'); 
  return cleaned.trim();
};

// --- NEW: Utility to strip Markdown AND SSML tags --- 
const stripAllTags = (text: string): string => {
  if (!text) return '';
  let cleaned = text;
  
  // 1. Remove Markdown Bold
  cleaned = cleaned.replace(/\*\*(.*?)\*\*/g, '$1');
  // 2. Remove Markdown Links ([text](url))
  cleaned = cleaned.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1');
  // 3. Remove <speak>...</speak> tags
  cleaned = cleaned.replace(/<speak>(.*?)<\/speak>/gs, '$1');
  // 4. Remove <lang xml:lang="...">...</lang> tags
  //    Handles potential attributes and ensures non-greedy matching for content
  cleaned = cleaned.replace(/<lang [^>]*>(.*?)<\/lang>/gs, '$1');
  // 5. (Optional) Remove other potential simple tags like <p>, <div> if needed
  // cleaned = cleaned.replace(/<\/?(p|div)[^>]*>/g, ''); 

  return cleaned.trim();
};

// GeminiChat组件集成到IGuidedScreen
function IGuidedScreen() {
  const navigation = useNavigation();
  const recordingRef = useRef(null); // 添加 Ref 来持有录音对象
  const playbackSoundRef = useRef(null); // <-- 新增：持有播放对象
  const [welcomePlayed, setWelcomePlayed] = useState(false); // <-- 新增：跟踪欢迎语是否播放过
  
  // --- NEW: Placeholder for Application Language --- 
  // TODO: Replace this with your actual logic to get the app's language 
  // (e.g., from state management, context, or AsyncStorage)
  const currentAppLanguage = 'en-US'; 
  // --- END Placeholder ---
  
  // 设置标题栏样式
  useFocusEffect(
    React.useCallback(() => {
      // 修改导航栏
      navigation.setOptions({
        headerShown: false, // 隐藏默认头部
      });
      
      // 状态栏样式
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('#ffffff');
      }
      StatusBar.setBarStyle('dark-content');
      
      // 清理函数
      return () => {
        if (Platform.OS === 'android') {
          StatusBar.setBackgroundColor('#ffffff');
        }
        StatusBar.setBarStyle('dark-content');
      };
    }, [navigation])
  );

  // 聊天相关状态
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [suggestions] = useState([
    'Nearby accommodation', 
    'Nearby food', 
    'Weather today'
  ]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(null);
  const scrollViewRef = useRef(null);
  const date = getCurrentDate();
  
  // 图片附件状态
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const [imageUri, setImageUri] = useState(null);
  const [showImageAttachmentModal, setShowImageAttachmentModal] = useState(false);
  const [imagePickerAvailable, setImagePickerAvailable] = useState(true);

  // 语音聊天状态 (UI展示用，不实际录音)
  const [isRecording, setIsRecording] = useState(false);
  // 添加实时语音聊天状态
  const [showVoiceChatModal, setShowVoiceChatModal] = useState(false);
  const [isVoicePaused, setIsVoicePaused] = useState(false);
  const [voiceChatMessages, setVoiceChatMessages] = useState([]);
  const [isListening, setIsListening] = useState(false);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false); // 添加处理标志，防止重复录音

  // --- NEW: State for contextual search and recommendations ---
  const [currentSearchContext, setCurrentSearchContext] = useState(null);
  const [cachedPlacesResults, setCachedPlacesResults] = useState(null);
  const [presentedPlaceIds, setPresentedPlaceIds] = useState(new Set());
  // --- END NEW STATE ---

  // 添加以下代码，用于语音波形动画
  const [waveHeights, setWaveHeights] = useState([10, 15, 20, 15, 10]);
  
  // 使用useEffect添加波形动画
  useEffect(() => {
    let animationFrameId: number;
    
    const animateWaves = () => {
      if (isListening && !isVoicePaused) {
        setWaveHeights([
          5 + Math.random() * 25,
          5 + Math.random() * 25,
          5 + Math.random() * 25,
          5 + Math.random() * 25,
          5 + Math.random() * 25
        ]);
      }
      animationFrameId = requestAnimationFrame(animateWaves);
    };
    
    animationFrameId = requestAnimationFrame(animateWaves);
    
    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [isListening, isVoicePaused]);

  // 检查权限和模块可用性
  useEffect(() => {
    (async () => {
      if (!ImagePicker) {
        setImagePickerAvailable(false);
        return;
      }
      
      if (Platform.OS !== 'web') {
        try {
          const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
          const { status: libraryStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();
          if (cameraStatus !== 'granted' || libraryStatus !== 'granted') {
            Alert.alert(
              'Permissions Required',
              'Please grant camera and photo library permissions to use this feature.',
              [{ text: 'OK' }]
            );
          }
        } catch (error) {
          console.warn('Error requesting camera permissions:', error);
          setImagePickerAvailable(false);
        }
      }
    })();
  }, []);

  const handleTakePhoto = async () => {
    setShowImageAttachmentModal(false);
    addLog('handleTakePhoto: Function started.'); // <-- ADD LOG
    
    if (!ImagePicker || !imagePickerAvailable) {
      addLog('handleTakePhoto: ImagePicker not available.'); // <-- ADD LOG
      Alert.alert(
        'Feature Unavailable',
        'Camera functionality is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    try {
      addLog('handleTakePhoto: Launching camera...'); // <-- ADD LOG
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      addLog(`handleTakePhoto: Camera result received. Cancelled: ${result.canceled}`); // <-- ADD LOG
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedAsset = result.assets[0];
        addLog(`handleTakePhoto: Image taken successfully. URI: ${selectedAsset.uri}, Size: ${selectedAsset.width}x${selectedAsset.height}`); // <-- ADD LOG
        setImageUri(selectedAsset.uri);
      } else {
        addLog('handleTakePhoto: No image asset returned or operation cancelled.'); // <-- ADD LOG
      }
    } catch (error) {
      addLog(`handleTakePhoto: Error taking photo: ${error}`); // <-- MODIFY LOG
      console.warn('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const handleChooseFromLibrary = async () => {
    setShowImageAttachmentModal(false);
    addLog('handleChooseFromLibrary: Function started.'); // <-- ADD LOG
    
    if (!ImagePicker || !imagePickerAvailable) {
      addLog('handleChooseFromLibrary: ImagePicker not available.'); // <-- ADD LOG
      Alert.alert(
        'Feature Unavailable',
        'Photo library access is not available on this device.',
        [{ text: 'OK' }]
      );
      return;
    }
    
    try {
      addLog('handleChooseFromLibrary: Launching image library...'); // <-- ADD LOG
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      addLog(`handleChooseFromLibrary: Library result received. Cancelled: ${result.canceled}`); // <-- ADD LOG
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedAsset = result.assets[0];
        addLog(`handleChooseFromLibrary: Image selected successfully. URI: ${selectedAsset.uri}, Size: ${selectedAsset.width}x${selectedAsset.height}`); // <-- ADD LOG
        setImageUri(selectedAsset.uri);
      } else {
        addLog('handleChooseFromLibrary: No image asset returned or operation cancelled.'); // <-- ADD LOG
      }
    } catch (error) {
      addLog(`handleChooseFromLibrary: Error choosing image: ${error}`); // <-- MODIFY LOG
      console.warn('Error choosing image from library:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  const handleRemoveImage = () => {
    addLog(`handleRemoveImage: Removing image URI: ${imageUri}`); // <-- ADD LOG
    setImageUri(null);
  };

  // Helper function to quickly check for non-location-related keywords
  const isGenericQuery = (text: string): boolean => {
    const lowerText = text.toLowerCase();
    const genericKeywords = [
      'hello', 'hi', 'how are you', 'who are you', // Greetings & Identity
      'joke', 'story', // Entertainment
      'capital of', 'what is', 'define', 'history of', // Factual questions
      'thank you', 'thanks', 'bye', // Conversation closers
      // Add more keywords or use a more sophisticated check if needed
    ];
    return genericKeywords.some(keyword => lowerText.includes(keyword));
  };

  // --- 修改 handleSend --- (集成 OpenAI Vision + 保留 Gemini 文本)
  const handleSend = async () => {
    if ((!input.trim() && !imageUri)) return;
    const userMessageText = input || "你能根据这张图片提供一些信息吗？"; // More natural default text for image-only
    const currentImageUri = imageUri; // Capture imageUri before clearing
    addLog(`handleSend: Function started. Input: "${input}", Image URI: ${currentImageUri || 'None'}`);

    const userMessage: Message = {
      id: Date.now(),
      text: userMessageText,
      sender: 'user',
      timestamp: Date.now(),
      imageUri: currentImageUri || undefined // Use captured URI
    };

    // Add user message to UI immediately
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setInput('');
    setImageUri(null); // Clear the image URI state *after* adding message
    if (currentImageUri) {
       addLog('handleSend: Cleared imageUri state after adding message to UI.');
    }
    setLoading(true);
    scrollToBottom();

    let finalResponseText = '';
    let placesToPresent: any[] | null = null;
    let currentLocationToUse: Location.LocationObject | null = null; // Moved declaration up
    let intentInfo: ExtractedIntentInfo = { intent: 'unknown' }; // Initialize intent

    try {
        // --- Get Location EARLY (Needed for both image and text paths) ---
        addLog('handleSend: Attempting to get current location (balanced)...');
        // Use default balanced settings first, as high accuracy might not always be needed
        currentLocationToUse = await getCurrentLocation();
        if (currentLocationToUse) {
            addLog(`handleSend: Using location: ${currentLocationToUse.coords.latitude.toFixed(4)}, ${currentLocationToUse.coords.longitude.toFixed(4)}`); // Fixed template literal syntax
        } else {
            addLog('handleSend: Proceeding without location info.');
        }

        // --- IMAGE PATH: Use OpenAI GPT-4o Vision ---
        if (currentImageUri) {
            addLog(`(Image Input) Processing image: ${currentImageUri}`); // Fixed template literal syntax
            if (!FileSystem) {
                addLog('(Image Input) Error: FileSystem module is not available.');
                finalResponseText = "抱歉，无法读取图像文件，FileSystem 模块不可用。";
            } else {
                try {
                    addLog('(Image Input) Reading image file as Base64...');
                    const imageBase64 = await FileSystem.readAsStringAsync(currentImageUri, {
                        encoding: FileSystem.EncodingType.Base64,
                    });
                    addLog(`(Image Input) Image Base64 read successfully, length: ${imageBase64.length}`); // Fixed template literal syntax

                    // Call the new OpenAI vision function, PASSING LOCATION
                    finalResponseText = await getGPT4oVisionResponse(
                        userMessageText,
                        imageBase64,
                        OPENAI_API_KEY,
                        currentLocationToUse, // <-- Pass location here
                        addLog
                    ) || "抱歉，分析图像时出错。"; // Provide fallback if null is returned

                    addLog(`(Image Input) OpenAI Vision response received (length: ${finalResponseText.length})`); // Fixed template literal syntax

                } catch (fileReadError) {
                    addLog(`(Image Input) Error reading image file: ${fileReadError}`); // Fixed template literal syntax
                    finalResponseText = "抱歉，读取图像文件时出错。请稍后再试。";
                }
            }
            // Clear place search context when image is the primary input
            setCurrentSearchContext(null);
            setCachedPlacesResults(null);
            setPresentedPlaceIds(new Set());

        } else {
          // --- TEXT-ONLY PATH: Use Existing Gemini Logic ---
          addLog('(Text Input) No image detected, proceeding with text-based logic.');

          // --- Language Detection ---
          let detectedLangCode = await detectLanguageWithAI(userMessageText, addLog);
          addLog(`(Text Input) Detected language: ${detectedLangCode}`); // Fixed template literal syntax

          // --- NEW: Language Override Logic using App Language ---
          const isShortInputText = userMessageText.split(' ').length <= 2;
          const detectedBaseLangText = detectedLangCode.split('-')[0];
          const appBaseLangText = currentAppLanguage.split('-')[0];

          if (isShortInputText && detectedBaseLangText !== appBaseLangText) {
              addLog(`OVERRIDE (Text): Short input detected as ${detectedLangCode} (base: ${detectedBaseLangText}), differs from app base lang ${appBaseLangText}. Forcing ${currentAppLanguage}.`);
              detectedLangCode = currentAppLanguage; // Override detected code
          }
          // --- END: Language Override Logic ---

          // --- Location was already acquired above ---
          // We can skip the specific location check here if needed,
          // as currentLocationToUse is already populated or null.
          const skipLocationLogic = isGenericQuery(userMessageText);
          if (skipLocationLogic) {
              addLog('(Text Input) Generic query check positive, location might not be used by intent logic.');
          }


          // --- Intent Recognition (Contextual) ---
          addLog('(Text Input) Analyzing user intent...');
          try {
               // Pass text history (updatedMessages) and the *current* search context state
              intentInfo = await extractPlaceInfoWithAI(userMessageText, updatedMessages, currentSearchContext, addLog);
              addLog(`(Text Input) Intent analysis result: ${JSON.stringify(intentInfo)}`); // Fixed template literal syntax
          } catch (intentError) {
              addLog(`(Text Input) Intent analysis error: ${intentError}`); // Fixed template literal syntax
              intentInfo = { intent: 'unknown' };
          }

          // --- Process based on Intent (Existing Logic - No changes needed here for now) ---
          switch (intentInfo.intent) {
              case 'initial_search':
                  addLog('(Text Input) Intent: initial_search. Performing search.');
                  // Reset previous context for a new text search
                  setCurrentSearchContext(null);
                  setCachedPlacesResults(null);
                  setPresentedPlaceIds(new Set());

                  // *** MODIFIED LOGIC: Check for locationName first ***
                  if (intentInfo.locationName && intentInfo.isNearby === false) {
                      // **Search within a specific location (e.g., Tokyo)**
                      addLog(`(Text Input) Searching within specific location: "${intentInfo.locationName}" for keyword: "${intentInfo.placeKeyword || '(none)'}"`); // Fixed template literal syntax
                      try {
                          const apiResults = await searchNearbyPlacesAPI_New(
                              intentInfo.placeKeyword || intentInfo.locationName, // Use keyword, fallback to location name itself
                              currentLocationToUse!, // Pass user's location (might be null, handled inside API)
                              API_KEY, // Google API Key for Places
                              3000,
                              addLog,
                              10,
                              intentInfo.locationName // *** Pass target location name ***
                          );
                          if (apiResults) {
                              addLog(`(Text Input - Specific Location) Search found ${apiResults.length} results.`); // Fixed template literal syntax
                              placesToPresent = apiResults;
                              // Set context reflecting the specific search
                              setCurrentSearchContext({
                                keyword: intentInfo.placeKeyword || intentInfo.locationName,
                                location: null, // Indicate context is not nearby-based
                                isNearbyQuery: false
                              });
                              setCachedPlacesResults(apiResults);
                              setPresentedPlaceIds(new Set(apiResults.map(p => p.place_id)));
                          } else {
                             addLog('(Text Input - Specific Location) Search returned null or empty.');
                          }
                      } catch (placesError) {
                          addLog(`(Text Input - Specific Location) Search API error: ${placesError}`); // Fixed template literal syntax
                      }
                  } else if (intentInfo.placeKeyword && currentLocationToUse) {
                      // **Nearby Search (Original Logic)**
                      addLog(`(Text Input) Performing nearby search for keyword: "${intentInfo.placeKeyword}"`); // Fixed template literal syntax
                      try {
                          const apiResults = await searchNearbyPlacesAPI_New(
                              intentInfo.placeKeyword,
                              currentLocationToUse,
                              API_KEY, // Google API Key for Places
                              3000,
                              addLog,
                              10,
                              undefined // *** Ensure targetLocationName is undefined for nearby search ***
                          );
                          if (apiResults) {
                              addLog(`(Text Input - Nearby) Search found ${apiResults.length} results.`); // Fixed template literal syntax
                              placesToPresent = apiResults;
                              setCurrentSearchContext({
                                keyword: intentInfo.placeKeyword,
                                location: currentLocationToUse,
                                isNearbyQuery: true
                              });
                              setCachedPlacesResults(apiResults);
                              setPresentedPlaceIds(new Set(apiResults.map(p => p.place_id)));
                          } else {
                             addLog('(Text Input - Nearby) Search returned null or empty.');
                          }
                      } catch (placesError) {
                          addLog(`(Text Input - Nearby) Search API error: ${placesError}`); // Fixed template literal syntax
                      }
                  } else {
                       addLog('(Text Input) Initial search intent missing required info (keyword+location for nearby, or specific location name).');
                  }
                  break;

              case 'more_recommendations':
                   addLog('(Text Input) Intent: more_recommendations. Treating as clarification for now.');
                   intentInfo.intent = 'clarification'; // Re-classify for AI prompt
                   // Fallthrough to reset context

              case 'generic_query':
              case 'context_switch':
              case 'clarification':
              case 'unknown':
                  addLog(`(Text Input) Intent: ${intentInfo.intent}. Resetting search context.`); // Fixed template literal syntax
                  setCurrentSearchContext(null);
                  setCachedPlacesResults(null);
                  setPresentedPlaceIds(new Set());
                  break;

              // --- handleSend: weather_query case ---
              case 'weather_query':
                  addLog('(Text Input) Intent: weather_query');
                  let weatherLocation: { lat: number; lon: number } | { cityName: string } | null = null;
                  let displayLocationName = 'your current location'; // Default display name
                  let weatherHandled = false; // Flag to ensure response is set

                  // 1. Determine Location
                  if (intentInfo.locationName) {
                      addLog(`Weather query for specific location: ${intentInfo.locationName}`); // Fixed template literal syntax
                      weatherLocation = { cityName: intentInfo.locationName };
                      displayLocationName = intentInfo.locationName;
                  } else if (currentLocationToUse) {
                      addLog(`Weather query for current location: ${currentLocationToUse.coords.latitude}, ${currentLocationToUse.coords.longitude}`); // Fixed template literal syntax
                      weatherLocation = { lat: currentLocationToUse.coords.latitude, lon: currentLocationToUse.coords.longitude };
                      // Display name remains 'your current location' or will be updated by weather API
                  } else {
                      addLog('Failed to get current location for weather query.');
                      finalResponseText = "抱歉，我需要您的位置信息才能查询当前天气。您可以指定一个城市，或者允许应用访问您的位置。";
                      weatherHandled = true; // Set flag as error response is ready
                  }

                  // Proceed only if location was determined
                  if (!weatherHandled && weatherLocation) {
                      // 2. Determine Forecast Type (Simplified for now, always current)
                      const forecastType = intentInfo.forecastType || 'current';
                      addLog(`Weather forecast type: ${forecastType}`); // Fixed template literal syntax

                      // 3. Call Weather API (Only current weather for now)
                      let weatherData: CurrentWeatherData | null = null;
                      let badWeatherWarning: string | null = null;
                      addLog(`Calling getCurrentWeatherData for ${JSON.stringify(weatherLocation)}`); // Fixed template literal syntax
                      weatherData = await getCurrentWeatherData(weatherLocation, OPENWEATHER_API_KEY, addLog);

                      // 4. Format Response using formatWeatherResponseWithAI
                      if (weatherData) {
                          addLog(`Weather data received for ${weatherData.name || displayLocationName}. Formatting...`); // Fixed template literal syntax
                          displayLocationName = weatherData.name || displayLocationName; // Update display name if available
                          badWeatherWarning = checkBadWeather(weatherData); // Check for bad weather

                          try {
                              finalResponseText = await formatWeatherResponseWithAI(
                                  weatherData,
                                  forecastType,
                                  displayLocationName,
                                  detectedLangCode, // <-- Use detected language
                                  addLog
                              );
                              if (badWeatherWarning) {
                                  addLog(`Prepending bad weather warning: ${badWeatherWarning}`); // Fixed template literal syntax
                                  finalResponseText = badWeatherWarning + "\n\n" + finalResponseText;
                              }
                              weatherHandled = true; // Mark as handled
                          } catch (formatError) {
                               addLog(`Error calling formatWeatherResponseWithAI: ${formatError}`); // Fixed template literal syntax
                               finalResponseText = `抱歉，格式化 ${displayLocationName} 的天气信息时出错。`; // Fixed template literal syntax
                               weatherHandled = true; // Mark as handled even on format error
                          }
                      } else {
                          addLog('Failed to get weather data from API.');
                          finalResponseText = `抱歉，无法获取 ${displayLocationName} 的天气信息。请稍后再试。`; // Fixed template literal syntax
                          weatherHandled = true; // Mark as handled
                      }
                  } else if (!weatherHandled) {
                      // Handle case where weatherLocation was null and no specific error message was set
                      addLog('Weather query failed due to missing location details.');
                      finalResponseText = "抱歉，无法确定您想查询天气的位置。";
                      weatherHandled = true; // Mark as handled
                  }

                  // Clear place search context
                  setCurrentSearchContext(null);
                  setCachedPlacesResults(null);
                  setPresentedPlaceIds(new Set());
                  break; // Break after handling weather query

              // ... other cases ...
          } // End Switch

          // --- AI Response Generation (Gemini for Text) ---
          // Only call Gemini if no specific response was generated by the switch cases (e.g., weather handled it)
          if (!finalResponseText && intentInfo.intent !== 'weather_query') {
            addLog(`(Text Input) No specific response generated yet. Intent: ${intentInfo.intent}. Calling getAIResponse (Gemini).`); // Fixed template literal syntax
            try {
              finalResponseText = await getAIResponse(
                updatedMessages, // Use updatedMessages for full history
                userMessageText,
                detectedLangCode, // <-- Use detected language
                intentInfo, // Pass the determined intent (could be generic, clarification etc.)
                currentLocationToUse,
                addLog,
                placesToPresent, // Will be null/empty for non-search intents
                'text'
              );
              addLog(`(Text Input) getAIResponse (Gemini) call successful. Response length: ${finalResponseText?.length || 0}`); // Fixed template literal syntax
            } catch (aiError) {
              addLog(`(Text Input) Error calling getAIResponse (Gemini): ${aiError instanceof Error ? aiError.message : aiError}`); // Fixed template literal syntax
              if (aiError instanceof Error && aiError.stack) {
                addLog(`(Text Input) getAIResponse error stack: ${aiError.stack}`); // Fixed template literal syntax
              }
              finalResponseText = '抱歉，我在思考时遇到了点问题。请稍后再试。'
            }
          } // End Gemini Call block

        } // --- End TEXT-ONLY PATH ---

    } catch (error) {
        // Enhanced error logging for the main try block
        addLog(`*** FATAL ERROR in handleSend main logic: ${error instanceof Error ? error.message : error} ***`); // Fixed template literal syntax
        if (error instanceof Error && error.stack) {
            addLog(`handleSend error stack: ${error.stack}`); // Fixed template literal syntax
        }
        finalResponseText = "处理您的请求时发生了一个意外错误。请检查日志。";
    }

    // --- Display final result ---
    // Ensure finalResponseText is never truly null/undefined, AND not just whitespace before setting state
    const finalTrimmedResponse = typeof finalResponseText === 'string' ? finalResponseText.trim() : '';
    const responseToDisplay = finalTrimmedResponse.length > 0
                            ? finalTrimmedResponse
                            : "抱歉，无法获取有效的回复，请稍后再试。"; // Default message if empty/null
    addLog(`Displaying final response (length: ${responseToDisplay.length}): "${responseToDisplay.substring(0, 100)}..."`); // Log actual displayed text
    setMessages(prev => [...prev, { id: Date.now() + 1, text: responseToDisplay, sender: 'gemini', timestamp: Date.now() }]);
    setLoading(false);
      setTimeout(scrollToBottom, 100);
  };

  // --- Fix Linter Error: Rename handleSuggestionPressInternal back --- 
  const handleSuggestionPress = async (suggestion: string, index: number) => {
    setSelectedSuggestion(index);
    const userMessage: Message = {
      id: Date.now(), 
      text: suggestion, 
      sender: 'user',
      timestamp: Date.now()
    };
    setMessages([...messages, userMessage]);
    setInput(''); 
    setImageUri(null); 
    setLoading(true);
    scrollToBottom();
    
    let finalResponseText = '';
    let placesApiResults: any[] | null = null;
    let currentLocationToUse: Location.LocationObject | null = null;

    try {
        // --- NEW: Detect language of the suggestion text EARLY --- << MOVE HERE
        let detectedLangCodeSuggestion = await detectLanguageWithAI(suggestion, addLog);
        addLog(`(Suggestion) Detected language: ${detectedLangCodeSuggestion}`);
        
        // --- NEW: Language Override Logic for Suggestions ---
        const isShortInputSuggestion = suggestion.split(' ').length <= 2;
        const detectedBaseLangSuggestion = detectedLangCodeSuggestion.split('-')[0];
        const appBaseLangSuggestion = currentAppLanguage.split('-')[0];

        if (isShortInputSuggestion && detectedBaseLangSuggestion !== appBaseLangSuggestion) {
            addLog(`OVERRIDE (Suggestion): Short input detected as ${detectedLangCodeSuggestion} (base: ${detectedBaseLangSuggestion}), differs from app base lang ${appBaseLangSuggestion}. Forcing ${currentAppLanguage}.`);
            detectedLangCodeSuggestion = currentAppLanguage; // Override detected code
        }
        // --- END: Language Override Logic ---

        // First, check if this is a weather query
        const placesKeyword = extractPlacesKeyword(suggestion);
        addLog(`(Suggestion) Extracted keyword: "${placesKeyword}"`);
      
        if (placesKeyword === 'weather') {
            // Handle as weather query
            addLog('(Suggestion) Handling as weather query');
            let weatherHandledSuggestion = false; // Local flag for this block
            
            // --- Weather Query Flow ---
            addLog('(Weather Query - Suggestion) Attempting to get location...');
            currentLocationToUse = await getCurrentLocation(); // Use default for suggestions
            
            let displayLocationNameSuggestion = 'your current location';
            let weatherLocationSuggestion: { lat: number; lon: number } | null = null;

            if (currentLocationToUse) {
                 addLog(`(Weather Query - Suggestion) Using location: ${currentLocationToUse.coords.latitude}, ${currentLocationToUse.coords.longitude}`);
                 weatherLocationSuggestion = { lat: currentLocationToUse.coords.latitude, lon: currentLocationToUse.coords.longitude };
            } else {
                addLog('(Weather Query - Suggestion) Location not available.');
                finalResponseText = "抱歉，我需要您的位置信息才能查询当前天气。";
                weatherHandledSuggestion = true;
            }

            // Proceed only if location was obtained
            if (!weatherHandledSuggestion && weatherLocationSuggestion) {
                // Reset context for weather query
                setCurrentSearchContext(null);
                setCachedPlacesResults(null);
                setPresentedPlaceIds(new Set());
                
                // Get weather data
                let weatherData = null;
                addLog(`(Weather Query - Suggestion) Getting weather for location: ${weatherLocationSuggestion.lat}, ${weatherLocationSuggestion.lon}`);
                weatherData = await getCurrentWeatherData(weatherLocationSuggestion, OPENWEATHER_API_KEY, addLog);
                
                if (weatherData) {
                    addLog(`(Weather Query - Suggestion) Weather data received for ${weatherData.name}. Formatting...`);
                    displayLocationNameSuggestion = weatherData.name || displayLocationNameSuggestion;
                    const badWeatherWarning = checkBadWeather(weatherData);
                    
                    try {
                         finalResponseText = await formatWeatherResponseWithAI(
                             weatherData,
                             'current', // Suggestions are always for current weather
                             displayLocationNameSuggestion,
                             detectedLangCodeSuggestion, // <-- Use the correct variable
                             addLog
                         );
                         if (badWeatherWarning) {
                             addLog(`(Weather Query - Suggestion) Prepending bad weather warning: ${badWeatherWarning}`);
                             finalResponseText = badWeatherWarning + "\n\n" + finalResponseText;
                         }
                         weatherHandledSuggestion = true;
                    } catch (formatError) {
                         addLog(`(Weather Query - Suggestion) Error calling formatWeatherResponseWithAI: ${formatError}`);
                         finalResponseText = `抱歉，格式化 ${displayLocationNameSuggestion} 的天气信息时出错。`;
                         weatherHandledSuggestion = true;
                    }
                } else {
                     addLog('(Weather Query - Suggestion) Failed to get weather data from API.');
                     finalResponseText = `抱歉，无法获取 ${displayLocationNameSuggestion} 的天气信息。请稍后再试。`;
                     weatherHandledSuggestion = true;
                }
            }
            // End of weather handling block
            
        } else {
            // Handle as location query (original code)
            // Suggestions are generally location-aware, so no Smart Skip here.
            
            // --- Location Acquisition (Balanced Plan B) ---
            addLog('(Suggestion) Attempting to get location (Cache/Balanced first)...');
            const cachedOrBalancedLocation = await getCurrentLocation();

            const requiresHighAccuracy = suggestion.toLowerCase().includes('nearby') || suggestion.toLowerCase().includes('附近') || suggestion.toLowerCase().includes('local'); // Added 'local'
            let highAccuracyLocation: Location.LocationObject | null = null;

            if (requiresHighAccuracy && !cachedOrBalancedLocation) {
                addLog('(Suggestion) Balanced location failed/timeout, retrying with High Accuracy...');
                const preciseLocationMessage = "[系统] 正在尝试获取更精确的位置...";
                setMessages(prev => [...prev, { id: Date.now()+0.5, text: preciseLocationMessage, sender: 'gemini', timestamp: Date.now() }]);
                scrollToBottom();
                highAccuracyLocation = await getCurrentLocation({ highAccuracy: true });
            }

            currentLocationToUse = highAccuracyLocation || cachedOrBalancedLocation;

            if (requiresHighAccuracy && !currentLocationToUse) {
                finalResponseText = "[系统] 无法获取足够精确的位置，无法提供附近推荐。";
                setLoading(false);
                setMessages(prev => [...prev, { id: Date.now()+0.6, text: finalResponseText, sender: 'gemini', timestamp: Date.now() }]);
                setSelectedSuggestion(null);
                scrollToBottom();
                return;
            } else if (currentLocationToUse) {
                addLog(`(Suggestion) Using location: ${currentLocationToUse.coords.latitude}, ${currentLocationToUse.coords.longitude}`);
                // --- Call Places API Search (with extracted keyword) ---
                // Suggestions are treated as initial search intent
                addLog(`(Suggestion) Calling NEW Places API with keyword: "${placesKeyword}"`);
                placesApiResults = await searchNearbyPlacesAPI_New( // Request up to 10 for suggestions
                    placesKeyword, 
                    currentLocationToUse, 
                    API_KEY, 
                    3000, 
                    addLog,
                    10 
                );
            } else {
                addLog('(Suggestion) Proceeding without location info after initial attempt(s).');
            }
            
            // --- Call AI --- 
            // Detect language of the suggestion text
            const detectedLangCodeSuggestion = await detectLanguageWithAI(suggestion, addLog);
            addLog(`(Weather Query) Detected language: ${detectedLangCodeSuggestion}`);
            
            // --- Define Intent for Suggestion (Treat as initial search) ---
            const intentInfoSuggestion: ExtractedIntentInfo = {
                intent: 'initial_search',
                placeKeyword: placesKeyword, // Use extracted keyword
                isNearby: suggestion.toLowerCase().includes('nearby') // Simple check for suggestions
            };
            addLog(`(Suggestion Input) Assigned Intent: ${JSON.stringify(intentInfoSuggestion)}`);

            // --- Reset Context for Suggestion Click ---
            // Clicking a suggestion starts a new context
            setCurrentSearchContext(null);
            setCachedPlacesResults(null);
            setPresentedPlaceIds(new Set());
            if (placesApiResults) {
                // Update context based on the suggestion search results
                setCurrentSearchContext({ 
                  keyword: intentInfoSuggestion.placeKeyword || null, 
                  location: currentLocationToUse, 
                  isNearbyQuery: intentInfoSuggestion.isNearby === true // <-- Ensure boolean
                });
                setCachedPlacesResults(placesApiResults);
                setPresentedPlaceIds(new Set(placesApiResults.map(p => p.place_id)));
            }

            // Call getAIResponse only for non-weather suggestions
            addLog('(Suggestion) Calling getAIResponse for location suggestion...');
            const aiSummaryText = await getAIResponse(
                [...messages, userMessage], // History including the suggestion click
                suggestion,               // The suggestion text itself as user input
                detectedLangCodeSuggestion, // <-- Use detected language
                intentInfoSuggestion,     // Pass the assigned intent
                currentLocationToUse,     
                addLog,                 
                placesApiResults,         // Pass results found for the suggestion
                'text'                    // Treat suggestion response like text
            );

            // --- Format Output --- 
            if (currentLocationToUse && placesApiResults) {
                finalResponseText = formatSummaryWithLinks(aiSummaryText, placesApiResults, addLog);
            } else {
                finalResponseText = aiSummaryText;
            }
        }
    } catch (error) {
        addLog(`Error in handleSuggestionPress logic: ${error}`);
        finalResponseText = "处理您的建议时发生意外错误。";
    }
    
    // --- Display Result --- 
    // Final check similar to handleSend
    const finalTrimmedResponseSuggestion = typeof finalResponseText === 'string' ? finalResponseText.trim() : '';
    const responseToDisplaySuggestion = finalTrimmedResponseSuggestion.length > 0 
                                        ? finalTrimmedResponseSuggestion 
                                        : "抱歉，无法处理此建议，请稍后再试。"; // Default suggestion error
    setMessages(prev => [...prev, { id: Date.now() + 1, text: responseToDisplaySuggestion, sender: 'gemini', timestamp: Date.now() }]);
    setLoading(false);
    setSelectedSuggestion(null); 
    setTimeout(scrollToBottom, 100);
  };

  const scrollToBottom = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  };

  const renderImageMessage = (uri: string) => {
    return (
      <View style={styles.imageMessageContainer}>
        <Image 
          source={{ uri }} 
          style={styles.imageMessage}
          resizeMode="cover"
        />
      </View>
    );
  };

  // 添加刷新状态
  const [refreshUI, setRefreshUI] = useState(0);
  const [debugLogs, setDebugLogs] = useState([]);
  const [showDebugLogs, setShowDebugLogs] = useState(false);
  
  // 添加音频播放状态
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  
  // 日志函数
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] ${message}`;
    console.log(logEntry);
    setDebugLogs(prev => {
      // 如果日志太多，只保留最近的100条
      const newLogs = [...prev, logEntry];
      return newLogs.length > 100 ? newLogs.slice(newLogs.length - 100) : newLogs;
    });
  }, []); // 依赖项为空，稳定

  // 清理录音函数 - 依赖于 addLog
  const cleanupRecording = useCallback(async (reason: string) => {
    addLog(`cleanupRecording called. Reason: ${reason}`);
    // 1. Clear VAD timer
    if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
        silenceTimerRef.current = null;
        addLog("Cleanup: VAD timer cleared.");
    }
    // 2. Resolve any pending promise
    if (recordAudioPromiseResolveRef.current) {
        addLog("Cleanup: Resolving pending promise with null.");
        recordAudioPromiseResolveRef.current(null);
        recordAudioPromiseResolveRef.current = null;
    }
    // 3. Stop/unload recording
    const currentRecording = recordingRef.current;
    if (currentRecording) {
        recordingRef.current = null; // Nullify ref first
        addLog("Cleanup: recordingRef set to null.");
        try {
            addLog("Cleanup: Attempting stopAndUnloadAsync...");
            await currentRecording.stopAndUnloadAsync();
            addLog("Cleanup: stopAndUnloadAsync succeeded.");
        } catch (error: any) {
            addLog(`Cleanup: stopAndUnloadAsync failed (likely expected if not prepared/already stopped): ${error.message || error}`);
        }
    } else {
        addLog("Cleanup: No active recording object found in ref.");
    }
    // 4. Reset listening state (if not unmounted)
    if (!isUnmountedRef.current) {
        addLog("Cleanup: Setting isListening = false.");
        setIsListening(false);
    } else {
        addLog("Cleanup: Component unmounted, skipping state update.");
    }
    // 清理录音循环 timer
    if (listeningLoopTimerRef.current) {
      clearTimeout(listeningLoopTimerRef.current);
      listeningLoopTimerRef.current = null;
      addLog("Cleanup: listeningLoopTimerRef cleared.");
    }
  }, [addLog]); // 依赖于稳定的 addLog
  
  // 强制刷新UI
  const forceRefresh = () => {
    setRefreshUI(prev => prev + 1);
  };
  
  // 在组件挂载时显示提示
  useEffect(() => {
    // 组件挂载时，添加一条通知消息
    const timeoutId = setTimeout(() => {
      Alert.alert(
        "AI实时聊天已启用",
        "点击麦克风图标可进入实时AI语音聊天功能。使用真实API连接，可以进行真实对话。",
        [{ text: "我知道了" }]
      );
    }, 1000);
    
    // 初始化状态
    addLog('组件已挂载，初始化语音聊天状态');
    setIsListening(false);
    setIsVoicePaused(false);
    setIsPlayingAudio(false);
    setVoiceChatMessages([]);
    
    return () => clearTimeout(timeoutId);
  }, []);
  
  // 暂停/继续实时语音聊天
  const toggleVoiceRecording = async () => {
    addLog(`toggleVoiceRecording被调用，当前状态isListening=${isListening}, isProcessingVoice=${isProcessingVoice}`);
    setShowVoiceChatModal(true);
    
    // 只在应用首次进入语音助手时播放欢迎语
    if (!welcomePlayed) {
      const welcomeMessage = "欢迎使用iGuided语音助手，您可以询问我任何旅行相关的问题。";
      setVoiceChatMessages([
        "[系统] AI语音助手已启动",
        `[AI] ${welcomeMessage}`
      ]);
      setWelcomePlayed(true); // 标记已播放
      
      // 稍后播放欢迎音频
      setTimeout(async () => {
        try {
          addLog('尝试播放欢迎音频');
          const welcomeAudio = await textToSpeechAPI(welcomeMessage);
          if (welcomeAudio) {
            await playAudio(welcomeAudio);
          }
        } catch (error) {
          addLog(`播放欢迎音频失败: ${error}`);
        }
      }, 500);
    } else if (voiceChatMessages.length === 0) {
        // 如果不是首次进入，但消息为空，可以只加一条系统提示
         setVoiceChatMessages(["[系统] AI语音助手已就绪"]);
    }
    
        forceRefresh();
        
    // 如果正在处理语音，不允许状态切换，防止冲突
    if (isProcessingVoice) {
      addLog('正在处理语音，忽略切换请求');
          return;
        }
        
    if (isListening) {
      // 如果正在录音，则停止
      addLog('停止录音');
      setIsListening(false);
      setVoiceChatMessages(prev => [...prev, "[系统] 录音已停止"]);
      
      // 确保UI立即更新
        forceRefresh();
          } else {
      // 默认不自动开始录音，让用户在语音助手界面中手动点击麦克风按钮开始录音
      addLog('语音聊天窗口已打开，等待用户点击麦克风按钮开始录音');
    }
  };

  // 关闭实时语音聊天
  const closeVoiceChat = async () => {
    addLog('关闭语音聊天，重置所有状态');
    
    // 停止当前可能在播放的音频
    if (playbackSoundRef.current) {
      addLog('关闭窗口时停止当前播放的音频...');
      try {
        await playbackSoundRef.current.stopAsync();
        await playbackSoundRef.current.unloadAsync();
        addLog('播放已停止并卸载。');
      } catch (e) {
        addLog(`停止播放时出错: ${e}`);
      }
      playbackSoundRef.current = null;
    }
    
    setIsPlayingAudio(false); // 确保播放状态重置
    setIsListening(false);
    setIsVoicePaused(false);
    // setVoiceChatMessages([]); // 暂时注释掉，看是否需要保留记录
    await cleanupRecording('CLOSE_VOICE_CHAT'); 
    forceRefresh();
    setTimeout(() => {
      setShowVoiceChatModal(false);
      forceRefresh();
    }, 300);
  };

  // Process captured image
  const processCapturedImage = async (imageAsset: ImagePickerAsset | string): Promise<void> => {
    try {
      setImageUri(typeof imageAsset === 'string' ? imageAsset : imageAsset.uri);
      
      // Additional image processing can be added here
      
        } catch (error) {
      console.error("Error processing image:", error);
      Alert.alert("Error", "Failed to process image");
    }
  };

  // 提供测试录音功能
  const testAudioRecording = async () => {
    addLog('开始测试录音功能 (Opus格式)...');
    setVoiceChatMessages(prev => [...prev, "[系统] 测试录音中 (Opus)..."]);
    
    try {
      // 检查权限
      await checkPermission();
      
      // 检查并设置音频模式
      await setupAudioMode();
      
      // 创建录音文件路径
      const audioPath = `${FileSystem.documentDirectory}test_recording.ogg`; // 使用.ogg扩展名
      addLog(`录音将保存至: ${audioPath}`);
      
      // 检查录音文件是否已存在，如果存在则删除
      const fileInfo = await FileSystem.getInfoAsync(audioPath);
      if (fileInfo.exists) {
        addLog('删除已存在的测试录音文件');
        await FileSystem.deleteAsync(audioPath);
      }
      
      // 配置录音选项 - 使用Opus配置
      const recordingOptions: any = {
        android: {
          extension: '.ogg',
          outputFormat: 7,  // OPUS
          audioEncoder: 7,  // OPUS
          sampleRate: 16000,
          numberOfChannels: 1,
          bitRate: 32000,
        },
        ios: {
          extension: '.opus',
          outputFormat: 18, // OPUS
          audioQuality: Audio.IOSAudioQuality.MEDIUM,
          sampleRate: 16000,
          numberOfChannels: 1,
          bitRate: 32000,
        },
        web: {
          mimeType: 'audio/opus',
          bitsPerSecond: 32000,
        },
      };
      
      // 创建录音对象
      const recording = new Audio.Recording();
      await recording.prepareToRecordAsync(recordingOptions);
      
      // 开始录音
      addLog('开始测试录音');
      await recording.startAsync();
      
      // 5秒后停止录音
      setTimeout(async () => {
      try {
        // 停止录音
          addLog('停止测试录音');
        await recording.stopAndUnloadAsync();
      
      // 获取录音URI
          const uri = recording.getURI() || '';
          addLog(`测试录音完成，URI: ${uri}`);
          
          // 尝试读取录音文件信息
          const recordingInfo = await FileSystem.getInfoAsync(uri);
          addLog(`录音文件信息: 存在=${recordingInfo.exists}, 大小=${recordingInfo.size || 'unknown'}`);
          
          // 检查文件格式
          if (recordingInfo.exists && recordingInfo.size > 0) {
            const base64 = await FileSystem.readAsStringAsync(uri, { encoding: FileSystem.EncodingType.Base64 });
            const fileHeader = base64.substring(0, 100);
            const isOggFormat = fileHeader.startsWith('T2dnUw'); // "OggS" in Base64
            
            addLog(`文件格式检测: Ogg/Opus=${isOggFormat}, 大小=${base64.length}`);
            addLog(`文件头部信息(Base64): ${fileHeader}`);
            
            if (isOggFormat) {
              addLog('成功: 录音文件是有效的Ogg/Opus格式');
            } else {
              addLog('警告: 录音文件可能不是标准Ogg/Opus格式');
            }
          }
          
          // 尝试播放录音 (注意：并非所有设备都能原生播放 .ogg/.opus)
          try {
            addLog('尝试播放测试录音 (可能不兼容)...');
            const sound = new Audio.Sound();
            await sound.loadAsync({ uri });
            await sound.playAsync();
            
            // 播放完毕后释放资源
            sound.setOnPlaybackStatusUpdate((status) => {
              if (status.isLoaded && status.didJustFinish) {
                sound.unloadAsync();
                addLog('测试录音播放完成并释放资源');
              }
            });
            
            setVoiceChatMessages(prev => [...prev, "[系统] 测试录音成功并已播放 (若设备支持)"]);
          } catch (playError) {
            addLog(`播放测试录音失败: ${playError}`);
            setVoiceChatMessages(prev => [...prev, `[系统] 测试录音成功但播放失败 (可能格式不兼容): ${playError}`]);
          }
        } catch (stopError) {
          addLog(`停止测试录音失败: ${stopError}`);
          setVoiceChatMessages(prev => [...prev, `[系统] 测试录音失败: ${stopError}`]);
        }
      }, 5000);
      
      setVoiceChatMessages(prev => [...prev, "[系统] 录音中，请说话，5秒后自动停止..."]);
    } catch (error) {
      addLog(`测试录音功能失败: ${error}`);
      setVoiceChatMessages(prev => [...prev, `[系统] 测试录音失败: ${error}`]);
    }
  };

  // 生成模拟音频数据（用于欢迎语等场景）
  const generateMockAudio = async (text: string): Promise<string | null> => {
    try {
      addLog(`为文本生成音频: "${text}"`);
      
      // 调用实际的TTS API
      return await textToSpeechAPI(text);
    } catch (error) {
      addLog(`生成音频失败: ${error}`);
      return null;
    }
  };

  // 检查录音权限
  const checkPermission = async (): Promise<boolean> => {
    try {
      addLog('检查录音权限');
      const { status: existingStatus } = await Audio.getPermissionsAsync();
      
      addLog(`已有权限状态: ${existingStatus}`);
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        addLog('请求录音权限');
        const { status } = await Audio.requestPermissionsAsync();
        finalStatus = status;
        addLog(`权限请求结果: ${status}`);
      }
      
      return finalStatus === 'granted';
    } catch (error) {
      addLog(`检查/请求权限时出错: ${error}`);
      return false;
    }
  };

  // 设置音频模式
  const setupAudioMode = async (): Promise<boolean> => {
    try {
      addLog('设置音频模式');
      
      // 直接设置音频模式
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        interruptionModeIOS: InterruptionModeIOS.DoNotMix, // Use imported enum/value
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        interruptionModeAndroid: InterruptionModeAndroid.DoNotMix, // Use imported enum/value
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false, // Important for VAD loop
      });
      addLog('音频模式设置成功');
      return true;
    } catch (error) {
      addLog(`设置音频模式失败: ${error}`);
      return false;
    }
  };
  
  // 在组件中添加 playAudio 函数 (修改为返回 Promise)
  const playAudio = async (base64Audio: string): Promise<void> => {
    // --- Promise Wrapper --- 
    return new Promise(async (resolve) => {
      // 先停止可能存在的旧播放
      if (playbackSoundRef.current) {
        addLog('playAudio: 停止旧的播放实例...');
        try {
          await playbackSoundRef.current.stopAsync();
          await playbackSoundRef.current.unloadAsync();
        } catch (e) { addLog(`playAudio: 停止旧播放时出错: ${e}`); }
        playbackSoundRef.current = null;
      }
      
      if (isPlayingAudio) {
        addLog('playAudio: 已有音频正在播放，取消本次播放请求'); // Change message slightly
        resolve(); // Resolve immediately if already playing
        return;
      }
      
      addLog(`playAudio: 开始播放音频，数据长度: ${base64Audio.length}`);
      setIsPlayingAudio(true);
      forceRefresh(); 
      
      if (!FileSystem) {
        addLog('playAudio Error: FileSystem模块不可用');
        setIsPlayingAudio(false);
        forceRefresh();
        resolve(); // Resolve on error
        return;
      }
      
      const fileUri = `${FileSystem.documentDirectory}temp_audio.mp3`;
      let soundObject: any | null = null; // Declare here for broader scopea
      
      try {
        addLog(`playAudio: 将Base64音频写入临时文件: ${fileUri}`);
        await FileSystem.writeAsStringAsync(fileUri, base64Audio, {
          encoding: FileSystem.EncodingType.Base64,
        });

        const fileInfo = await FileSystem.getInfoAsync(fileUri);
        addLog(`playAudio: 临时文件信息: ${JSON.stringify(fileInfo)}`);
        if (!fileInfo.exists || fileInfo.size === 0) {
          throw new Error('临时文件未成功创建或为空');
        }

        addLog('playAudio: 创建音频对象...');
        soundObject = new Audio.Sound();
        playbackSoundRef.current = soundObject; // Store ref *before* loading/playing
          
        addLog(`playAudio: 加载音频文件: ${fileUri}`);
        await soundObject.loadAsync({ uri: fileUri });
        await soundObject.setVolumeAsync(1.0);
        addLog('playAudio: 开始播放音频...');
        
        // Set status update callback *before* playing
        soundObject.setOnPlaybackStatusUpdate((status: any) => {
          if (!status.isLoaded) {
            // Handle unload or initial load error state
            if (status.error) {
              addLog(`playAudio Status Error (Not Loaded): ${status.error}`);
        setIsPlayingAudio(false);
              playbackSoundRef.current = null; 
        forceRefresh();
              resolve(); // Resolve on error
            }
        return;
      }
      
          // Playback finished successfully
          if (status.didJustFinish) {
            addLog('playAudio: 音频播放完成');
            setIsPlayingAudio(false);
            playbackSoundRef.current = null; 
            forceRefresh();
            try {
               // Ensure unload is attempted, but don't await inside callback directly
               soundObject?.unloadAsync().catch(unloadError => addLog(`playAudio: 卸载音频对象错误: ${unloadError}`)); 
            } catch (e) { /* ignore */ }
            resolve(); // Resolve on success
          } else if (status.error) { // Handle playback error
             addLog(`playAudio: 播放时发生错误: ${status.error}`);
          setIsPlayingAudio(false);
             playbackSoundRef.current = null; 
          forceRefresh();
             try {
                 soundObject?.unloadAsync().catch(unloadError => addLog(`playAudio: 卸载音频对象错误(播放错误后): ${unloadError}`));
             } catch (e) { /* ignore */ }
             resolve(); // Resolve on error
          }
        });

        // Now play
        await soundObject.playAsync();
        
        // Safety timeout (optional but recommended)
        // setTimeout(() => { ... handle timeout ... resolve(); }, 15000); 
          
      } catch (audioError) {
        addLog(`playAudio: 创建或播放音频对象错误: ${audioError}`);
        setIsPlayingAudio(false);
        if (playbackSoundRef.current === soundObject) { // Only clear ref if it's the one that failed
            playbackSoundRef.current = null; 
        }
        // Attempt to unload if sound object was created
        if (soundObject) {
             try { 
                  await soundObject.unloadAsync(); 
                  addLog('playAudio: Error caught, unloaded sound object.');
             } catch (e) { addLog(`playAudio: Error caught, failed to unload sound object: ${e}`); }
        }
        forceRefresh();
        resolve(); // Resolve even if error occurred during setup/play
      }
    }); // End of new Promise
  };

  // VAD (Voice Activity Detection) related constants and refs
  const silenceThreshold = -35; // dBFS (Adjust as needed, more negative means quieter)
  const silenceDurationThreshold = 1500; // ms (Duration of silence to trigger stop)
  const minRecordingDuration = 1000; // ms (Minimum time to record before VAD can stop)
  const recordingUpdateInterval = 300; // ms (How often to check metering)

  const silenceTimerRef = useRef(null);
  const recordingStartTimeRef = useRef(null);
  // Ref to hold the resolve function of the recordAudio promise
  const recordAudioPromiseResolveRef = useRef(null);
  const isUnmountedRef = useRef(false); 

  // Helper function to stop recording, process audio, and resolve the promise
  // Renamed to avoid conflict with existing stopRecording
  const stopRecordingVAD = useCallback(async (recording: any | null, reason: string) => {
    addLog(`stopRecordingVAD called. Reason: ${reason}`);

    // --- Cleanup Timer ---
    if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
        silenceTimerRef.current = null;
        addLog("VAD: Silence timer cleared by stop function.");
    }

    // --- Check if recording object is valid ---
    if (!recording) {
        addLog("VAD Stop: Attempted to stop a null recording object.");
        if (recordAudioPromiseResolveRef.current) {
            recordAudioPromiseResolveRef.current(null);
            recordAudioPromiseResolveRef.current = null;
        }
      return;
    }
    
    // --- Prevent stopping too early (only for VAD reason) ---
    const now = Date.now();
    if (reason === 'VAD_SILENCE' && recordingStartTimeRef.current && now - recordingStartTimeRef.current < minRecordingDuration) {
         addLog(`VAD Stop: Silence detected but recording duration (${now - recordingStartTimeRef.current}ms) < min (${minRecordingDuration}ms). Ignoring stop.`);
         // Don't resolve the promise yet, let recording continue
         return;
    }

    // --- Check if recording is actually running ---
    let status: any | null = null;
    try {
        status = await recording.getStatusAsync();
        if (!status.isRecording) {
            addLog(`VAD Stop: Recording (URI: ${recording.getURI()}) is already stopped or not prepared. Aborting stop action.`);
             // Resolve null if the promise is still pending
            if (recordAudioPromiseResolveRef.current) {
                recordAudioPromiseResolveRef.current(null);
                recordAudioPromiseResolveRef.current = null;
            }
            // Try to clean up the main ref if it still points to this stopped recording
            if(recordingRef.current === recording) {
                 recordingRef.current = null;
            }
            return;
        }
         addLog(`VAD Stop: Proceeding to stop recording (Duration: ${status.durationMillis}ms)`);
    } catch (statusError) {
         addLog(`VAD Stop: Error getting recording status before stop: ${statusError}. Attempting stop anyway.`);
    }


    // --- Resolve Promise & Cleanup State ---
    // Grab the resolver *before* clearing refs or doing async work
    const resolvePromise = recordAudioPromiseResolveRef.current;
    // Clear refs immediately to prevent race conditions / double processing
    recordAudioPromiseResolveRef.current = null;
    if (recordingRef.current === recording) { // Ensure we only clear the ref if it's the one we're stopping
      recordingRef.current = null;
      addLog("VAD Stop: Cleared main recordingRef.");
    } else {
      addLog("VAD Stop: Warning - recording object passed to stop doesn't match main recordingRef.");
    }

    // --- Stop and Process Audio ---
    try {
        await recording.stopAndUnloadAsync();
        addLog(`VAD Stop: Recording stopped & unloaded successfully (${reason}).`);
        const uri = recording.getURI();
        addLog(`VAD Stop: Recording URI: ${uri}`);

        if (!uri || !FileSystem) {
          addLog('VAD Stop: Invalid URI or FileSystem unavailable.');
          if (resolvePromise) resolvePromise(null);
        return;
      }
      
        const fileInfo = await FileSystem.getInfoAsync(uri);
        if (!fileInfo.exists) {
            addLog('VAD Stop: Recording file does not exist after stop.');
             if (resolvePromise) resolvePromise(null);
            return;
        }
        addLog(`VAD Stop: File Info: Size=${fileInfo.size}, Exists=${fileInfo.exists}`);

        if (fileInfo.size === 0) {
            addLog('VAD Stop: Recording file is empty.');
             if (resolvePromise) resolvePromise(null);
             return;
        }

        addLog('VAD Stop: Reading file as Base64...');
        const base64 = await FileSystem.readAsStringAsync(uri, { encoding: FileSystem.EncodingType.Base64 });
        addLog(`VAD Stop: Base64 read success, length ${base64.length}.`);

        // Resolve the promise with the base64 data
        if (resolvePromise) {
            resolvePromise(base64);
        } else {
             addLog("VAD Stop: Warning - Promise resolver was null after processing audio.");
        }

    } catch (error) {
        addLog(`VAD Stop: Error during stop/unload/processing: ${error}`);
        // Resolve null if an error occurred
        if (resolvePromise) {
            resolvePromise(null);
        }
    }
  // Note: No 'finally' needed as refs are cleared before the try block
  }, [addLog]); // Add dependencies used inside useCallback

  // Callback for VAD status updates
  const handleRecordingStatusUpdate = useCallback((status: any) => {
    if (!status.isRecording) {
        if (silenceTimerRef.current) {
            clearTimeout(silenceTimerRef.current);
            silenceTimerRef.current = null;
            addLog('VAD Update: Recording stopped (externally?), VAD timer cleared.');
        }
        return;
    }

    if (status.metering === undefined) {
        // VAD cannot work without metering
        // Optional: Log this only once?
        // addLog("VAD Update: Metering data not available.");
      return;
    }
    
    // Log metering level less frequently or conditionally if needed
    // addLog(`VAD Update: Metering Level: ${status.metering.toFixed(2)} dBFS`);

    if (status.metering < silenceThreshold) {
        // Silence detected
        if (!silenceTimerRef.current) {
            // Start silence timer only if recording has passed minimum duration
             const now = Date.now();
             if (recordingStartTimeRef.current && now - recordingStartTimeRef.current >= minRecordingDuration) {
                  addLog(`VAD Update: Silence detected (level ${status.metering.toFixed(2)} < ${silenceThreshold}). Starting ${silenceDurationThreshold}ms timer.`);
                  silenceTimerRef.current = setTimeout(() => {
                      // Timer finished, attempt to stop. Critical: Pass the correct recording object.
                      // Accessing recordingRef.current here *might* be okay if state updates are fast enough,
                      // but it's slightly risky due to potential race conditions if the ref changes.
                      if (recordingRef.current) {
                           stopRecordingVAD(recordingRef.current, 'VAD_SILENCE');
    } else {
                           addLog("VAD Timer End: recordingRef was null, cannot stop.");
                           // Ensure promise is resolved if somehow still pending
                           if (recordAudioPromiseResolveRef.current) {
                                recordAudioPromiseResolveRef.current(null);
                                recordAudioPromiseResolveRef.current = null;
                           }
                      }
                  }, silenceDurationThreshold);
             } else {
                  // Log silence before min duration, but don't start timer
                  // addLog(`VAD Update: Silence detected but min duration not met.`);
             }
        }
    } else {
        // Sound detected
        if (silenceTimerRef.current) {
            // addLog(`VAD Update: Sound detected (level ${status.metering.toFixed(2)} >= ${silenceThreshold}). Clearing silence timer.`);
            clearTimeout(silenceTimerRef.current);
            silenceTimerRef.current = null;
        }
    }
  }, [addLog, stopRecordingVAD, silenceThreshold, silenceDurationThreshold, minRecordingDuration]); // Add dependencies

  // Refactored audio recording function with VAD
  const recordAudio = async (): Promise<string | null> => {
    addLog('recordAudio: Initiating VAD recording sequence...');
    // Reset VAD state refs safely
    if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
        silenceTimerRef.current = null;
    }
    recordingStartTimeRef.current = null;
    // Ensure previous promise resolver is cleared before setting a new one
    if (recordAudioPromiseResolveRef.current) {
         addLog("recordAudio: Warning - Previous promise resolver was not cleared. Clearing now.");
         recordAudioPromiseResolveRef.current(null); // Resolve previous with null if pending
    }
    recordAudioPromiseResolveRef.current = null;

    // --- Promise Wrapper ---
    return new Promise(async (resolve, reject) => {
        // Store the resolver for this specific recording attempt
        recordAudioPromiseResolveRef.current = resolve;
        addLog("recordAudio: Promise created, resolver stored.");

        // --- Cleanup potential existing recording ---
        if (recordingRef.current) {
            addLog('recordAudio: Cleaning up existing recordingRef before start.');
            try {
                await recordingRef.current.stopAndUnloadAsync();
                addLog('recordAudio: Existing recording stopped and unloaded.');
            } catch (cleanupError) {
                addLog(`recordAudio: Error cleaning up existing recording: ${cleanupError}`);
            }
            recordingRef.current = null;
        }

        // --- Main Recording Logic ---
        try {
            // Check permissions
            const permission = await checkPermission();
            if (!permission) {
                addLog('recordAudio: Permission not granted.');
                // Don't set state here, let calling function handle UI state
                resolve(null); // Resolve the promise with null
                recordAudioPromiseResolveRef.current = null; // Clear resolver
                return;
            }

           // Set Audio Mode and prepare recording
await setupAudioMode();

// 使用 Expo 内置预设高质量选项创建并启动录音
addLog('recordAudio: Creating recording using preset high quality...');
// @ts-ignore: RECORDING_OPTIONS_PRESET_HIGH_QUALITY 可能未包含在类型定义
const recording = new Audio.Recording();
await recording.prepareToRecordAsync((Audio as any).RECORDING_OPTIONS_PRESET_HIGH_QUALITY);
// 绑定 VAD 回调
recording.setOnRecordingStatusUpdate(handleRecordingStatusUpdate);
// 存储实例，启动录音
recordingRef.current = recording;
addLog('recordAudio: recordingRef set. Starting recording...');
await recording.startAsync();
recordingStartTimeRef.current = Date.now();
addLog('recordAudio: Recording started. VAD active.');

            // Promise will now be resolved by stopRecordingVAD (called by VAD or manual stop)
      
    } catch (error) {
            addLog(`recordAudio: Error during setup/start: ${error}`);
            if (recordingRef.current) { // Clean up ref if error happened after creation
                 try { await recordingRef.current.stopAndUnloadAsync(); } catch(e) {}
                 recordingRef.current = null;
            }
            // Resolve the promise with null if an error occurs
            if (recordAudioPromiseResolveRef.current === resolve) { // Ensure it's the correct resolver
                 resolve(null);
                 recordAudioPromiseResolveRef.current = null; // Clear resolver
            }
        }
    }); // End of new Promise
  }; // End of recordAudio function

  // 新增 listeningLoopTimerRef 用于保存录音循环的 setTimeout
  const listeningLoopTimerRef = useRef(null);

  // Helper to create AI context history from voice messages
  const getVoiceHistory = (voiceMessages: string[]): Message[] => {
    const history: Message[] = [];
    // Process the last N messages, filtering system messages
    const relevantMessages = voiceMessages.slice(-6); // Look at last 6 lines
    
    relevantMessages.forEach((msg, index) => {
      if (msg.startsWith('[用户]')) {
        history.push({
          id: Date.now() + index,
          text: msg.replace('[用户]', '').trim(),
          sender: 'user',
          timestamp: Date.now() + index
        });
      } else if (msg.startsWith('[AI]')) {
        history.push({
          id: Date.now() + index,
          text: msg.replace('[AI]', '').trim(),
          sender: 'gemini',
          timestamp: Date.now() + index
        });
      } // Ignore [系统] messages
    });
    return history;
  };

  // --- 修改 startListeningLoop --- (区分屏幕显示和语音播报文本)
  const startListeningLoop = async () => {
    try {
      // 设置处理中状态，防止重复启动
      setIsProcessingVoice(true);
      forceRefresh();
      
      // 获取位置（不需要高精度）
      const userLocation = await getCurrentLocation();

      // 1. 录音并获取 Base64 音频
      addLog('开始录音并侦测语音...');
      setVoiceChatMessages(prev => [...prev, "[系统] 开始收听，请说话..."]);
    forceRefresh();
    
      const audioBase64 = await recordAudio();
      
      // 如果录音被中断或出错，退出循环
      if (!audioBase64) {
        addLog('录音返回空数据或被取消。');
        setVoiceChatMessages(prev => [...prev, "[系统] 录音被取消或无声音检测到"]);
        setIsProcessingVoice(false);
    setIsListening(false);  
      forceRefresh();
        return;
      }
      
      // 2. STT (Speech to Text) -- 上传音频到服务器并获取文本
      addLog(`录音完成，音频数据长度: ${audioBase64.length}。开始语音识别...`);
      setVoiceChatMessages(prev => [...prev, "[系统] 正在识别您的语音..."]);
    forceRefresh();
    
      const recognizedText = await uploadAudioForStt(audioBase64);
      
      if (!recognizedText) {
        addLog('语音识别失败或返回空文本。');
        setVoiceChatMessages(prev => [...prev, "[系统] 无法识别语音或服务器连接失败"]);
        setIsProcessingVoice(false);
        setIsListening(false);
      forceRefresh();
        return;
      }
      
      // 3. 将识别的文本添加到聊天记录，并显示在界面上
      addLog(`识别结果: "${recognizedText}"`);
      setVoiceChatMessages(prev => [...prev, `[用户] ${recognizedText}`]);
      forceRefresh();
      
      // 4. 提取意图和关键词 (Intent extraction is language agnostic for now)
      addLog('分析用户查询意图...');
      let intentInfo: ExtractedIntentInfo = { intent: 'unknown' }; // <-- Use new type and initialize
      try {
          // <-- Pass correct history context
          intentInfo = await extractPlaceInfoWithAI(recognizedText, getVoiceHistory(voiceChatMessages), currentSearchContext, addLog);
          addLog(`意图分析结果: ${JSON.stringify(intentInfo)}`);
      } catch (intentError) {
          addLog(`意图分析出错: ${intentError}`);
          // Default to unknown intent on error
          intentInfo = { intent: 'unknown' };
      }
      
      // --- NEW: Detect Language EARLY --- << MOVE LANGUAGE DETECTION HERE
      let detectedLangCode = await detectLanguageWithAI(recognizedText, addLog);
      addLog(`Detected language for STT result: ${detectedLangCode}`);
      
      // --- NEW: Language Override Logic for Voice Input ---
      const isShortInputVoice = recognizedText.split(' ').length <= 2;
      const detectedBaseLangVoice = detectedLangCode.split('-')[0];
      const appBaseLangVoice = currentAppLanguage.split('-')[0];

      if (isShortInputVoice && detectedBaseLangVoice !== appBaseLangVoice) {
          addLog(`OVERRIDE (Voice): Short input detected as ${detectedLangCode} (base: ${detectedBaseLangVoice}), differs from app base lang ${appBaseLangVoice}. Forcing ${currentAppLanguage}.`);
          detectedLangCode = currentAppLanguage; // Override detected code
      }
      // --- END: Language Override Logic ---

      // 5. 如果是位置相关查询，尝试搜索附近地点 (Modify based on intent)
      let placesResults: any[] | null = null;
      let presentedPlacesForThisTurn: any[] = [];
      const BATCH_SIZE = 5; // Number of recommendations per turn

      // --- Branching Logic based on Intent ---
      switch (intentInfo.intent) {
        case 'initial_search':
          addLog('Intent: initial_search. Clearing previous context and performing new search.');
          // Reset context for new search
          setCurrentSearchContext(null);
          setCachedPlacesResults(null);
          setPresentedPlaceIds(new Set());

          if (intentInfo.placeKeyword && userLocation) {
            addLog(`Performing initial search for keyword: "${intentInfo.placeKeyword}"`);
            try {
              const apiResults = await searchNearbyPlacesAPI_New(
                intentInfo.placeKeyword, 
                userLocation, 
                API_KEY, 
                3000, // Radius
                addLog,
                20 // Request 20 results initially
              );
              
              if (apiResults) {
                addLog(`Initial search found ${apiResults.length} results.`);
                setCachedPlacesResults(apiResults); // Cache all results
                presentedPlacesForThisTurn = apiResults.slice(0, BATCH_SIZE); // Get the first batch
                const newPresentedIds = new Set(presentedPlacesForThisTurn.map(p => p.place_id));
                setPresentedPlaceIds(newPresentedIds); // Update presented IDs
                setCurrentSearchContext({ 
                  keyword: intentInfo.placeKeyword, 
                  location: userLocation, 
                  isNearbyQuery: intentInfo.isNearby === true // <-- Ensure boolean
                }); // Set context
              } else {
                addLog('Initial search returned null or empty array.');
                // Keep states null/empty
              }
            } catch (placesError) {
              addLog(`Initial search API error: ${placesError}`);
              // Keep states null/empty
            }
          } else {
            addLog('Initial search intent missing keyword or user location.');
          }
          break;

        case 'more_recommendations':
          addLog('Intent: more_recommendations. Checking cached results.');
          if (cachedPlacesResults && currentSearchContext) {
            const remainingPlaces = cachedPlacesResults.filter(p => !presentedPlaceIds.has(p.place_id));
            addLog(`Found ${remainingPlaces.length} remaining places in cache.`);
            if (remainingPlaces.length > 0) {
              presentedPlacesForThisTurn = remainingPlaces.slice(0, BATCH_SIZE);
              const newPresentedIds = new Set([...presentedPlaceIds, ...presentedPlacesForThisTurn.map(p => p.place_id)]);
              setPresentedPlaceIds(newPresentedIds);
              addLog(`Presenting next batch of ${presentedPlacesForThisTurn.length} places.`);
            } else {
              addLog('No more places left in cache to recommend.');
              // Keep presentedPlacesForThisTurn empty, AI will be notified
            }
          } else {
            addLog('Cannot provide more recommendations: No cached results or context available.');
            // Possibly treat as clarification?
            intentInfo.intent = 'clarification'; 
          }
          break;

        // --- NEW: Add validation for details_about_place --- 
        case 'details_about_place':
          addLog(`Intent: details_about_place for "${intentInfo.placeName}". Validating place...`);
          let proceedWithDetails = false;
          if (intentInfo.placeName && userLocation) {
            try {
              // Use Text Search to find the place mentioned
              const validationResults = await searchNearbyPlacesAPI_New(
                intentInfo.placeName, 
                userLocation, 
                API_KEY, 
                10000, // Use a wider radius for validation
                addLog,
                1 // We only need to know if *at least one* relevant result exists
              );
              
              if (validationResults && validationResults.length > 0) {
                addLog(`Validation successful: Found potentially matching place(s) for "${intentInfo.placeName}".`);
                // Optionally, check if top result name is similar enough?
                // For now, assume any result means we can ask AI for details.
                proceedWithDetails = true;
                // Reset context as we are shifting focus to a specific place
                setCurrentSearchContext(null);
                setCachedPlacesResults(null); // Clear previous search results
                setPresentedPlaceIds(new Set());
              } else {
                addLog(`Validation failed: Could not find place "${intentInfo.placeName}" via Places API.`);
              }
            } catch (validationError) {
              addLog(`Validation API error for "${intentInfo.placeName}": ${validationError}`);
            }
          } else {
            addLog('Cannot validate place details without place name or user location.');
          }
          
          if (!proceedWithDetails) {
            // If validation fails, don't call getAIResponse for details.
            // Generate a clarification message directly.
            const clarificationMsg = `抱歉，我不太确定"${intentInfo.placeName || '那个地方'}"具体是指哪里，或者我暂时找不到它的详细信息。您可以换个说法或者问问附近的其它地方吗？`;
            setVoiceChatMessages(prev => [...prev, `[AI] ${clarificationMsg}`]);
            forceRefresh();
            
            // Play the clarification audio
            addLog('Playing clarification audio for failed validation...');
            const clarificationAudio = await textToSpeechAPI(clarificationMsg, detectedLangCode); // Use detected lang
            if (clarificationAudio) {
                await playAudio(clarificationAudio);
            } else {
                addLog('Failed to generate clarification audio.');
            }
            
            // Important: Skip the rest of the AI call and restart loop or stop
            setIsProcessingVoice(false); 
            forceRefresh();
            // Decide whether to loop again or stop based on isListening state
            if (isListening) {
                addLog('Validation failed, preparing next listening cycle.');
            } else {
                addLog('Validation failed and listening stopped.');
            }
            return; // Exit the switch and function early
          }
          // If validation passed, fallthrough to reset context like generic queries
          // The actual getAIResponse call later will handle the details intent
          // Reset context when focusing on details
          setCurrentSearchContext(null);
          setCachedPlacesResults(null); 
          setPresentedPlaceIds(new Set());
          break;
          // --- END: details_about_place validation --- 
          
        case 'generic_query':
        case 'context_switch':
        case 'clarification':
        case 'unknown':
          addLog(`Intent: ${intentInfo.intent}. Resetting search context.`);
          // Reset context if not a place search continuation
          setCurrentSearchContext(null);
          setCachedPlacesResults(null);
          setPresentedPlaceIds(new Set());
          break;
          
        // case 'details_about_place': // TODO: Implement logic if needed
        //   addLog(`Intent: details_about_place for "${intentInfo.placeName}".`);
        //   // Need logic to find the place and potentially call getPlaceDetails
        //   break;

        default:
          addLog(`Unhandled intent: ${intentInfo.intent}. Resetting context.`);
          setCurrentSearchContext(null);
          setCachedPlacesResults(null);
          setPresentedPlaceIds(new Set());
      }
      
      // --- Update placesResults for getAIResponse --- 
      // Pass only the places relevant for *this turn* to the AI
      // For details_about_place, pass null/empty as we handled validation
      placesResults = (intentInfo.intent === 'details_about_place') ? null : presentedPlacesForThisTurn;
      addLog(`Passing ${placesResults?.length ?? 0} places to getAIResponse for this turn.`);

      // 6. 调用 AI 生成回复
      addLog('正在生成AI回复...');
      setVoiceChatMessages(prev => [...prev, "[系统] 正在生成回复..."]);
      forceRefresh();
      
      // 创建会话历史上下文
      const historyContext = getVoiceHistory(voiceChatMessages);
      
      // --- NEW: Detect Language --- <<
      // const detectedLangCode = await detectLanguageWithAI(recognizedText, addLog); // <-- REMOVE THIS LINE
      // addLog(`Detected language for STT result: ${detectedLangCode}`); // <-- REMOVE THIS LINE
      
      // 调用AI（现在传递了 intentInfo 和地点子集）
      const aiSummaryText = await getAIResponse(
        historyContext,         
        recognizedText,         
        detectedLangCode, // <-- Use detected language
        intentInfo,             // <-- NEW: Pass intent info
        userLocation,           
        addLog,                 
        placesResults,          // <-- This is now the subset for the turn
        'voice'                 
      );
      
      if (!aiSummaryText) throw new Error('AI回复生成失败或为空');
        
      // --- 7. 清理 AI 回复文本 -> 作为屏幕显示文本 --- 
      const displayText = stripAllTags(aiSummaryText); // Use full cleaned text for display
      addLog(`AI原始回复: "${aiSummaryText.substring(0, 70)}..."`);
      addLog(`屏幕显示文本 (清理后): "${displayText.substring(0, 70)}..."`);

      // --- 8. 生成简洁的语音播报文本 --- 
      // 使用清理后的AI回复作为语音播报内容 (因为voice prompt已要求自然语言)
      const spokenText = displayText; 
      addLog(`语音播报文本 (使用清理后的AI回复): "${spokenText.substring(0, 70)}..."`);

      // --- 9. 添加 SSML 语言标签 (作用于语音文本) --- 
      // Note: Applying SSML to a fixed Chinese phrase might be redundant,
      // but we keep the step in case spokenText becomes dynamic.
      const ssmlSpokenText = addSSMLLangTags(spokenText); 
      if (ssmlSpokenText !== spokenText) {
        addLog(`添加SSML后语音文本: "${ssmlSpokenText.substring(0, 100)}..."`); 
      }

      // --- 10. 更新语音聊天UI (使用较完整的 displayText) --- 
      setVoiceChatMessages(prev => [...prev, `[AI] ${displayText}`]); // <-- UI shows more detail
      forceRefresh();

      // --- 11. TTS (使用简洁的 ssmlSpokenText 和检测到的语言) --- 
      addLog(`准备调用 TTS API (使用简洁语音文本, 语言: zh-CN)...`); 
      const audioData = await textToSpeechAPI(ssmlSpokenText, 'zh-CN'); 
      
      if (audioData) {
        addLog('TTS 成功，调用 playAudio 并等待完成...');
        await playAudio(audioData);
        addLog('playAudio 完成。');
      } else {
        addLog('TTS API 返回空数据或失败。跳过播放。');
        if (isPlayingAudio) { setIsPlayingAudio(false); forceRefresh(); }
      }
      
      // 新增：播放结束后重置处理状态并触发下一次循环
      setIsProcessingVoice(false);
      forceRefresh();
      if (isListening) {
        addLog('AI 播放完成，准备下一次监听循环。');
      } else {
        addLog('AI 播放完成，监听已停止，结束循环。');
      }

    } catch (outerError) {
      addLog(`startListeningLoop 发生错误: ${outerError}`);
      // 出错时停止循环并重置状态
      setIsProcessingVoice(false);
      setIsListening(false);
      forceRefresh();
    }
  }; // End of startListeningLoop

  // *** 新增函数：上传音频到服务器进行 STT ***
  const uploadAudioForStt = async (audioBase64: string): Promise<string | null> => {
    // !!! 重要：请将下面的 URL 替换为你的后端服务器地址 !!!
    const SERVER_ENDPOINT = 'http://************:4000/api/stt'; // 统一服务器 IP 和端口为 4000
    
    addLog(`准备上传音频到服务器: ${SERVER_ENDPOINT}`);
    try {
      const response = await fetch(SERVER_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        // 修改参数名称以匹配server.js中的参数名
        body: JSON.stringify({
          audio: audioBase64,
        }),
      });

      addLog(`服务器响应状态: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        addLog(`服务器错误: ${errorText}`);
        return null;
      }

      const result = await response.json();
      addLog(`服务器返回结果: ${JSON.stringify(result)}`);

      // 假设服务器成功时返回 { text: '识别的文本' }
      return result.text || null;
      
    } catch (error) {
      addLog(`上传或处理服务器响应时出错: ${error}`);
      // 这里可以根据错误类型添加更具体的提示，例如网络错误
      if (error instanceof TypeError && error.message.includes('Network request failed')) {
         setVoiceChatMessages(prev => [...prev, "[系统] 网络错误，无法连接到语音识别服务器"]);
      } else {
         setVoiceChatMessages(prev => [...prev, "[系统] 连接语音识别服务器时发生未知错误"]);
      }
      forceRefresh();
      return null;
    }
  };

  // 使用useEffect监听isListening状态变化，并启动/停止录音循环
  useEffect(() => {
    if (isListening && !isProcessingVoice) {
      addLog('useEffect: isListening && !isProcessingVoice, starting listening loop.');
      setupAudioMode().then(modeSet => {
        if (modeSet) {
          addLog('useEffect: Audio mode set. Calling startListeningLoop.');
          startListeningLoop();
        } else {
          addLog('useEffect: Failed to set audio mode. Aborting loop start.');
          setIsListening(false);
        }
      });
    } else if (!isListening) {
      addLog('useEffect: isListening is false, running cleanup.');
      if (silenceTimerRef.current) {
        clearTimeout(silenceTimerRef.current);
        silenceTimerRef.current = null;
        addLog('useEffect cleanup: VAD timer cleared.');
      }
      if (listeningLoopTimerRef.current) {
        clearTimeout(listeningLoopTimerRef.current);
        listeningLoopTimerRef.current = null;
        addLog('useEffect cleanup: listeningLoopTimerRef cleared.');
      }
      const recOnCleanup = recordingRef.current;
      const resOnCleanup = recordAudioPromiseResolveRef.current;
      if (recOnCleanup && resOnCleanup) {
        addLog('useEffect cleanup: Forcing recording stop on cleanup.');
        stopRecordingVAD(recOnCleanup, 'EFFECT_CLEANUP');
      } else if (resOnCleanup) {
        resOnCleanup(null);
        recordAudioPromiseResolveRef.current = null;
      }
    }
    // No action when isListening && isProcessingVoice (loop in progress)

    // Cleanup on unmount or before next effect run
    return () => {};
  }, [isListening, isProcessingVoice]);

  // 组件卸载处理 - 确保这是在此文件中最后一个 useEffect
  useEffect(() => {
    isUnmountedRef.current = false; // 初始设置为false

    // 将稳定函数引用存储在 useEffect 的作用域内
    const stableCleanup = cleanupRecording;
    const stableAddLog = addLog;

    // 返回一个清理函数
    return () => {
      isUnmountedRef.current = true; // 标记为已卸载
      stableAddLog("组件卸载: 执行清理..."); // 使用 useEffect 作用域内的稳定引用
      // 调用稳定版本的清理函数
      stableCleanup("COMPONENT_UNMOUNT"); // 使用 useEffect 作用域内的稳定引用
    };
  // 依赖项保持不变，包含 addLog 和 cleanupRecording 以确保使用的是最新稳定版本
  }, [addLog, cleanupRecording]); // 依赖项不变

  const [location, setLocation] = useState(null); // <-- 新增：位置 state
  const [locationLoading, setLocationLoading] = useState(false); // <-- 新增：位置加载 state

  // 新增：请求位置权限函数
  const requestLocationPermission = async (): Promise<boolean> => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      addLog('Location permission denied');
      Alert.alert('权限被拒绝', '需要位置权限才能提供附近推荐。');
      return false;
    }
    addLog('Location permission granted');
    return true;
  };

  // 修改：获取当前位置函数 (增加高精度选项，优化默认值)
  const getCurrentLocation = async (options: { timeout?: number, maxAge?: number, highAccuracy?: boolean } = {}): Promise<Location.LocationObject | null> => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      return null;
    }

    // -- 优化默认值 --
    const highAccuracyRequested = options.highAccuracy === true;
    const defaultTimeout = highAccuracyRequested ? 15000 : 8000;    // 高精度超时15s，平衡精度超时8s
    const defaultMaxAge = 1000 * 60 * 2;                         // 缓存有效期延长至2分钟
    // 修复Location.Accuracy的访问方式 - 使用常量值
    const accuracyLevel = highAccuracyRequested ? 1 : 3; // 1=High, 3=Balanced
    const accuracyDesc = highAccuracyRequested ? "High" : "Balanced";

    // 从选项中获取值，如果未提供则使用优化后的默认值
    const { timeout = defaultTimeout, maxAge = defaultMaxAge } = options;

    try {
      // 1. 尝试获取最后已知位置 (使用延长的 maxAge)
      addLog(`尝试获取最后已知位置 (maxAge: ${maxAge}ms)...`);
      const lastKnown = await Location.getLastKnownPositionAsync({
        maxAge: maxAge 
      });

      if (lastKnown) {
        addLog(`使用最后已知位置 (获取于 ${new Date(lastKnown.timestamp).toLocaleTimeString()}, Accuracy: ${accuracyDesc}): ${lastKnown.coords.latitude}, ${lastKnown.coords.longitude}`);
        setLocation(lastKnown); // 更新 state
        return lastKnown;
      }

      // 2. 如果没有有效缓存，获取当前位置 (根据请求的精度 + 超时)
      addLog(`无有效缓存或缓存过旧，开始获取当前位置 (${accuracyDesc} Accuracy, timeout: ${timeout}ms)...`);
      setLocationLoading(true); // 显示加载状态

      const locationPromise = Location.getCurrentPositionAsync({
        accuracy: accuracyLevel,
      });

      const timeoutPromise = new Promise<null>((resolve) =>
        setTimeout(() => {
            addLog(`获取位置超时 (${timeout}ms)`);
            resolve(null);
        }, timeout)
      );

      // 看哪个先完成
      const currentLocation = await Promise.race([locationPromise, timeoutPromise]);

      setLocationLoading(false); // 结束加载状态

      if (currentLocation) {
        addLog(`实时位置获取成功 (${accuracyDesc} Accuracy): ${currentLocation.coords.latitude}, ${currentLocation.coords.longitude}`);
        setLocation(currentLocation);
        return currentLocation;
      } else {
        // 超时或获取失败，根据是否请求高精度显示不同提示
        const accuracyText = highAccuracyRequested ? "高精度" : "平衡精度";
        addLog(`无法在 ${timeout/1000} 秒内获取到 ${accuracyText} 位置。`);
        // 仅在用户明确需要高精度时才弹窗提示失败，避免打扰普通查询
        if (highAccuracyRequested) {
             Alert.alert('定位超时', `无法在 ${timeout/1000} 秒内获取到 ${accuracyText} 的位置。`);
        }
        return null;
      }

    } catch (error) {
      addLog(`获取位置时出错: ${error}`);
      // 仅在用户明确需要高精度时才弹窗提示错误
      if (highAccuracyRequested) { 
        Alert.alert('定位错误', '无法获取当前位置。');
      }
      setLocationLoading(false);
      return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#202124" />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      >
        <View style={styles.container}>
          {/* 头部 */}
          <View style={styles.headerContainer}>
            <Ionicons name="navigate" size={20} color="#4285f4" />
            <Text style={styles.headerTitle}>iGuided AI</Text>
          </View>
          
          <ScrollView 
            ref={scrollViewRef}
            style={styles.messagesContainer}
            contentContainerStyle={styles.messagesList}
            keyboardShouldPersistTaps="handled"
          >
            {/* 日期分隔线 */}
            <View style={styles.dateDivider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dateText}>{date}</Text>
              <View style={styles.dividerLine} />
            </View>
            
            {!messages || messages.length === 0 ? (
              <View style={styles.welcomeContainer}>
                <Text style={styles.welcomeTitle}>iGuided AI</Text>
                <Text style={styles.welcomeSubtitle}>Your intelligent travel assistant, providing professional travel advice, itinerary planning, and destination information</Text>
                <View style={styles.suggestionContainer}>
                  {suggestions.map((suggestion, index) => (
                    <TouchableOpacity 
                      key={index} 
                      style={[
                        styles.suggestionButton,
                        selectedSuggestion === index && styles.suggestionButtonActive
                      ]}
                      onPress={() => handleSuggestionPress(suggestion, index)}
                      disabled={loading}
                    >
                      <Text style={styles.suggestionText}>{suggestion}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            ) : (
              Array.isArray(messages) && messages.map((message, index) => {
                if (!message) return null; // Skip if message is undefined/null
                return (
                <View 
                    key={message.id || index} // 使用index作为备用key
                  style={styles.messageRow}
                >
                  {/* 消息发送者头像 */}
                  {message.sender === 'gemini' ? (
                      // Use Image for AI Avatar
                      <Image 
                        source={require('C:/AppTest/letstravel3.131/LetsTravel/assets/images/iguided.png')} // <-- Reference the image
                        style={styles.botAvatarContainer} // Apply styling for size/shape
                      />
                    ) : (
                      // User Avatar (unchanged)
                    <View style={styles.avatarContainer}>
                      <Text style={styles.userAvatarText}>U</Text>
                    </View>
                  )}
                  
                    {/* 消息内容 - 使用 Markdown 组件，并添加检查 */}
                  <View style={styles.messageContent}>
                    <View 
                      style={[
                        styles.messageBubble, 
                        message.sender === 'user' ? styles.userBubble : styles.geminiBubble
                      ]}
                    >
                        {message.sender === 'gemini' ? (
                          <Markdown style={markdownStyles}>
                            {typeof message.text === 'string' ? message.text : ''}
                          </Markdown>
                        ) : (
                          <Text style={styles.messageText}>
                            {typeof message.text === 'string' ? message.text : ''}
                          </Text>
                        )}
                      {message.imageUri && renderImageMessage(message.imageUri)}
                    </View>
                  </View>
                </View>
                );
              })
            )}
            
            {/* 加载动画 */}
            {loading && (
              <View style={styles.messageRow}>
                <View style={styles.botAvatarContainer}>
                  <Text style={styles.botAvatarText}>G</Text>
                </View>
                <TypingAnimation />
              </View>
            )}
            
            {/* 图片预览 */}
            {imageUri && (
              <View style={styles.imagePreviewContainer}>
                <Image 
                  source={{ uri: imageUri }} 
                  style={styles.imagePreview}
                  resizeMode="cover"
                />
                <TouchableOpacity 
                  style={styles.removeImageButton} 
                  onPress={handleRemoveImage}
                >
                  <Ionicons name="close" size={16} color="#ffffff" />
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>
          
          {/* 输入区域 */}
          <View style={styles.inputContainer}>
            <TouchableOpacity 
              style={styles.attachmentButton}
              onPress={() => setShowImageAttachmentModal(true)}
            >
              <Ionicons 
                name="add-circle" 
                size={24} 
                color="#4285f4" 
              />
            </TouchableOpacity>
            
            <TextInput
              style={styles.input}
              value={input}
              onChangeText={setInput}
              placeholder="Send a message to iGuided..."
              placeholderTextColor="#71767a"
              multiline
              maxLength={1000}
            />

            {/* 语音聊天按钮 */}
            <TouchableOpacity 
              style={styles.voiceButton} 
              onPress={toggleVoiceRecording}
            >
              <Ionicons 
                name="mic-outline"
                size={24} 
                color="#ffffff"
              />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.sendButton,
                (!input.trim() && !imageUri) && styles.sendButtonDisabled
              ]} 
              onPress={handleSend}
              disabled={(!input.trim() && !imageUri) || loading}
            >
              <Ionicons 
                name="send"
                size={18} 
                color="#ffffff"
                style={styles.sendIcon}
              />
            </TouchableOpacity>
          </View>
          
          {/* 图片附件模态窗 */}
          <Modal
            visible={showImageAttachmentModal}
            transparent={true}
            animationType="fade"
            onRequestClose={() => setShowImageAttachmentModal(false)}
          >
            <TouchableWithoutFeedback onPress={() => setShowImageAttachmentModal(false)}>
              <View style={styles.modalContainer}>
                <TouchableWithoutFeedback>
                  <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                      <Text style={styles.modalTitle}>Add Image</Text>
                      <TouchableOpacity onPress={() => setShowImageAttachmentModal(false)}>
                        <Ionicons name="close" size={24} color="#5f6368" />
                      </TouchableOpacity>
                    </View>
                    
                    <TouchableOpacity 
                      style={styles.modalOption} 
                      onPress={handleTakePhoto}
                    >
                      <Ionicons name="camera" size={24} color="#4285f4" />
                      <Text style={styles.modalOptionText}>Take photo</Text>
                    </TouchableOpacity>
                    
                    <TouchableOpacity 
                      style={styles.modalOption} 
                      onPress={handleChooseFromLibrary}
                    >
                      <Ionicons name="image" size={24} color="#4285f4" />
                      <Text style={styles.modalOptionText}>Choose from library</Text>
                    </TouchableOpacity>
                  </View>
                </TouchableWithoutFeedback>
              </View>
            </TouchableWithoutFeedback>
          </Modal>

          {/* 实时语音聊天模态窗 */}
          <Modal
            visible={showVoiceChatModal}
            transparent={true}
            animationType="slide"
            onRequestClose={closeVoiceChat}
          >
            <View style={styles.voiceChatModalContainer}>
              <View style={styles.voiceChatModalHeader}>
                <Text style={styles.voiceChatModalTitle}>iGuided 语音助手</Text>
                <View style={{flexDirection: 'row'}}>
                  {/* 添加刷新按钮 */}
                  <TouchableOpacity onPress={forceRefresh} style={styles.debugButton}>
                    <Ionicons name="refresh" size={24} color="#ffffff" />
                  </TouchableOpacity>
                  <TouchableOpacity 
                    onPress={() => {
                      setShowDebugLogs(!showDebugLogs);
                      addLog(`调试日志显示状态: ${!showDebugLogs ? "显示" : "隐藏"}`);
                      forceRefresh();
                    }} 
                    style={[styles.debugButton, { zIndex: 1001 }]}
                    activeOpacity={0.6}
                  >
                    <Ionicons name="bug" size={24} color={showDebugLogs ? "#FF5722" : "#ffffff"} />
                  </TouchableOpacity>
                  <TouchableOpacity onPress={closeVoiceChat} style={styles.voiceChatCloseButton}>
                    <Ionicons name="close" size={24} color="#ffffff" />
                  </TouchableOpacity>
                </View>
              </View>
              
              {/* 添加快速测试按钮 */}
              <View style={styles.testButtonsContainer}>
                <TouchableOpacity 
                  style={styles.mainTestButton} 
                  onPress={testAudioRecording}
                >
                  <Ionicons name="mic-circle" size={28} color="#fff" />
                  <Text style={styles.mainTestButtonText}>测试录音功能</Text>
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.voiceChatMessagesContainer}>
                {voiceChatMessages?.map((message, index) => (
                  <View key={`msg-${index}-${refreshUI}`} style={[
                    styles.voiceChatMessage,
                    message.startsWith('[系统]') 
                      ? styles.voiceChatSystemMessage 
                      : (index % 2 === 0 ? styles.voiceChatAiMessage : styles.voiceChatUserMessage)
                  ]}>
                    <Text style={styles.voiceChatMessageText}>{message}</Text>
                  </View>
                ))}
                
                {isListening && (
                  <View style={styles.listeningIndicator}>
                    <Text style={styles.listeningText}>Listening{isVoicePaused ? " (Paused)" : "..."}</Text>
                    <View style={styles.listeningAnimation}>
                      {waveHeights.map((height, index) => (
                        <View 
                          key={index} 
                          style={[
                            styles.listeningBar, 
                            { 
                              height: isVoicePaused ? 5 : height,
                              backgroundColor: isVoicePaused ? '#9AA0A6' : '#4285f4'
                            }
                          ]} 
                        />
                      ))}
                    </View>
                  </View>
                )}
              </ScrollView>
              
              {/* Voice chat footer controls */}
              <View style={styles.voiceChatFooter}>
                {isPlayingAudio ? (
                  <View style={styles.voiceChatButton}>
                    <Ionicons name="volume-high" size={32} color="#fff" />
                  </View>
                ) : (
                  <>
                    <TouchableOpacity 
                      style={[styles.voiceChatButton, isListening ? styles.voiceChatButtonActive : null, (isProcessingVoice || isPlayingAudio) ? styles.voiceChatButtonDisabled : null]} // 添加禁用样式
                      onPress={async () => {
                        console.log('**** VOICE CHAT MIC BUTTON PRESSED ****');
                        addLog('**** VOICE CHAT MIC BUTTON PRESSED ****');
                        addLog('点击主麦克风按钮');
                        if (isPlayingAudio) {
                          addLog('正在播放音频，忽略点击');
                          return;
                        }
                        if (isListening) {
                          addLog('点击按钮停止录音');
                          setIsListening(false);
                          setVoiceChatMessages(prev => [...prev, "[系统] 录音已停止"]);
                          await cleanupRecording('STOP_BUTTON'); // 新增，彻底清理
                          forceRefresh();
                        } else {
                          if (recordingRef.current) {
                            addLog('警告：开始录音前发现仍有录音引用，尝试清理...');
                            try {
                              await recordingRef.current.stopAndUnloadAsync();
                            } catch(e) { /* ignore */ }
                            recordingRef.current = null;
                          }
                          setIsVoicePaused(false);
                          setIsListening(true);
                        }
                      }}
                      disabled={isProcessingVoice || isPlayingAudio} // 核心：禁用按钮
                    >
                      <Ionicons 
                        name={isListening ? "mic" : "mic-off"} 
                        size={32} 
                        color="#fff" 
                      />
                    </TouchableOpacity>
                    
                    <Text style={styles.voiceButtonText}>
                      {isProcessingVoice ? "处理中..." : isPlayingAudio ? "播放中..." : isListening ? "正在聆听..." : "点击开始"} // 根据状态显示不同文本
                    </Text>
                  </>
                )}
                
                <TouchableOpacity style={styles.closeChatButton} onPress={closeVoiceChat}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
          
          {/* 调试日志模态窗 */}
          {showDebugLogs && (
            <View style={styles.debugLogsContainer}>
              <View style={styles.debugLogsHeader}>
                <Text style={styles.debugLogsTitle}>调试日志</Text>
                <View style={{flexDirection: 'row'}}>
                  <TouchableOpacity onPress={forceRefresh} style={styles.debugButton}>
                    <Text style={styles.clearLogsText}>刷新</Text>
                  </TouchableOpacity>
                  <TouchableOpacity onPress={() => setDebugLogs([])} style={styles.clearLogsButton}>
                    <Text style={styles.clearLogsText}>清除</Text>
                  </TouchableOpacity>
                </View>
              </View>
              <FlatList
                data={debugLogs}
                keyExtractor={(_, index) => `log-${index}-${refreshUI}`}
                renderItem={({item}) => (
                  <Text style={styles.logEntry}>{item}</Text>
                )}
                style={styles.logsList}
                // 自动滚动到底部
                ref={ref => {
                  if (ref && debugLogs.length > 0) {
                    setTimeout(() => ref.scrollToEnd({animated: false}), 100);
                  }
                }}
              />
            </View>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

// Styles for Markdown component
const markdownStyles = {
  body: { fontSize: 15, color: '#202124', lineHeight: 22 },
  strong: { fontWeight: 'bold' },
  link: { color: '#1a73e8' }, // Default link color - library handles click
  // Add other styles as needed (e.g., list_item, heading)
};

// Get screen width for responsive styles
const { width } = Dimensions.get('window');

// Main component styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#ffffff',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#202124',
    marginLeft: 12,
  },
  headerLogo: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesList: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    paddingTop: 8,
  },
  welcomeContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 24,
  },
  welcomeTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#202124',
    marginBottom: 8,
    textAlign: 'center',
  },
  welcomeSubtitle: {
    fontSize: 16,
    color: '#5f6368',
    marginBottom: 40,
    textAlign: 'center',
    lineHeight: 22,
  },
  suggestionContainer: {
    width: '100%',
    maxWidth: 500,
    alignSelf: 'center',
  },
  suggestionButton: {
    backgroundColor: '#f1f3f4',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  suggestionButtonActive: {
    backgroundColor: '#e8f0fe',
  },
  suggestionText: {
    color: '#202124',
    fontSize: 15,
  },
  messageBubble: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 6,
    maxWidth: width > 500 ? '70%' : '88%', // Ensure width is available here
  },
  userBubble: {
    backgroundColor: '#e8f0fe',
    alignSelf: 'flex-end',
    borderTopRightRadius: 4,
  },
  geminiBubble: {
    backgroundColor: '#f1f3f4',
    alignSelf: 'flex-start',
    borderTopLeftRadius: 4,
  },
  messageText: {
    color: '#202124',
    fontSize: 15,
    lineHeight: 22,
  },
  loadingContainer: {
    padding: 16,
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f1f3f4',
    borderRadius: 12,
    alignSelf: 'flex-start',
    maxWidth: '70%',
  },
  typingDot: {
    height: 6,
    width: 6,
    borderRadius: 3,
    backgroundColor: '#4285f4',
    marginHorizontal: 2,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 12,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  input: {
    flex: 1,
    backgroundColor: '#f1f3f4',
    color: '#202124',
    padding: 12,
    paddingTop: 12,
    borderRadius: 24,
    maxHeight: 120,
    fontSize: 15,
    marginRight: 8,
  },
  sendButton: {
    marginLeft: 12,
    backgroundColor: '#4285f4',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#dadce0',
  },
  sendIcon: {
    marginLeft: 2,
  },
  // 语音按钮样式
  voiceButton: {
    marginLeft: 8,
    backgroundColor: '#FF5722',
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  voiceButtonRecording: {
    backgroundColor: '#EA4335', // Google红色，表示正在录制
    transform: [{ scale: 1.1 }],
  },
  dateDivider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#e0e0e0',
  },
  dateText: {
    color: '#5f6368',
    fontSize: 13,
    marginHorizontal: 12,
  },
  avatarContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#4285f4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  botAvatarContainer: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#4285f4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  botAvatarText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  userAvatarText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  messageContent: {
    flex: 1,
  },
  // 附件按钮样式
  attachmentButton: {
    width: 40, 
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f1f3f4',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  attachmentButtonIcon: {
    color: '#5f6368',
  },
  attachmentOptionsContainer: {
    position: 'absolute',
    bottom: 80,
    left: 12,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  attachmentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
  },
  attachmentOptionText: {
    marginLeft: 16,
    color: '#202124',
    fontSize: 14,
  },
  imagePreviewContainer: {
    width: '100%',
    padding: 8,
    backgroundColor: '#f1f3f4',
    borderRadius: 8,
    marginVertical: 8,
  },
  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    backgroundColor: '#e0e0e0',
  },
  imageMessageContainer: {
    width: '100%',
    maxWidth: 300,
  },
  imageMessage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '85%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#202124',
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalOptionText: {
    marginLeft: 16,
    fontSize: 15,
    color: '#202124',
  },
  voiceChatModalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  voiceChatModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#4285f4',
  },
  voiceChatModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  voiceChatCloseButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceChatMessagesContainer: {
    flex: 1,
    padding: 16,
  },
  voiceChatMessage: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    maxWidth: '85%',
  },
  voiceChatAiMessage: {
    backgroundColor: '#f1f3f4',
    alignSelf: 'flex-start',
    borderTopLeftRadius: 4,
  },
  voiceChatUserMessage: {
    backgroundColor: '#e8f0fe',
    alignSelf: 'flex-end',
    borderTopRightRadius: 4,
  },
  voiceChatMessageText: {
    color: '#202124',
    fontSize: 16,
    lineHeight: 22,
  },
  listeningIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    marginVertical: 20,
  },
  listeningText: {
    color: '#4285f4',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 12,
  },
  listeningAnimation: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 30,
  },
  listeningBar: {
    width: 4,
    backgroundColor: '#4285f4',
    marginHorizontal: 3,
    borderRadius: 2,
  },
  voiceChatControlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#ffffff',
  },
  voiceChatControlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4285f4',
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginHorizontal: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  voiceChatResumeButton: {
    backgroundColor: '#4CAF50',
  },
  voiceChatPauseButton: {
    backgroundColor: '#F44336',
  },
  voiceChatControlText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  testButtonsContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#3c4043',
  },
  mainTestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FF5722',
    padding: 12,
    borderRadius: 30,
    marginVertical: 6,
  },
  mainTestButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  voiceChatSystemMessage: {
    backgroundColor: '#444',
    alignSelf: 'center',
    borderRadius: 12,
    maxWidth: '90%',
  },
  debugLogsContainer: {
    position: 'absolute',
    top: 60,
    left: 10,
    right: 10,
    bottom: 80,
    backgroundColor: 'rgba(0,0,0,0.95)',
    borderRadius: 8,
    zIndex: 9999,
    elevation: 10,
    borderWidth: 1,
    borderColor: '#444',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  debugLogsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#111',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  debugLogsTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  clearLogsButton: {
    padding: 5,
  },
  clearLogsText: {
    color: '#8ab4f8',
    fontSize: 14,
  },
  logsList: {
    flex: 1,
  },
  logEntry: {
    color: '#0f0',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: 12,
    padding: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  voiceChatFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#3c4043',
  },
  voiceChatButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#8ab4f8',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceChatButtonActive: {
    backgroundColor: '#66BB6A',
  },
  voiceChatButtonDisabled: {
    backgroundColor: '#9E9E9E', // 灰色表示禁用
    opacity: 0.6,
  },
  voiceChatToggleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  debugButton: {
    padding: 8,
    marginRight: 8,
  },
  closeChatButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 

// Expo Router需要组件作为默认导出
export default IGuidedScreen;
