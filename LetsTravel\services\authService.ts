/**
 * Trekmate 4.0 - 用户认证服务
 * 提供用户注册、登录、注销和用户信息管理
 */

import { supabase } from './supabaseClient';
import type { User, UserProfile } from '../types/User';

export interface AuthResponse {
  success: boolean;
  user?: User;
  error?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  name: string;
  confirmPassword: string;
}

export interface UpdateProfileData {
  name?: string;
  avatar?: string;
  preferences?: any;
}

/**
 * 认证服务类
 */
export class AuthService {
  private currentUser: User | null = null;

  /**
   * 用户注册
   */
  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    try {
      const { email, password, name, confirmPassword } = credentials;

      // 验证输入
      if (!email || !password || !name) {
        return {
          success: false,
          error: '请填写所有必填字段',
        };
      }

      if (password !== confirmPassword) {
        return {
          success: false,
          error: '密码确认不匹配',
        };
      }

      if (password.length < 6) {
        return {
          success: false,
          error: '密码长度至少6位',
        };
      }

      // 使用 Supabase 注册
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) {
        console.error('注册错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      if (data.user) {
        // 创建用户资料
        await this.createUserProfile(data.user.id, {
          name,
          email,
          avatar: null,
          preferences: {
            language: 'zh-CN',
            currency: 'MYR',
            notifications: true,
          },
        });

        const user = this.transformSupabaseUser(data.user, { name });
        this.currentUser = user;

        return {
          success: true,
          user,
        };
      }

      return {
        success: false,
        error: '注册失败',
      };
    } catch (error) {
      console.error('注册失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '注册失败',
      };
    }
  }

  /**
   * 用户登录
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const { email, password } = credentials;

      if (!email || !password) {
        return {
          success: false,
          error: '请输入邮箱和密码',
        };
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('登录错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      if (data.user) {
        // 获取用户资料
        const profile = await this.getUserProfile(data.user.id);
        const user = this.transformSupabaseUser(data.user, profile);
        this.currentUser = user;

        return {
          success: true,
          user,
        };
      }

      return {
        success: false,
        error: '登录失败',
      };
    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '登录失败',
      };
    }
  }

  /**
   * 用户注销
   */
  async logout(): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('注销错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      this.currentUser = null;
      return { success: true };
    } catch (error) {
      console.error('注销失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '注销失败',
      };
    }
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * 检查用户是否已登录
   */
  isAuthenticated(): boolean {
    return this.currentUser !== null;
  }

  /**
   * 获取当前会话
   */
  async getSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('获取会话错误:', error);
        return null;
      }

      if (session?.user) {
        // 获取用户资料
        const profile = await this.getUserProfile(session.user.id);
        this.currentUser = this.transformSupabaseUser(session.user, profile);
      }

      return session;
    } catch (error) {
      console.error('获取会话失败:', error);
      return null;
    }
  }

  /**
   * 更新用户资料
   */
  async updateProfile(data: UpdateProfileData): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      if (!this.currentUser) {
        return {
          success: false,
          error: '用户未登录',
        };
      }

      // 更新用户资料表
      const { error } = await supabase
        .from('user_profiles')
        .update({
          name: data.name,
          avatar: data.avatar,
          preferences: data.preferences,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', this.currentUser.id);

      if (error) {
        console.error('更新资料错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      // 更新本地用户对象
      this.currentUser = {
        ...this.currentUser,
        name: data.name || this.currentUser.name,
        avatar: data.avatar || this.currentUser.avatar,
        preferences: data.preferences || this.currentUser.preferences,
      };

      return {
        success: true,
        user: this.currentUser,
      };
    } catch (error) {
      console.error('更新资料失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新资料失败',
      };
    }
  }

  /**
   * 重置密码
   */
  async resetPassword(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);

      if (error) {
        console.error('重置密码错误:', error);
        return {
          success: false,
          error: error.message,
        };
      }

      return { success: true };
    } catch (error) {
      console.error('重置密码失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '重置密码失败',
      };
    }
  }

  /**
   * 创建用户资料
   */
  private async createUserProfile(userId: string, profile: UserProfile): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          name: profile.name,
          email: profile.email,
          avatar: profile.avatar,
          preferences: profile.preferences,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error('创建用户资料错误:', error);
      }
    } catch (error) {
      console.error('创建用户资料失败:', error);
    }
  }

  /**
   * 获取用户资料
   */
  private async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('获取用户资料错误:', error);
        return null;
      }

      return {
        name: data.name,
        email: data.email,
        avatar: data.avatar,
        preferences: data.preferences,
      };
    } catch (error) {
      console.error('获取用户资料失败:', error);
      return null;
    }
  }

  /**
   * 转换 Supabase 用户对象为应用用户对象
   */
  private transformSupabaseUser(supabaseUser: any, profile?: UserProfile | null): User {
    return {
      id: supabaseUser.id,
      email: supabaseUser.email,
      name: profile?.name || supabaseUser.user_metadata?.name || '',
      avatar: profile?.avatar || null,
      preferences: profile?.preferences || {
        language: 'zh-CN',
        currency: 'MYR',
        notifications: true,
      },
      createdAt: supabaseUser.created_at,
      lastLoginAt: supabaseUser.last_sign_in_at,
    };
  }

  /**
   * 监听认证状态变化
   */
  onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        const profile = await this.getUserProfile(session.user.id);
        this.currentUser = this.transformSupabaseUser(session.user, profile);
        callback(this.currentUser);
      } else {
        this.currentUser = null;
        callback(null);
      }
    });
  }
}

// 导出单例实例
export const authService = new AuthService(); 