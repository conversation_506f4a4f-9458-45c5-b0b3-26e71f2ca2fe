# Values used to calculate the hash in this folder name.
# Should not depend on the absolute path of the project itself.
#   - AGP: 8.6.0.
#   - $NDK is the path to NDK 26.1.10909125.
#   - $PROJECT is the path to the parent folder of the root Gradle build file.
#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.
#   - $HASH is the hash value computed from this text.
#   - $CMAKE is the path to CMake 3.22.1.
#   - $NINJA is the path to Ninja.
-HC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=$ABI
-DCMAKE_ANDROID_ARCH_ABI=$ABI
-DANDROID_NDK=$NDK
-DCMAKE_ANDROID_NDK=$NDK
-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake
-DCMA<PERSON>_MAKE_PROGRAM=$NINJA
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/$HASH/prefab/$ABI/prefab
-BC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/$HASH/$ABI
-GNinja
-DANDROID_STL=c++_shared