/**
 * Trekmate 4.0 - 帖子卡片组件
 * 社区动态中的帖子展示卡片，支持图片、位置、互动等功能
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { COMMUNITY } from '../../constants/Strings';
import type { CommunityPost } from '../../services/community/CommunityService';

// ============================================================================
// 帖子卡片Props
// ============================================================================

interface PostCardProps {
  post: CommunityPost;
  onPress?: (post: CommunityPost) => void;
  onLike?: (postId: string) => void;
  onComment?: (post: CommunityPost) => void;
  onShare?: (post: CommunityPost) => void;
  onBookmark?: (postId: string) => void;
  onUserPress?: (userId: string) => void;
  onLocationPress?: (location: CommunityPost['location']) => void;
  onJourneyPress?: (journeyId: string) => void;
  style?: any;
}

// ============================================================================
// 帖子卡片组件
// ============================================================================

export default function PostCard({
  post,
  onPress,
  onLike,
  onComment,
  onShare,
  onBookmark,
  onUserPress,
  onLocationPress,
  onJourneyPress,
  style,
}: PostCardProps) {
  const [imageLoadErrors, setImageLoadErrors] = useState<Set<string>>(new Set());

  // 处理点赞
  const handleLike = () => {
    onLike?.(post.id);
  };

  // 处理评论
  const handleComment = () => {
    onComment?.(post);
  };

  // 处理分享
  const handleShare = () => {
    onShare?.(post);
  };

  // 处理收藏
  const handleBookmark = () => {
    onBookmark?.(post.id);
  };

  // 处理用户点击
  const handleUserPress = () => {
    onUserPress?.(post.authorId);
  };

  // 处理位置点击
  const handleLocationPress = () => {
    if (post.location) {
      onLocationPress?.(post.location);
    }
  };

  // 处理行程点击
  const handleJourneyPress = () => {
    if (post.journeyId) {
      onJourneyPress?.(post.journeyId);
    }
  };

  // 处理图片加载错误
  const handleImageError = (imageUrl: string) => {
    setImageLoadErrors(prev => new Set(prev).add(imageUrl));
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) {
      return '刚刚';
    } else if (diffMins < 60) {
      return `${diffMins}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // 渲染用户头像和信息
  const renderUserHeader = () => (
    <TouchableOpacity style={styles.userHeader} onPress={handleUserPress}>
      <View style={styles.avatarContainer}>
        {post.authorAvatar ? (
          <Image source={{ uri: post.authorAvatar }} style={styles.avatar} />
        ) : (
          <View style={[styles.avatar, styles.avatarPlaceholder]}>
            <Ionicons name="person" size={20} color={colors.textSecondary} />
          </View>
        )}
      </View>
      
      <View style={styles.userInfo}>
        <Text style={styles.authorName}>{post.authorName}</Text>
        <Text style={styles.postTime}>{formatTime(post.createdAt)}</Text>
      </View>

      <TouchableOpacity style={styles.moreButton}>
        <Ionicons name="ellipsis-horizontal" size={20} color={colors.textSecondary} />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  // 渲染内容
  const renderContent = () => (
    <TouchableOpacity 
      style={styles.contentContainer} 
      onPress={() => onPress?.(post)}
      activeOpacity={0.95}
    >
      <Text style={styles.content}>{post.content}</Text>
      
      {/* 标签 */}
      {post.tags.length > 0 && (
        <View style={styles.tagsContainer}>
          {post.tags.slice(0, 3).map((tag, index) => (
            <View key={index} style={styles.tag}>
              <Text style={styles.tagText}>#{tag}</Text>
            </View>
          ))}
          {post.tags.length > 3 && (
            <Text style={styles.moreTagsText}>+{post.tags.length - 3}</Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  // 渲染图片
  const renderImages = () => {
    if (!post.images || post.images.length === 0) return null;

    const validImages = post.images.filter(img => !imageLoadErrors.has(img));
    if (validImages.length === 0) return null;

    return (
      <View style={styles.imagesContainer}>
        {validImages.length === 1 ? (
          <TouchableOpacity style={styles.singleImageContainer}>
            <Image
              source={{ uri: validImages[0] }}
              style={styles.singleImage}
              onError={() => handleImageError(validImages[0])}
            />
          </TouchableOpacity>
        ) : (
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.multipleImagesContainer}
          >
            {validImages.map((image, index) => (
              <TouchableOpacity key={index} style={styles.multipleImageContainer}>
                <Image
                  source={{ uri: image }}
                  style={styles.multipleImage}
                  onError={() => handleImageError(image)}
                />
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </View>
    );
  };

  // 渲染位置信息
  const renderLocation = () => {
    if (!post.location) return null;

    return (
      <TouchableOpacity style={styles.locationContainer} onPress={handleLocationPress}>
        <Ionicons name="location" size={16} color={colors.primary[500]} />
        <Text style={styles.locationText} numberOfLines={1}>
          {post.location.address}
        </Text>
      </TouchableOpacity>
    );
  };

  // 渲染行程关联
  const renderJourneyLink = () => {
    if (!post.journeyId) return null;

    return (
      <TouchableOpacity style={styles.journeyContainer} onPress={handleJourneyPress}>
        <Ionicons name="map" size={16} color={colors.secondary[500]} />
        <Text style={styles.journeyText}>查看完整行程</Text>
      </TouchableOpacity>
    );
  };

  // 渲染互动按钮
  const renderActions = () => (
    <View style={styles.actionsContainer}>
      <TouchableOpacity 
        style={[styles.actionButton, post.isLiked && styles.actionButtonActive]}
        onPress={handleLike}
      >
        <Ionicons 
          name={post.isLiked ? "heart" : "heart-outline"} 
          size={20} 
          color={post.isLiked ? colors.error : colors.textSecondary} 
        />
        <Text style={[
          styles.actionText,
          post.isLiked && styles.actionTextActive
        ]}>
          {formatNumber(post.likes)}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.actionButton} onPress={handleComment}>
        <Ionicons name="chatbubble-outline" size={20} color={colors.textSecondary} />
        <Text style={styles.actionText}>{formatNumber(post.comments)}</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
        <Ionicons name="share-outline" size={20} color={colors.textSecondary} />
        <Text style={styles.actionText}>{formatNumber(post.shares)}</Text>
      </TouchableOpacity>

      <View style={styles.actionsSpacer} />

      <TouchableOpacity 
        style={[styles.actionButton, post.isBookmarked && styles.actionButtonActive]}
        onPress={handleBookmark}
      >
        <Ionicons 
          name={post.isBookmarked ? "bookmark" : "bookmark-outline"} 
          size={20} 
          color={post.isBookmarked ? colors.warning : colors.textSecondary} 
        />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={[styles.container, style]}>
      {renderUserHeader()}
      {renderContent()}
      {renderImages()}
      {renderLocation()}
      {renderJourneyLink()}
      {renderActions()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    marginHorizontal: spacing[4],
    marginVertical: spacing[2],
    ...shadows.md,
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[4],
    paddingBottom: spacing[3],
  },
  avatarContainer: {
    marginRight: spacing[3],
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  avatarPlaceholder: {
    backgroundColor: colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  authorName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[1],
  },
  postTime: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  moreButton: {
    padding: spacing[1],
  },
  contentContainer: {
    paddingHorizontal: spacing[4],
    paddingBottom: spacing[3],
  },
  content: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text,
    marginBottom: spacing[3],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: spacing[2],
  },
  tag: {
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.sm,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
  },
  tagText: {
    fontSize: 12,
    color: colors.primary[700],
    fontWeight: '500',
  },
  moreTagsText: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  imagesContainer: {
    marginBottom: spacing[3],
  },
  singleImageContainer: {
    marginHorizontal: spacing[4],
    borderRadius: borderRadius.md,
    overflow: 'hidden',
  },
  singleImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  multipleImagesContainer: {
    paddingLeft: spacing[4],
  },
  multipleImageContainer: {
    marginRight: spacing[2],
    borderRadius: borderRadius.md,
    overflow: 'hidden',
  },
  multipleImage: {
    width: 150,
    height: 120,
    resizeMode: 'cover',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
    backgroundColor: colors.primary[50],
    marginHorizontal: spacing[4],
    marginBottom: spacing[3],
    borderRadius: borderRadius.md,
  },
  locationText: {
    fontSize: 14,
    color: colors.primary[700],
    marginLeft: spacing[2],
    flex: 1,
  },
  journeyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
    backgroundColor: colors.secondary[50],
    marginHorizontal: spacing[4],
    marginBottom: spacing[3],
    borderRadius: borderRadius.md,
  },
  journeyText: {
    fontSize: 14,
    color: colors.secondary[700],
    marginLeft: spacing[2],
    fontWeight: '500',
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing[2],
    paddingHorizontal: spacing[3],
    borderRadius: borderRadius.md,
    marginRight: spacing[4],
  },
  actionButtonActive: {
    backgroundColor: colors.primary[50],
  },
  actionText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: spacing[2],
    fontWeight: '500',
  },
  actionTextActive: {
    color: colors.primary[700],
  },
  actionsSpacer: {
    flex: 1,
  },
});
