ninja: Entering directory `C:\AppTest\letstravel3.131\LetsTravel\node_modules\expo-gl\android\.cxx\Debug\6k1h1t2m\armeabi-v7a'
[1/9] Building CXX object CMakeFiles/expo-gl.dir/src/main/cpp/EXGLJniApi.cpp.o
[2/9] Building CXX object CMakeFiles/expo-gl.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLNativeApi.cpp.o
[3/9] Building CXX object CMakeFiles/expo-gl.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLContextManager.cpp.o
[4/9] Building CXX object CMakeFiles/expo-gl.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLNativeContext.cpp.o
[5/9] Building CXX object CMakeFiles/expo-gl.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXTypedArrayApi.cpp.o
[6/9] Building CXX object CMakeFiles/expo-gl.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXWebGLRenderer.cpp.o
[7/9] Building CXX object CMakeFiles/expo-gl.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXGLImageUtils.cpp.o
[8/9] Building CXX object CMakeFiles/expo-gl.dir/C_/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/common/EXWebGLMethods.cpp.o
[9/9] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\6k1h1t2m\obj\armeabi-v7a\libexpo-gl.so
