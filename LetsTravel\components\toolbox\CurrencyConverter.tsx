/**
 * Trekmate 4.0 - 货币转换器组件
 * 提供实时汇率查询和货币转换功能，采用2025风格设计
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { TOOLBOX, COMMON } from '../../constants/Strings';
import { currencyService } from '../../services/toolbox/CurrencyService';
import type { CurrencyRate, CurrencyConversion } from '../../services/toolbox/CurrencyService';

// ============================================================================
// 货币选择器组件
// ============================================================================

interface CurrencySelectorProps {
  label: string;
  selectedCurrency: string;
  onCurrencySelect: (currency: string) => void;
  currencies: CurrencyRate[];
}

const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  label,
  selectedCurrency,
  onCurrencySelect,
  currencies,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  
  const selectedCurrencyData = currencies.find(c => c.code === selectedCurrency);

  const handleCurrencyPress = (currency: string) => {
    onCurrencySelect(currency);
    setShowPicker(false);
  };

  return (
    <View style={styles.currencySelector}>
      <Text style={styles.selectorLabel}>{label}</Text>
      
      <TouchableOpacity
        style={styles.currencyButton}
        onPress={() => setShowPicker(!showPicker)}
      >
        <View style={styles.currencyInfo}>
          <Text style={styles.currencyCode}>{selectedCurrency}</Text>
          <Text style={styles.currencyName}>
            {selectedCurrencyData?.name || '选择货币'}
          </Text>
        </View>
        <Ionicons 
          name={showPicker ? "chevron-up" : "chevron-down"} 
          size={20} 
          color={colors.textSecondary} 
        />
      </TouchableOpacity>

      {showPicker && (
        <View style={styles.currencyPicker}>
          <ScrollView style={styles.currencyList} showsVerticalScrollIndicator={false}>
            {currencies.map((currency) => (
              <TouchableOpacity
                key={currency.code}
                style={[
                  styles.currencyOption,
                  currency.code === selectedCurrency && styles.currencyOptionSelected,
                ]}
                onPress={() => handleCurrencyPress(currency.code)}
              >
                <View style={styles.currencyOptionInfo}>
                  <Text style={[
                    styles.currencyOptionCode,
                    currency.code === selectedCurrency && styles.currencyOptionCodeSelected,
                  ]}>
                    {currency.code}
                  </Text>
                  <Text style={[
                    styles.currencyOptionName,
                    currency.code === selectedCurrency && styles.currencyOptionNameSelected,
                  ]}>
                    {currency.name}
                  </Text>
                </View>
                {currency.code === selectedCurrency && (
                  <Ionicons name="checkmark" size={20} color={colors.primary[500]} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

// ============================================================================
// 货币转换器组件
// ============================================================================

export default function CurrencyConverter() {
  const [fromCurrency, setFromCurrency] = useState('USD');
  const [toCurrency, setToCurrency] = useState('MYR');
  const [amount, setAmount] = useState('100');
  const [result, setResult] = useState<CurrencyConversion | null>(null);
  const [currencies, setCurrencies] = useState<CurrencyRate[]>([]);
  const [loading, setLoading] = useState(false);
  const [ratesLoading, setRatesLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // 加载支持的货币列表
  useEffect(() => {
    loadCurrencies();
  }, []);

  // 当货币或金额变化时自动转换
  useEffect(() => {
    if (amount && fromCurrency && toCurrency && currencies.length > 0) {
      convertCurrency();
    }
  }, [amount, fromCurrency, toCurrency, currencies]);

  const loadCurrencies = async () => {
    try {
      setRatesLoading(true);
      const response = await currencyService.getSupportedCurrencies();
      
      if (response.success && response.data) {
        setCurrencies(response.data);
        setLastUpdated(new Date());
      } else {
        Alert.alert(COMMON.ERROR, response.error || '加载货币列表失败');
      }
    } catch (error) {
      Alert.alert(COMMON.ERROR, '网络连接失败');
    } finally {
      setRatesLoading(false);
    }
  };

  const convertCurrency = async () => {
    if (!amount || isNaN(Number(amount))) {
      setResult(null);
      return;
    }

    try {
      setLoading(true);
      const response = await currencyService.convertCurrency(
        fromCurrency,
        toCurrency,
        Number(amount)
      );

      if (response.success && response.data) {
        setResult(response.data);
      } else {
        Alert.alert(COMMON.ERROR, response.error || '转换失败');
        setResult(null);
      }
    } catch (error) {
      Alert.alert(COMMON.ERROR, '网络连接失败');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const handleSwapCurrencies = () => {
    const temp = fromCurrency;
    setFromCurrency(toCurrency);
    setToCurrency(temp);
  };

  const handleAmountChange = (text: string) => {
    // 只允许数字和小数点
    const cleanText = text.replace(/[^0-9.]/g, '');
    
    // 防止多个小数点
    const parts = cleanText.split('.');
    if (parts.length > 2) {
      return;
    }
    
    setAmount(cleanText);
  };

  const formatCurrency = (value: number, currencyCode: string) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  // 渲染头部
  const renderHeader = () => (
    <LinearGradient
      colors={[colors.primary[500], colors.primary[600]]}
      style={styles.header}
    >
      <View style={styles.headerContent}>
        <Text style={styles.headerTitle}>{TOOLBOX.CURRENCY_CONVERTER}</Text>
        <Text style={styles.headerSubtitle}>
          {lastUpdated ? `更新时间: ${lastUpdated.toLocaleTimeString('zh-CN')}` : ''}
        </Text>
      </View>
      
      <TouchableOpacity style={styles.refreshButton} onPress={loadCurrencies}>
        <Ionicons name="refresh" size={20} color={colors.surface} />
      </TouchableOpacity>
    </LinearGradient>
  );

  // 渲染转换器主体
  const renderConverter = () => (
    <View style={styles.converterContainer}>
      {/* 金额输入 */}
      <View style={styles.amountContainer}>
        <Text style={styles.amountLabel}>转换金额</Text>
        <TextInput
          style={styles.amountInput}
          value={amount}
          onChangeText={handleAmountChange}
          placeholder="输入金额"
          placeholderTextColor={colors.textSecondary}
          keyboardType="decimal-pad"
          selectTextOnFocus
        />
      </View>

      {/* 源货币选择器 */}
      <CurrencySelector
        label="从"
        selectedCurrency={fromCurrency}
        onCurrencySelect={setFromCurrency}
        currencies={currencies}
      />

      {/* 交换按钮 */}
      <View style={styles.swapContainer}>
        <TouchableOpacity style={styles.swapButton} onPress={handleSwapCurrencies}>
          <Ionicons name="swap-vertical" size={24} color={colors.primary[500]} />
        </TouchableOpacity>
      </View>

      {/* 目标货币选择器 */}
      <CurrencySelector
        label="到"
        selectedCurrency={toCurrency}
        onCurrencySelect={setToCurrency}
        currencies={currencies}
      />
    </View>
  );

  // 渲染结果
  const renderResult = () => {
    if (ratesLoading) {
      return (
        <View style={styles.resultContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={styles.loadingText}>加载汇率数据...</Text>
        </View>
      );
    }

    if (loading) {
      return (
        <View style={styles.resultContainer}>
          <ActivityIndicator size="large" color={colors.primary[500]} />
          <Text style={styles.loadingText}>计算中...</Text>
        </View>
      );
    }

    if (!result || !amount || isNaN(Number(amount))) {
      return (
        <View style={styles.resultContainer}>
          <Ionicons name="calculator" size={48} color={colors.textSecondary} />
          <Text style={styles.emptyText}>输入金额开始转换</Text>
        </View>
      );
    }

    return (
      <View style={styles.resultContainer}>
        <View style={styles.resultCard}>
          <Text style={styles.resultAmount}>
            {formatCurrency(result.convertedAmount, toCurrency)}
          </Text>
          <Text style={styles.resultRate}>
            1 {fromCurrency} = {result.exchangeRate.toFixed(4)} {toCurrency}
          </Text>
          <Text style={styles.resultTimestamp}>
            汇率更新时间: {new Date(result.timestamp).toLocaleString('zh-CN')}
          </Text>
        </View>
      </View>
    );
  };

  // 渲染快捷货币
  const renderQuickCurrencies = () => {
    const quickCurrencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'MYR', 'SGD', 'THB'];
    
    return (
      <View style={styles.quickCurrenciesContainer}>
        <Text style={styles.quickCurrenciesTitle}>常用货币</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.quickCurrenciesList}>
            {quickCurrencies.map((currency) => {
              const currencyData = currencies.find(c => c.code === currency);
              if (!currencyData) return null;

              return (
                <TouchableOpacity
                  key={currency}
                  style={[
                    styles.quickCurrencyChip,
                    (currency === fromCurrency || currency === toCurrency) && 
                    styles.quickCurrencyChipActive,
                  ]}
                  onPress={() => {
                    if (currency !== fromCurrency && currency !== toCurrency) {
                      setToCurrency(currency);
                    }
                  }}
                >
                  <Text style={[
                    styles.quickCurrencyCode,
                    (currency === fromCurrency || currency === toCurrency) && 
                    styles.quickCurrencyCodeActive,
                  ]}>
                    {currency}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderHeader()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderConverter()}
        {renderResult()}
        {renderQuickCurrencies()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
    borderBottomLeftRadius: borderRadius.xl,
    borderBottomRightRadius: borderRadius.xl,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.surface,
    marginBottom: spacing[1],
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.surface,
    opacity: 0.8,
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing[4],
  },
  converterContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    padding: spacing[6],
    marginTop: -spacing[8],
    ...shadows.lg,
  },
  amountContainer: {
    marginBottom: spacing[6],
  },
  amountLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[3],
  },
  amountInput: {
    fontSize: 32,
    fontWeight: '700',
    color: colors.text,
    borderBottomWidth: 2,
    borderBottomColor: colors.primary[500],
    paddingVertical: spacing[2],
    textAlign: 'center',
  },
  currencySelector: {
    marginBottom: spacing[4],
  },
  selectorLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
    marginBottom: spacing[2],
  },
  currencyButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.neutral[100],
    borderRadius: borderRadius.lg,
    padding: spacing[4],
  },
  currencyInfo: {
    flex: 1,
  },
  currencyCode: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  currencyName: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  currencyPicker: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    marginTop: spacing[1],
    ...shadows.lg,
    zIndex: 1000,
  },
  currencyList: {
    maxHeight: 200,
  },
  currencyOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  currencyOptionSelected: {
    backgroundColor: colors.primary[50],
  },
  currencyOptionInfo: {
    flex: 1,
  },
  currencyOptionCode: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  currencyOptionCodeSelected: {
    color: colors.primary[700],
  },
  currencyOptionName: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  currencyOptionNameSelected: {
    color: colors.primary[600],
  },
  swapContainer: {
    alignItems: 'center',
    marginVertical: spacing[2],
  },
  swapButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultContainer: {
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  resultCard: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    padding: spacing[6],
    alignItems: 'center',
    ...shadows.md,
  },
  resultAmount: {
    fontSize: 36,
    fontWeight: '700',
    color: colors.primary[500],
    marginBottom: spacing[2],
  },
  resultRate: {
    fontSize: 16,
    color: colors.text,
    marginBottom: spacing[1],
  },
  resultTimestamp: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: spacing[3],
  },
  emptyText: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: spacing[3],
  },
  quickCurrenciesContainer: {
    marginTop: spacing[6],
    marginBottom: spacing[4],
  },
  quickCurrenciesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[3],
  },
  quickCurrenciesList: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  quickCurrencyChip: {
    backgroundColor: colors.neutral[100],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
  },
  quickCurrencyChipActive: {
    backgroundColor: colors.primary[500],
  },
  quickCurrencyCode: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  quickCurrencyCodeActive: {
    color: colors.surface,
  },
});
