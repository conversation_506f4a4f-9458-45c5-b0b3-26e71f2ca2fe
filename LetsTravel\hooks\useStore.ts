/**
 * Trekmate 4.0 - 状态管理钩子
 * 提供便捷的状态访问和操作钩子
 */

import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { AppState } from 'react-native';

import {
  useUserStore,
  useJourneyStore,
  useExploreStore,
  useSettingsStore,
  useUIStore,
  userSelectors,
  journeySelectors,
  exploreSelectors,
  settingsSelectors,
  rootStoreManager,
} from '../store';

import type { Journey, POI, User } from '../types/CoreServices';

// ============================================================================
// 用户相关钩子
// ============================================================================

/**
 * 用户认证钩子
 */
export const useAuth = () => {
  const isAuthenticated = useUserStore(state => state.isAuthenticated);
  const isLoading = useUserStore(state => state.auth.isLoading);
  const error = useUserStore(state => state.auth.error);
  const actions = useUserStore(state => state.actions);

  return {
    isAuthenticated,
    isLoading,
    error,
    login: actions.login,
    logout: actions.logout,
    register: actions.register,
  };
};

/**
 * 当前用户钩子
 */
export const useCurrentUser = () => {
  const user = useUserStore(state => state.user);
  const actions = useUserStore(state => state.actions);

  return {
    user,
    updateProfile: actions.updateProfile,
    updatePreferences: actions.updatePreferences,
  };
};

// ============================================================================
// 行程相关钩子
// ============================================================================

/**
 * 行程列表钩子
 */
export const useJourneys = () => {
  const journeys = useJourneyStore(state => state.journeys);
  const isLoading = useJourneyStore(state => state.isLoading);
  const error = useJourneyStore(state => state.error);
  const actions = useJourneyStore(state => state.actions);

  return {
    journeys: journeys || [],
    isLoading: isLoading || false,
    error: error || null,
    createJourney: actions.createJourney,
    updateJourney: actions.updateJourney,
    deleteJourney: actions.deleteJourney,
  };
};

/**
 * 当前行程钩子
 */
export const useCurrentJourney = () => {
  const currentJourney = useJourneyStore(state => state.currentJourney);
  const isLoading = useJourneyStore(state => state.isLoading);
  const error = useJourneyStore(state => state.error);
  const actions = useJourneyStore(state => state.actions);

  return {
    journey: currentJourney,
    isLoading,
    error,
    selectJourney: actions.selectJourney,
    clearCurrentJourney: actions.clearCurrentJourney,
    updateJourney: actions.updateJourney,
  };
};

// ============================================================================
// 探索相关钩子
// ============================================================================

/**
 * POI搜索钩子
 */
export const usePOISearch = () => {
  const search = useExploreStore(state => state.search);
  const searchResults = useExploreStore(state => state.pois?.search);
  const actions = useExploreStore(state => state.actions);

  return {
    query: search?.query || '',
    filters: search?.filters || { type: [], priceLevel: [], rating: 0, distance: 10, openNow: false },
    location: search?.location || null,
    isSearching: search?.isSearching || false,
    error: search?.error || null,
    results: searchResults?.data || [],
    searchPOIs: actions?.searchPOIs || (() => Promise.resolve()),
    setSearchQuery: actions?.setSearchQuery || (() => {}),
    setSearchFilters: actions?.setSearchFilters || (() => {}),
    setSearchLocation: actions?.setSearchLocation || (() => {}),
    clearSearch: actions?.clearSearch || (() => {}),
  };
};

/**
 * 附近POI钩子
 */
export const useNearbyPOIs = () => {
  const nearby = useExploreStore(state => state.pois?.nearby);
  const actions = useExploreStore(state => state.actions);

  return {
    pois: nearby?.data || [],
    isLoading: nearby?.isLoading || false,
    error: nearby?.error || null,
    fetchNearbyPOIs: actions?.fetchNearbyPOIs || (() => Promise.resolve()),
  };
};

/**
 * 收藏POI钩子
 */
export const useFavoritePOIs = () => {
  const favorites = useExploreStore(state => state.pois?.favorites);
  const actions = useExploreStore(state => state.actions);

  return {
    favorites: favorites?.data || [],
    isLoading: favorites?.isLoading || false,
    error: favorites?.error || null,
    addToFavorites: actions?.addToFavorites || (() => Promise.resolve()),
    removeFromFavorites: actions?.removeFromFavorites || (() => Promise.resolve()),
    
  };
};

// ============================================================================
// UI相关钩子
// ============================================================================

/**
 * 主题钩子
 */
export const useTheme = () => {
  const theme = useSettingsStore(state => state.theme);
  const updateTheme = useSettingsStore(state => state.actions.updateTheme);

  return {
    theme,
    updateTheme,
  };
};

/**
 * 语言钩子
 */
export const useLanguage = () => {
  const language = useSettingsStore(state => state.language);
  const updateLanguage = useSettingsStore(state => state.actions.updateLanguage);

  return {
    language,
    updateLanguage,
  };
};

/**
 * 加载状态钩子
 */
export const useLoading = () => {
  const globalLoading = useUIStore(state => state.loading.global);
  const overlayLoading = useUIStore(state => state.loading.overlay);
  const message = useUIStore(state => state.loading.message);
  const actions = useUIStore(state => state.actions);

  return {
    isGlobalLoading: globalLoading,
    isOverlayLoading: overlayLoading,
    message,
    setGlobalLoading: actions.setGlobalLoading,
    setOverlayLoading: actions.setOverlayLoading,
  };
};

/**
 * 错误状态钩子
 */
export const useError = () => {
  const error = useUIStore(state => state.errors.global);
  const networkError = useUIStore(state => state.errors.network);
  const lastError = useUIStore(state => state.errors.lastError);
  const actions = useUIStore(state => state.actions);

  return {
    error,
    networkError,
    lastError,
    setGlobalError: actions.setGlobalError,
    setNetworkError: actions.setNetworkError,
    clearMessages: actions.clearMessages,
  };
};

/**
 * 应用生命周期钩子
 */
export const useAppLifecycle = () => {
  const handleAppStateChange = (nextAppState: string) => {
    if (nextAppState.match(/inactive|background/)) {
      rootStoreManager.saveAll();
    }
  };

  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      // 清理监听器
    };
  }, []);
};

/**
 * 防抖钩子
 */
export const useDebounce = (value: any, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * 节流钩子
 */
export const useThrottle = (
  callback: (...args: any[]) => any,
  delay: number
) => {
  const lastRun = useRef(Date.now());

  return useCallback(
    (...args: any[]) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    },
    [callback, delay]
  );
};
