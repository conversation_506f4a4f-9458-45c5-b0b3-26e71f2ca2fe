/**
 * Trekmate 4.0 - 用户状态管理
 * 管理用户认证、资料和偏好设置
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { WritableDraft } from 'immer/dist/internal';

import type { User, UserPreferences } from '../types/User';
import { authService, type LoginCredentials, type RegisterCredentials, type UpdateProfileData } from '../services/authService';
import { coreServiceRegistry } from '../services/core/CoreServiceRegistry';
import { defaultStorage } from '../lib/storage/StorageAdapter';

// ============================================================================
// 状态接口定义
// ============================================================================

export interface UserState {
  // 用户信息
  user: User | null;
  isAuthenticated: boolean;
  
  // 认证状态
  auth: {
    isLoading: boolean;
    error: string | null;
  };

  // 操作方法
  actions: {
    // 认证相关
    login: (credentials: LoginCredentials) => Promise<boolean>;
    register: (credentials: RegisterCredentials) => Promise<boolean>;
    logout: () => Promise<void>;
    
    // 用户资料
    updateProfile: (data: UpdateProfileData) => Promise<boolean>;
    updatePreferences: (preferences: Partial<UserPreferences>) => Promise<boolean>;
    
    // 会话管理
    initializeAuth: () => Promise<void>;
    setUser: (user: User | null) => void;
    clearError: () => void;
  };
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: Omit<UserState, 'actions'> = {
  user: null,
  isAuthenticated: false,
  auth: {
    isLoading: false,
    error: null,
  },
};

// ============================================================================
// Store 实现
// ============================================================================

export const useUserStore = create<UserState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    actions: {
      // 用户登录
      login: async (credentials: LoginCredentials): Promise<boolean> => {
        set(state => ({
          auth: { ...state.auth, isLoading: true, error: null },
        }));

        try {
          const response = await authService.login(credentials);

          if (response.success && response.user) {
            set({
              user: response.user,
              isAuthenticated: true,
              auth: { isLoading: false, error: null },
            });
            return true;
          } else {
            set(state => ({
              auth: { 
                ...state.auth, 
                isLoading: false, 
                error: response.error || '登录失败' 
              },
            }));
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '登录失败';
          set(state => ({
            auth: { ...state.auth, isLoading: false, error: errorMessage },
          }));
          return false;
        }
      },

      // 用户注册
      register: async (credentials: RegisterCredentials): Promise<boolean> => {
        set(state => ({
          auth: { ...state.auth, isLoading: true, error: null },
        }));

        try {
          const response = await authService.register(credentials);

          if (response.success && response.user) {
            set({
              user: response.user,
              isAuthenticated: true,
              auth: { isLoading: false, error: null },
            });
            return true;
          } else {
            set(state => ({
              auth: { 
                ...state.auth, 
                isLoading: false, 
                error: response.error || '注册失败' 
              },
            }));
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '注册失败';
          set(state => ({
            auth: { ...state.auth, isLoading: false, error: errorMessage },
          }));
          return false;
        }
      },

      // 用户注销
      logout: async (): Promise<void> => {
        try {
          await authService.logout();
          set({
            user: null,
            isAuthenticated: false,
            auth: { isLoading: false, error: null },
          });
        } catch (error) {
          console.error('注销失败:', error);
        }
      },

      // 更新用户资料
      updateProfile: async (data: UpdateProfileData): Promise<boolean> => {
        try {
          const response = await authService.updateProfile(data);

          if (response.success && response.user) {
            set(state => ({
              user: response.user!,
            }));
            return true;
          } else {
            set(state => ({
              auth: { 
                ...state.auth, 
                error: response.error || '更新资料失败' 
              },
            }));
            return false;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新资料失败';
          set(state => ({
            auth: { ...state.auth, error: errorMessage },
          }));
          return false;
        }
      },

      // 更新用户偏好设置
      updatePreferences: async (preferences: Partial<UserPreferences>): Promise<boolean> => {
        const state = get();
        
        if (!state.user) {
          return false;
        }

        const updatedPreferences = {
          ...state.user.preferences,
          ...preferences,
        };

        return state.actions.updateProfile({ preferences: updatedPreferences });
      },

      // 初始化认证状态
      initializeAuth: async (): Promise<void> => {
        set(state => ({
          auth: { ...state.auth, isLoading: true },
        }));

        try {
          const session = await authService.getSession();
          
          if (session?.user) {
            const user = authService.getCurrentUser();
            if (user) {
              set({
                user,
                isAuthenticated: true,
                auth: { isLoading: false, error: null },
              });
            }
          } else {
            set({
              user: null,
              isAuthenticated: false,
              auth: { isLoading: false, error: null },
            });
          }
        } catch (error) {
          console.error('初始化认证失败:', error);
          set({
            user: null,
            isAuthenticated: false,
            auth: { isLoading: false, error: null },
          });
        }
      },

             // 设置用户（用于认证状态监听）
       setUser: (user: User | null): void => {
         set(state => ({
           ...state,
           user,
           isAuthenticated: user !== null,
         }));
       },

      // 清除错误
      clearError: (): void => {
        set(state => ({
          auth: { ...state.auth, error: null },
        }));
      },
    },
  }))
);

// ============================================================================
// 选择器
// ============================================================================

export const userSelectors = {
  getUser: (state: UserState) => state.user,
  isAuthenticated: (state: UserState) => state.isAuthenticated,
  getAuthState: (state: UserState) => state.auth,
  isLoading: (state: UserState) => state.auth.isLoading,
  getError: (state: UserState) => state.auth.error,
  getUserPreferences: (state: UserState) => state.user?.preferences,
  getUserName: (state: UserState) => state.user?.name,
  getUserEmail: (state: UserState) => state.user?.email,
  getUserAvatar: (state: UserState) => state.user?.avatar,
};

// ============================================================================
// 认证状态监听
// ============================================================================

// 监听认证状态变化
authService.onAuthStateChange((user) => {
  useUserStore.getState().actions.setUser(user);
});

// ============================================================================
// 状态持久化
// ============================================================================

export const userStorePersistence = {
  // 保存状态到本地存储
  save: async () => {
    const state = useUserStore.getState();
    
    await defaultStorage.batch([
      {
        type: 'set',
        key: 'user_state',
        value: {
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          auth: {
            ...state.auth,
            // 不保存敏感信息到状态中
            error: null,
          },
        },
      },
    ]);
  },

  // 从本地存储加载状态
  load: async () => {
    try {
      const savedState = await defaultStorage.getObject<any>('user_state');
      const savedUser = await defaultStorage.getObject<User>('user');
      const savedToken = await defaultStorage.getString('auth_token');
      const savedExpiresAt = await defaultStorage.getString('auth_expires_at');

      if (savedState || savedUser || savedToken) {
        useUserStore.setState(state => {
          if (savedState) {
            Object.assign(state, savedState);
          }
          
          if (savedUser) {
            state.user = savedUser;
          }
          
          if (savedToken && savedExpiresAt) {
            const expiresAt = new Date(savedExpiresAt);
            const now = new Date();
            
            if (expiresAt > now) {
              state.isAuthenticated = true;
              state.auth.token = savedToken;
              state.auth.expiresAt = savedExpiresAt;
            }
          }
        });
      }
    } catch (error) {
      console.error('Failed to load user state:', error);
    }
  },

  // 清除持久化数据
  clear: async () => {
    await defaultStorage.batch([
      { type: 'remove', key: 'user_state' },
      { type: 'remove', key: 'user' },
      { type: 'remove', key: 'auth_token' },
      { type: 'remove', key: 'auth_refresh_token' },
      { type: 'remove', key: 'auth_expires_at' },
      { type: 'remove', key: 'user_preferences' },
    ]);
  },
};

// ============================================================================
// 状态订阅和副作用
// ============================================================================

// 监听认证状态变化
useUserStore.subscribe(
  state => state.isAuthenticated,
  (isAuthenticated) => {
    console.log('Authentication state changed:', isAuthenticated);
    
    if (!isAuthenticated) {
      // 用户退出登录时清理相关数据
      userStorePersistence.clear();
    }
  }
);

// 监听用户偏好变化
useUserStore.subscribe(
  state => state.user?.preferences,
  (preferences) => {
    console.log('User preferences changed:', preferences);
    
    // 可以在这里触发主题变化、语言变化等副作用
  }
);
