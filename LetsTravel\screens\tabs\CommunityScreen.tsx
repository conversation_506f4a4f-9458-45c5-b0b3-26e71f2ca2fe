/**
 * Trekmate 4.0 - 社区主屏幕
 * 社区动态的主要展示界面，包含动态流、过滤器和发布入口
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { COMMUNITY, NAVIGATION } from '../../constants/Strings';
import CommunityFeed from '../../components/community/CommunityFeed';
import { useCurrentUser } from '../../hooks/useStore';
import type { CommunityScreenProps } from '../../types/Navigation';
import type { CommunityPost, FeedFilter } from '../../services/community/CommunityService';

// ============================================================================
// 过滤器选项
// ============================================================================

interface FilterOption {
  type: FeedFilter['type'];
  label: string;
  icon: string;
  description: string;
}

const FILTER_OPTIONS: FilterOption[] = [
  {
    type: 'all',
    label: '全部',
    icon: 'globe-outline',
    description: '查看所有公开动态',
  },
  {
    type: 'following',
    label: '关注',
    icon: 'people-outline',
    description: '查看关注用户的动态',
  },
  {
    type: 'nearby',
    label: '附近',
    icon: 'location-outline',
    description: '查看附近的动态',
  },
  {
    type: 'trending',
    label: '热门',
    icon: 'trending-up-outline',
    description: '查看热门动态',
  },
];

// ============================================================================
// 社区主屏幕组件
// ============================================================================

export default function CommunityScreen({ navigation }: CommunityScreenProps) {
  const [selectedFilter, setSelectedFilter] = useState<FeedFilter['type']>('all');
  const [showFilterMenu, setShowFilterMenu] = useState(false);
  const user = useCurrentUser();

  // 构建当前过滤器
  const currentFilter: FeedFilter = {
    type: selectedFilter,
    // 如果是附近动态，可以添加位置信息
    ...(selectedFilter === 'nearby' && {
      location: {
        latitude: 3.1390, // 示例坐标，实际应从用户位置获取
        longitude: 101.6869,
        radius: 5000, // 5km
      },
    }),
  };

  // 处理帖子点击
  const handlePostPress = useCallback((post: CommunityPost) => {
    navigation.navigate('PostDetail', { postId: post.id });
  }, [navigation]);

  // 处理用户点击
  const handleUserPress = useCallback((userId: string) => {
    navigation.navigate('UserProfile', { userId });
  }, [navigation]);

  // 处理位置点击
  const handleLocationPress = useCallback((location: CommunityPost['location']) => {
    if (location) {
      navigation.navigate('Map', {
        initialLocation: {
          latitude: location.latitude,
          longitude: location.longitude,
        },
        showPOI: location.poiId,
      });
    }
  }, [navigation]);

  // 处理行程点击
  const handleJourneyPress = useCallback((journeyId: string) => {
    navigation.navigate('JourneyDetail', { journeyId });
  }, [navigation]);

  // 处理创建帖子
  const handleCreatePost = useCallback(() => {
    if (!user) {
      Alert.alert('提示', '请先登录');
      return;
    }
    navigation.navigate('CreatePost');
  }, [navigation, user]);

  // 处理过滤器选择
  const handleFilterSelect = useCallback((filterType: FeedFilter['type']) => {
    setSelectedFilter(filterType);
    setShowFilterMenu(false);
  }, []);

  // 获取当前过滤器选项
  const getCurrentFilterOption = () => {
    return FILTER_OPTIONS.find(option => option.type === selectedFilter) || FILTER_OPTIONS[0];
  };

  // 渲染头部
  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerLeft}>
        <Text style={styles.headerTitle}>{COMMUNITY.COMMUNITY}</Text>
        <Text style={styles.headerSubtitle}>
          {getCurrentFilterOption().description}
        </Text>
      </View>

      <View style={styles.headerRight}>
        {/* 搜索按钮 */}
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('CommunitySearch')}
        >
          <Ionicons name="search-outline" size={24} color={colors.text} />
        </TouchableOpacity>

        {/* 通知按钮 */}
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.navigate('Notifications')}
        >
          <Ionicons name="notifications-outline" size={24} color={colors.text} />
          {/* 可以添加未读通知指示器 */}
        </TouchableOpacity>
      </View>
    </View>
  );

  // 渲染过滤器栏
  const renderFilterBar = () => (
    <View style={styles.filterBar}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filterScrollContent}
      >
        {FILTER_OPTIONS.map((option) => (
          <TouchableOpacity
            key={option.type}
            style={[
              styles.filterButton,
              selectedFilter === option.type && styles.filterButtonActive,
            ]}
            onPress={() => handleFilterSelect(option.type)}
          >
            <Ionicons
              name={option.icon as any}
              size={18}
              color={
                selectedFilter === option.type
                  ? colors.primary[700]
                  : colors.textSecondary
              }
            />
            <Text
              style={[
                styles.filterButtonText,
                selectedFilter === option.type && styles.filterButtonTextActive,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  // 渲染发布按钮
  const renderCreateButton = () => (
    <TouchableOpacity
      style={styles.createButton}
      onPress={handleCreatePost}
      activeOpacity={0.8}
    >
      <Ionicons name="add" size={24} color={colors.surface} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      {renderFilterBar()}
      
      <CommunityFeed
        filter={currentFilter}
        onPostPress={handlePostPress}
        onUserPress={handleUserPress}
        onLocationPress={handleLocationPress}
        onJourneyPress={handleJourneyPress}
        onCreatePost={handleCreatePost}
        style={styles.feed}
      />

      {renderCreateButton()}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    ...shadows.sm,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.text,
    marginBottom: spacing[1],
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.neutral[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBar: {
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  filterScrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    gap: spacing[3],
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
    borderRadius: borderRadius.lg,
    backgroundColor: colors.neutral[100],
    gap: spacing[2],
  },
  filterButtonActive: {
    backgroundColor: colors.primary[100],
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  filterButtonTextActive: {
    color: colors.primary[700],
  },
  feed: {
    flex: 1,
  },
  createButton: {
    position: 'absolute',
    bottom: spacing[6],
    right: spacing[4],
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.lg,
    elevation: 8,
  },
});
