[{"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLNativeApi.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLNativeApi.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLNativeApi.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLImageUtils.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLImageUtils.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLImageUtils.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLNativeContext.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLNativeContext.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLNativeContext.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLContextManager.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLContextManager.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXGLContextManager.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXWebGLMethods.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXWebGLMethods.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXWebGLMethods.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXWebGLRenderer.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXWebGLRenderer.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXWebGLRenderer.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXTypedArrayApi.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXTypedArrayApi.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\common\\EXTypedArrayApi.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_gl_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/../common -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -O2 -fexceptions -frtti -Wall -Wextra -Wno-unused-parameter -Wshorten-64-to-32 -Wstrict-prototypes -std=gnu++20 -o CMakeFiles\\expo-gl.dir\\src\\main\\cpp\\EXGLJniApi.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\src\\main\\cpp\\EXGLJniApi.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-gl\\android\\src\\main\\cpp\\EXGLJniApi.cpp"}]