[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\armeabi-v7a\\android_gradle_build.json due to:", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Microsoft\\\\jdk-17.0.14.7-hotspot\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  armeabi-v7a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  26 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging2468160875522465341\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.10.2\\\\transforms\\\\0f839437195569227022e709030e837c\\\\transformed\\\\react-android-0.76.9-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.10.2\\\\transforms\\\\1957b0db02baa50e26af4d5b92e700f6\\\\transformed\\\\fbjni-0.6.0\\\\prefab\"\n", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\armeabi-v7a'", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\armeabi-v7a'", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v2d5r5q\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v2d5r5q\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\2v2d5r5q\\\\prefab\\\\armeabi-v7a\\\\prefab\" ^\n  \"-BC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\2v2d5r5q\\\\armeabi-v7a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_DIR=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_TARGET_VERSION=76\" ^\n  \"-DUSE_HERMES=false\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DUNIT_TEST=false\"\n", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v2d5r5q\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\2v2d5r5q\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\2v2d5r5q\\\\prefab\\\\armeabi-v7a\\\\prefab\" ^\n  \"-BC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\expo-modules-core\\\\android\\\\.cxx\\\\Debug\\\\2v2d5r5q\\\\armeabi-v7a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DREACT_NATIVE_DIR=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_TARGET_VERSION=76\" ^\n  \"-DUSE_HERMES=false\" ^\n  \"-DIS_NEW_ARCHITECTURE_ENABLED=true\" ^\n  \"-DUNIT_TEST=false\"\n", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\armeabi-v7a\\compile_commands.json.bin normally", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\Debug\\2v2d5r5q\\armeabi-v7a\\compile_commands.json to C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\.cxx\\tools\\debug\\armeabi-v7a\\compile_commands.json", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\CMakeLists.txt", "tag_": "debug|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]