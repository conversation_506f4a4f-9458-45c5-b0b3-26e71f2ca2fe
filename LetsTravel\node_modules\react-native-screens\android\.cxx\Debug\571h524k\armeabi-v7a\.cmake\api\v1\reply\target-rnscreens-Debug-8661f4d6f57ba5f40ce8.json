{"artifacts": [{"path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/build/intermediates/cxx/Debug/571h524k/obj/armeabi-v7a/librnscreens.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_definitions", "include_directories", "set_target_properties"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}, {"command": 1, "file": 0, "line": 45, "parent": 0}, {"command": 2, "file": 0, "line": 33, "parent": 0}, {"command": 3, "file": 0, "line": 22, "parent": 0}, {"command": 4, "file": 0, "line": 26, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"fragment": "-std=c++20"}], "defines": [{"backtrace": 3, "define": "FOLLY_NO_CONFIG=1"}, {"define": "rnscreens_EXPORTS"}], "includes": [{"backtrace": 4, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/android/../cpp"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include"}], "language": "CXX", "languageStandard": {"backtraces": [5], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "rnscreens::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0f839437195569227022e709030e837c\\transformed\\react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1957b0db02baa50e26af4d5b92e700f6\\transformed\\fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "role": "libraries"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "rnscreens", "nameOnDisk": "librnscreens.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/jni-adapter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/NativeProxy.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/main/cpp/OnLoad.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}