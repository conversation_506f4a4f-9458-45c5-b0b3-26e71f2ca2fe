Trekmate 4.0 五主Tab架构开发计划
目录
导航与Tab结构
资源准备
依赖与技术栈初始化
启动流程开发（Splash → Onboarding → Login）
五主Tab页面开发
全局浮动按钮与AI助手
服务层与数据流
UI统一与主题
测试与质量保证
上架与交付
风险与注意事项
1. 导航与Tab结构
主Tab（底部导航栏）
Journey（行程/计划/AI生成/导出）
Explore（地图/推荐/搜索/收藏）
Camera（AR翻译/识别/汇率/拍照，品牌核心）
Toolbox（汇率/天气/路线/紧急联系等工具）
Me（个人中心/设置/成长/同步）
全局浮动按钮
AI助手（🤖）：全局悬浮，随时唤起AI对话/推荐
主FAB（+）：在Journey/Explore等页面出现，提供新建/收藏/定位等快捷操作
2. 资源准备
App Icon：assets/icons/trekmate icon.png
Splash Screen：assets/splash/welcomepage.png
Onboarding插画：assets/illustrations/onboarding_1.svg/png、onboarding_2.svg/png、onboarding_3.svg/png
主色/辅助色/字体：见constants/Colors.ts，Figma/设计令牌同步
app.json 配置
Apply to Trekmate_4.0...
3. 依赖与技术栈初始化
Tamagui（主UI）、NativeWind（Tailwind语法）、Moti/Reanimated（动效）、Lucide/Tabler Icons（图标）
react-native-svg（插画）、react-hook-form+zod（表单）、expo-splash-screen、expo-font
认证：expo-auth-session（Google/Facebook）
主题/设计令牌：constants/Colors.ts、tamagui.config.ts
安装命令示例
Apply to Trekmate_4.0...
Run
4. 启动流程开发（Splash → Onboarding → Login）
4.1 Splash Screen
资源：assets/splash/welcomepage.png
组件：AppLoader.tsx（控制 splash 显示/隐藏，加载字体/资源后自动跳转）
仅做品牌展示，不含业务逻辑
4.2 Onboarding
组件：screens/onboarding/Onboarding.tsx
开发参考：docs/archived/onboard.md
三页插画，底部分页指示器（主色激活，圆点动画）
标题、副标题、按钮文案全部走i18n
插画SVG优先，PNG自动适配低端机
“跳过”/“立即体验”按钮，最后一页跳转登录
4.3 路由流程
Splash → Onboarding（首次启动或未登录）
Onboarding → Login（onDone/onSkip）
Login → MainTabs（登录成功）
5. 五主Tab页面开发
5.1 Journey（行程中心）
行程列表、详情、编辑、AI生成、导出/分享
组件：JourneyList、JourneyTimeline、JourneyEditor、ExportPanel、GrowthPanel
服务：PlanDataService、AIPlanService、ExportService
5.2 Explore（探索）
地图、推荐、搜索、收藏、POI详情
组件：ExploreMap、POICard、POIDetail、SearchBar
服务：RecommendationService、MapService
5.3 Camera（AR核心）
AR翻译/识别/汇率/拍照，品牌主打
组件：ARCoreTextTranslation、CameraOverlay、CurrencyBubble
服务：ARTranslationService、CurrencyConverter
功能/交互/布局全部保留，仅升级视觉
5.4 Toolbox（工具箱）
汇率、天气、路线、紧急联系等
组件：CurrencyConverter、WeatherWidget、RoutePlanner、EmergencyPanel
服务：CurrencyService、WeatherService、RouteService、EmergencyService
5.5 Me（个人中心）
个人信息、收藏、设置、成长体系、同步
组件：ProfileHeader、CollectionList、SettingsPanel、SyncManager、GrowthPanel
服务：UserService、CollectionService、SettingsService、SyncService
6. 全局浮动按钮与AI助手
AI助手（🤖）：全局悬浮，随时唤起AI对话/推荐/行程生成
组件：AIChatPanel、AIQuickAction、AIResultCard
服务：AIService、SpeechService
主FAB（+）：在Journey/Explore等页面出现，提供新建/收藏/定位等快捷操作
组件：FAB、FABMenu、FABAction
仅承载高频操作，避免功能堆叠
7. 服务层与数据流
服务接口（PlanDataService, CurrencyConverter, ARTranslationService等）不变
本地+云端同步，接口/数据结构保持兼容
所有业务逻辑与UI分离，便于维护
8. UI统一与主题
所有页面/组件统一用Tamagui/NativeWind重构UI
汇率表仅美化UI，数据结构/交互完全保留
响应式适配，支持深浅色、无障碍
主题/设计令牌统一管理
9. 测试与质量保证
单元测试：关键组件/服务80%覆盖
E2E测试：Detox/Playwright覆盖主流程
手动QA：所有页面/功能/交互/深浅色/多语言/无障碍全覆盖
性能：冷启动<2.5s，主流程无卡顿
CI/CD：每次push自动lint/test/build，预览包自动生成
10. 上架与交付
App元数据：新icon/splash/截图/描述/隐私政策
版本管理：v4.0.0，CHANGELOG.md
灰度发布：10%→50%→100%，监控Crashlytics
用户反馈：收集首批用户体验，快速修复
11. 风险与注意事项
风险点	影响	对策
资源尺寸/适配问题	启动/插画拉伸/变形	设计师多分辨率导出，开发真机多机型测试
主题/深浅色切换	UI不一致	统一Colors.ts/tamagui.config.ts
认证集成（OAuth）	登录失败/回调异常	先本地调通，后接入生产key
旧业务逻辑遗漏	功能缺失/bug	逐页对照现有App，QA全流程回归
AR/汇率表兼容性	关键功能损坏	仅做UI升级，绝不动核心逻辑
性能/冷启动	首屏慢/闪退	图片压缩，Splash预加载，代码分包
上架审核	被拒/隐私问题	参照App Store/Play最新政策
12. 交互流程图（Mermaid）
Next/Skip
Success
Splash (welcomepage.png)
Onboarding (3 pages)
Login
Main Tabs (Journey, Explore, Camera, Toolbox, Me)
AI助手 (全局浮动)
主FAB (部分页面浮动)
备注：Onboarding开发请严格参考 docs/archived/onboard.md，Camera Tab为品牌核心，所有AR/翻译/汇率功能完整保留。
如需进一步细化每一阶段的负责人、每日任务、验收标准，或有特殊业务场景，请随时补充！