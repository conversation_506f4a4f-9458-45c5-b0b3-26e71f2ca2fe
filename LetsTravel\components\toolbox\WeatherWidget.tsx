/**
 * Trekmate 4.0 - 天气组件
 * 提供当前天气和未来几天的天气预报，采用2025风格设计
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { TOOLBOX, COMMON } from '../../constants/Strings';
import { weatherService } from '../../services/toolbox/WeatherService';
import type { WeatherData, WeatherForecast } from '../../services/toolbox/WeatherService';
import type { Location } from '../../types/CoreServices';

// ============================================================================
// 天气组件Props
// ============================================================================

interface WeatherWidgetProps {
  location?: Location;
  onLocationPress?: () => void;
  style?: any;
}

// ============================================================================
// 天气图标映射
// ============================================================================

const getWeatherIcon = (condition: string, isDay: boolean = true) => {
  const iconMap: Record<string, string> = {
    sunny: isDay ? 'sunny' : 'moon',
    cloudy: 'cloudy',
    'partly-cloudy': isDay ? 'partly-sunny' : 'cloudy-night',
    rainy: 'rainy',
    stormy: 'thunderstorm',
    snowy: 'snow',
    foggy: 'cloudy',
    windy: 'cloudy',
  };

  return iconMap[condition] || 'cloudy';
};

const getWeatherGradient = (condition: string) => {
  const gradientMap: Record<string, string[]> = {
    sunny: [colors.warning, colors.secondary[500]],
    cloudy: [colors.neutral[400], colors.neutral[600]],
    'partly-cloudy': [colors.primary[400], colors.primary[600]],
    rainy: [colors.info, colors.primary[700]],
    stormy: [colors.neutral[700], colors.neutral[900]],
    snowy: [colors.neutral[200], colors.neutral[400]],
    foggy: [colors.neutral[300], colors.neutral[500]],
    windy: [colors.primary[300], colors.primary[500]],
  };

  return gradientMap[condition] || [colors.primary[400], colors.primary[600]];
};

// ============================================================================
// 每日天气卡片组件
// ============================================================================

interface DayWeatherCardProps {
  forecast: WeatherForecast;
  isToday?: boolean;
}

const DayWeatherCard: React.FC<DayWeatherCardProps> = ({ forecast, isToday }) => {
  const dayName = isToday ? '今天' : new Date(forecast.date).toLocaleDateString('zh-CN', { weekday: 'short' });
  
  return (
    <View style={[styles.dayCard, isToday && styles.todayCard]}>
      <Text style={[styles.dayName, isToday && styles.todayDayName]}>
        {dayName}
      </Text>
      
      <View style={styles.dayIconContainer}>
        <Ionicons 
          name={getWeatherIcon(forecast.condition) as any} 
          size={32} 
          color={isToday ? colors.primary[500] : colors.textSecondary} 
        />
      </View>
      
      <View style={styles.dayTemperature}>
        <Text style={[styles.dayTempHigh, isToday && styles.todayTempHigh]}>
          {Math.round(forecast.highTemp)}°
        </Text>
        <Text style={[styles.dayTempLow, isToday && styles.todayTempLow]}>
          {Math.round(forecast.lowTemp)}°
        </Text>
      </View>
      
      <Text style={[styles.dayCondition, isToday && styles.todayCondition]}>
        {forecast.description}
      </Text>
    </View>
  );
};

// ============================================================================
// 天气组件
// ============================================================================

export default function WeatherWidget({
  location,
  onLocationPress,
  style,
}: WeatherWidgetProps) {
  const [currentWeather, setCurrentWeather] = useState<WeatherData | null>(null);
  const [forecast, setForecast] = useState<WeatherForecast[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // 加载天气数据
  const loadWeatherData = useCallback(async () => {
    if (!location) return;

    try {
      setLoading(true);

      // 获取当前天气
      const currentResponse = await weatherService.getCurrentWeather(location);
      if (currentResponse.success && currentResponse.data) {
        setCurrentWeather(currentResponse.data);
      }

      // 获取天气预报
      const forecastResponse = await weatherService.getWeatherForecast(location, 7);
      if (forecastResponse.success && forecastResponse.data) {
        setForecast(forecastResponse.data);
      }

      setLastUpdated(new Date());
    } catch (error) {
      Alert.alert(COMMON.ERROR, '获取天气数据失败');
    } finally {
      setLoading(false);
    }
  }, [location]);

  useEffect(() => {
    loadWeatherData();
  }, [loadWeatherData]);

  // 渲染当前天气
  const renderCurrentWeather = () => {
    if (!currentWeather) return null;

    const gradientColors = getWeatherGradient(currentWeather.condition);

    return (
      <LinearGradient colors={gradientColors} style={styles.currentWeatherCard}>
        <View style={styles.currentWeatherHeader}>
          <TouchableOpacity 
            style={styles.locationButton}
            onPress={onLocationPress}
          >
            <Ionicons name="location" size={16} color={colors.surface} />
            <Text style={styles.locationText} numberOfLines={1}>
              {location?.address || '当前位置'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.refreshButton} onPress={loadWeatherData}>
            <Ionicons name="refresh" size={20} color={colors.surface} />
          </TouchableOpacity>
        </View>

        <View style={styles.currentWeatherContent}>
          <View style={styles.currentWeatherLeft}>
            <Text style={styles.currentTemperature}>
              {Math.round(currentWeather.temperature)}°
            </Text>
            <Text style={styles.currentCondition}>
              {currentWeather.description}
            </Text>
            <Text style={styles.feelsLike}>
              体感温度 {Math.round(currentWeather.feelsLike)}°
            </Text>
          </View>

          <View style={styles.currentWeatherRight}>
            <Ionicons 
              name={getWeatherIcon(currentWeather.condition) as any} 
              size={80} 
              color={colors.surface} 
            />
          </View>
        </View>

        <View style={styles.weatherDetails}>
          <View style={styles.weatherDetailItem}>
            <Ionicons name="water" size={16} color={colors.surface} />
            <Text style={styles.weatherDetailText}>
              {currentWeather.humidity}%
            </Text>
          </View>
          
          <View style={styles.weatherDetailItem}>
            <Ionicons name="eye" size={16} color={colors.surface} />
            <Text style={styles.weatherDetailText}>
              {currentWeather.visibility}km
            </Text>
          </View>
          
          <View style={styles.weatherDetailItem}>
            <Ionicons name="speedometer" size={16} color={colors.surface} />
            <Text style={styles.weatherDetailText}>
              {currentWeather.pressure}hPa
            </Text>
          </View>
          
          <View style={styles.weatherDetailItem}>
            <Ionicons name="leaf" size={16} color={colors.surface} />
            <Text style={styles.weatherDetailText}>
              {currentWeather.windSpeed}km/h
            </Text>
          </View>
        </View>

        {lastUpdated && (
          <Text style={styles.lastUpdated}>
            更新时间: {lastUpdated.toLocaleTimeString('zh-CN')}
          </Text>
        )}
      </LinearGradient>
    );
  };

  // 渲染天气预报
  const renderForecast = () => {
    if (forecast.length === 0) return null;

    return (
      <View style={styles.forecastContainer}>
        <Text style={styles.forecastTitle}>7天预报</Text>
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.forecastScrollContent}
        >
          {forecast.map((dayForecast, index) => (
            <DayWeatherCard
              key={dayForecast.date}
              forecast={dayForecast}
              isToday={index === 0}
            />
          ))}
        </ScrollView>
      </View>
    );
  };

  // 渲染加载状态
  const renderLoading = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={colors.primary[500]} />
      <Text style={styles.loadingText}>获取天气数据...</Text>
    </View>
  );

  // 渲染空状态
  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Ionicons name="location-outline" size={48} color={colors.textSecondary} />
      <Text style={styles.emptyTitle}>需要位置信息</Text>
      <Text style={styles.emptyText}>
        请选择位置以获取天气信息
      </Text>
      {onLocationPress && (
        <TouchableOpacity style={styles.selectLocationButton} onPress={onLocationPress}>
          <Text style={styles.selectLocationButtonText}>选择位置</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading && !currentWeather) {
    return (
      <View style={[styles.container, style]}>
        {renderLoading()}
      </View>
    );
  }

  if (!location) {
    return (
      <View style={[styles.container, style]}>
        {renderEmpty()}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {renderCurrentWeather()}
      {renderForecast()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  currentWeatherCard: {
    borderRadius: borderRadius.xl,
    padding: spacing[6],
    marginBottom: spacing[4],
    ...shadows.lg,
  },
  currentWeatherHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: spacing[1],
  },
  locationText: {
    fontSize: 14,
    color: colors.surface,
    fontWeight: '500',
  },
  refreshButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentWeatherContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  currentWeatherLeft: {
    flex: 1,
  },
  currentTemperature: {
    fontSize: 64,
    fontWeight: '200',
    color: colors.surface,
    lineHeight: 64,
  },
  currentCondition: {
    fontSize: 18,
    color: colors.surface,
    fontWeight: '500',
    marginBottom: spacing[1],
  },
  feelsLike: {
    fontSize: 14,
    color: colors.surface,
    opacity: 0.8,
  },
  currentWeatherRight: {
    alignItems: 'center',
  },
  weatherDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing[2],
  },
  weatherDetailItem: {
    alignItems: 'center',
    gap: spacing[1],
  },
  weatherDetailText: {
    fontSize: 12,
    color: colors.surface,
    fontWeight: '500',
  },
  lastUpdated: {
    fontSize: 12,
    color: colors.surface,
    opacity: 0.7,
    textAlign: 'center',
  },
  forecastContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.xl,
    padding: spacing[4],
    ...shadows.md,
  },
  forecastTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing[3],
  },
  forecastScrollContent: {
    gap: spacing[3],
  },
  dayCard: {
    alignItems: 'center',
    backgroundColor: colors.neutral[100],
    borderRadius: borderRadius.lg,
    padding: spacing[3],
    minWidth: 80,
  },
  todayCard: {
    backgroundColor: colors.primary[100],
  },
  dayName: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    marginBottom: spacing[2],
  },
  todayDayName: {
    color: colors.primary[700],
  },
  dayIconContainer: {
    marginBottom: spacing[2],
  },
  dayTemperature: {
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  dayTempHigh: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  todayTempHigh: {
    color: colors.primary[700],
  },
  dayTempLow: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  todayTempLow: {
    color: colors.primary[600],
  },
  dayCondition: {
    fontSize: 10,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  todayCondition: {
    color: colors.primary[600],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  loadingText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: spacing[3],
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[6],
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing[3],
    marginBottom: spacing[2],
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: spacing[4],
  },
  selectLocationButton: {
    backgroundColor: colors.primary[500],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
  },
  selectLocationButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.surface,
  },
});
