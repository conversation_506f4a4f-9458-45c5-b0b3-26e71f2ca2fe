/**
 * Trekmate 4.0 - 发布准备脚本
 * 自动化发布前的检查、优化和构建流程
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ============================================================================
// 配置常量
// ============================================================================

const CONFIG = {
  APP_NAME: 'Trekmate',
  VERSION: '4.0.0',
  BUILD_NUMBER: process.env.BUILD_NUMBER || '1',
  ENVIRONMENT: process.env.NODE_ENV || 'production',
  
  // 路径配置
  PATHS: {
    ROOT: process.cwd(),
    SRC: path.join(process.cwd(), 'src'),
    ASSETS: path.join(process.cwd(), 'assets'),
    BUILD: path.join(process.cwd(), 'build'),
    DIST: path.join(process.cwd(), 'dist'),
  },
  
  // 检查配置
  CHECKS: {
    LINT: true,
    TESTS: true,
    TYPE_CHECK: true,
    BUNDLE_SIZE: true,
    SECURITY: true,
    PERFORMANCE: true,
  },
  
  // 优化配置
  OPTIMIZATION: {
    IMAGES: true,
    BUNDLE: true,
    CACHE: true,
    MINIFY: true,
  },
  
  // 构建配置
  BUILD: {
    PLATFORMS: ['ios', 'android'],
    PROFILES: ['preview', 'production'],
    CLEAN_BUILD: true,
  },
};

// ============================================================================
// 工具函数
// ============================================================================

const log = {
  info: (message) => console.log(`ℹ️  ${message}`),
  success: (message) => console.log(`✅ ${message}`),
  warning: (message) => console.log(`⚠️  ${message}`),
  error: (message) => console.log(`❌ ${message}`),
  step: (step, total, message) => console.log(`[${step}/${total}] ${message}`),
};

const executeCommand = (command, options = {}) => {
  try {
    log.info(`执行命令: ${command}`);
    const result = execSync(command, {
      stdio: 'inherit',
      cwd: CONFIG.PATHS.ROOT,
      ...options,
    });
    return { success: true, result };
  } catch (error) {
    log.error(`命令执行失败: ${command}`);
    log.error(error.message);
    return { success: false, error };
  }
};

const checkFileExists = (filePath) => {
  return fs.existsSync(filePath);
};

const readJsonFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    log.error(`读取文件失败: ${filePath}`);
    return null;
  }
};

const writeJsonFile = (filePath, data) => {
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    log.error(`写入文件失败: ${filePath}`);
    return false;
  }
};

// ============================================================================
// 检查函数
// ============================================================================

const runPreflightChecks = async () => {
  log.step(1, 8, '运行预检查');
  
  const checks = [];
  
  // 检查必要文件
  const requiredFiles = [
    'package.json',
    'app.json',
    'App.tsx',
    'babel.config.js',
    'metro.config.js',
  ];
  
  for (const file of requiredFiles) {
    if (!checkFileExists(path.join(CONFIG.PATHS.ROOT, file))) {
      checks.push(`缺少必要文件: ${file}`);
    }
  }
  
  // 检查依赖
  const packageJson = readJsonFile(path.join(CONFIG.PATHS.ROOT, 'package.json'));
  if (!packageJson) {
    checks.push('无法读取 package.json');
  } else {
    // 检查关键依赖
    const requiredDeps = [
      'react',
      'react-native',
      'expo',
      '@react-navigation/native',
    ];
    
    for (const dep of requiredDeps) {
      if (!packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]) {
        checks.push(`缺少关键依赖: ${dep}`);
      }
    }
  }
  
  // 检查环境变量
  const requiredEnvVars = ['NODE_ENV'];
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      log.warning(`环境变量未设置: ${envVar}`);
    }
  }
  
  if (checks.length > 0) {
    log.error('预检查失败:');
    checks.forEach(check => log.error(`  - ${check}`));
    return false;
  }
  
  log.success('预检查通过');
  return true;
};

const runLinting = async () => {
  if (!CONFIG.CHECKS.LINT) {
    log.info('跳过代码检查');
    return true;
  }
  
  log.step(2, 8, '运行代码检查');
  
  const lintResult = executeCommand('npx eslint . --ext .ts,.tsx,.js,.jsx');
  if (!lintResult.success) {
    log.error('代码检查失败');
    return false;
  }
  
  log.success('代码检查通过');
  return true;
};

const runTypeCheck = async () => {
  if (!CONFIG.CHECKS.TYPE_CHECK) {
    log.info('跳过类型检查');
    return true;
  }
  
  log.step(3, 8, '运行类型检查');
  
  const typeCheckResult = executeCommand('npx tsc --noEmit');
  if (!typeCheckResult.success) {
    log.error('类型检查失败');
    return false;
  }
  
  log.success('类型检查通过');
  return true;
};

const runTests = async () => {
  if (!CONFIG.CHECKS.TESTS) {
    log.info('跳过测试');
    return true;
  }
  
  log.step(4, 8, '运行测试套件');
  
  const testResult = executeCommand('npm test -- --coverage --watchAll=false');
  if (!testResult.success) {
    log.error('测试失败');
    return false;
  }
  
  log.success('测试通过');
  return true;
};

const runSecurityCheck = async () => {
  if (!CONFIG.CHECKS.SECURITY) {
    log.info('跳过安全检查');
    return true;
  }
  
  log.step(5, 8, '运行安全检查');
  
  const auditResult = executeCommand('npm audit --audit-level=moderate');
  if (!auditResult.success) {
    log.warning('发现安全漏洞，请检查 npm audit 输出');
  }
  
  log.success('安全检查完成');
  return true;
};

// ============================================================================
// 优化函数
// ============================================================================

const optimizeAssets = async () => {
  if (!CONFIG.OPTIMIZATION.IMAGES) {
    log.info('跳过资源优化');
    return true;
  }
  
  log.step(6, 8, '优化应用资源');
  
  // 优化图片
  const assetsPath = CONFIG.PATHS.ASSETS;
  if (checkFileExists(assetsPath)) {
    log.info('优化图片资源...');
    // 这里可以添加图片压缩逻辑
    // 例如使用 imagemin 或其他工具
  }
  
  // 清理未使用的资源
  log.info('清理未使用的资源...');
  
  log.success('资源优化完成');
  return true;
};

const analyzeBundleSize = async () => {
  if (!CONFIG.CHECKS.BUNDLE_SIZE) {
    log.info('跳过包大小分析');
    return true;
  }
  
  log.step(7, 8, '分析包大小');
  
  // 生成包大小报告
  const bundleAnalyzeResult = executeCommand('npx expo export --platform all');
  if (!bundleAnalyzeResult.success) {
    log.warning('包大小分析失败');
    return false;
  }
  
  log.success('包大小分析完成');
  return true;
};

// ============================================================================
// 构建函数
// ============================================================================

const updateVersionInfo = () => {
  log.info('更新版本信息...');
  
  // 更新 package.json
  const packageJson = readJsonFile(path.join(CONFIG.PATHS.ROOT, 'package.json'));
  if (packageJson) {
    packageJson.version = CONFIG.VERSION;
    writeJsonFile(path.join(CONFIG.PATHS.ROOT, 'package.json'), packageJson);
  }
  
  // 更新 app.json
  const appJson = readJsonFile(path.join(CONFIG.PATHS.ROOT, 'app.json'));
  if (appJson) {
    appJson.expo.version = CONFIG.VERSION;
    appJson.expo.ios.buildNumber = CONFIG.BUILD_NUMBER;
    appJson.expo.android.versionCode = parseInt(CONFIG.BUILD_NUMBER);
    writeJsonFile(path.join(CONFIG.PATHS.ROOT, 'app.json'), appJson);
  }
  
  log.success('版本信息已更新');
};

const buildApp = async () => {
  log.step(8, 8, '构建应用');
  
  updateVersionInfo();
  
  if (CONFIG.BUILD.CLEAN_BUILD) {
    log.info('清理构建缓存...');
    executeCommand('npx expo r -c');
  }
  
  // 构建不同平台
  for (const platform of CONFIG.BUILD.PLATFORMS) {
    log.info(`构建 ${platform} 平台...`);
    
    for (const profile of CONFIG.BUILD.PROFILES) {
      log.info(`使用 ${profile} 配置构建...`);
      
      const buildCommand = `npx eas build --platform ${platform} --profile ${profile} --non-interactive`;
      const buildResult = executeCommand(buildCommand);
      
      if (!buildResult.success) {
        log.error(`${platform} 平台构建失败 (${profile})`);
        return false;
      }
    }
  }
  
  log.success('应用构建完成');
  return true;
};

// ============================================================================
// 报告生成
// ============================================================================

const generateReleaseReport = () => {
  log.info('生成发布报告...');
  
  const report = {
    appName: CONFIG.APP_NAME,
    version: CONFIG.VERSION,
    buildNumber: CONFIG.BUILD_NUMBER,
    environment: CONFIG.ENVIRONMENT,
    buildTime: new Date().toISOString(),
    
    checks: {
      preflight: '✅ 通过',
      linting: CONFIG.CHECKS.LINT ? '✅ 通过' : '⏭️ 跳过',
      typeCheck: CONFIG.CHECKS.TYPE_CHECK ? '✅ 通过' : '⏭️ 跳过',
      tests: CONFIG.CHECKS.TESTS ? '✅ 通过' : '⏭️ 跳过',
      security: CONFIG.CHECKS.SECURITY ? '✅ 通过' : '⏭️ 跳过',
    },
    
    optimization: {
      assets: CONFIG.OPTIMIZATION.IMAGES ? '✅ 完成' : '⏭️ 跳过',
      bundleAnalysis: CONFIG.CHECKS.BUNDLE_SIZE ? '✅ 完成' : '⏭️ 跳过',
    },
    
    build: {
      platforms: CONFIG.BUILD.PLATFORMS,
      profiles: CONFIG.BUILD.PROFILES,
      status: '✅ 成功',
    },
    
    nextSteps: [
      '1. 在测试设备上验证构建',
      '2. 执行最终用户验收测试',
      '3. 准备应用商店元数据',
      '4. 提交到应用商店审核',
      '5. 准备发布说明',
    ],
  };
  
  const reportPath = path.join(CONFIG.PATHS.ROOT, 'release-report.json');
  writeJsonFile(reportPath, report);
  
  // 打印报告摘要
  console.log('\n📋 发布报告摘要:');
  console.log(`应用: ${report.appName} v${report.version} (${report.buildNumber})`);
  console.log(`构建时间: ${report.buildTime}`);
  console.log(`环境: ${report.environment}`);
  console.log('\n检查结果:');
  Object.entries(report.checks).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  console.log('\n优化结果:');
  Object.entries(report.optimization).forEach(([key, value]) => {
    console.log(`  ${key}: ${value}`);
  });
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  
  log.success('发布报告生成完成');
};

// ============================================================================
// 主流程
// ============================================================================

const main = async () => {
  console.log(`🚀 开始 ${CONFIG.APP_NAME} v${CONFIG.VERSION} 发布准备\n`);
  
  try {
    // 运行所有检查和构建步骤
    const steps = [
      runPreflightChecks,
      runLinting,
      runTypeCheck,
      runTests,
      runSecurityCheck,
      optimizeAssets,
      analyzeBundleSize,
      buildApp,
    ];
    
    for (const step of steps) {
      const result = await step();
      if (!result) {
        log.error('发布准备失败');
        process.exit(1);
      }
    }
    
    // 生成发布报告
    generateReleaseReport();
    
    console.log('\n🎉 发布准备完成！');
    console.log('应用已准备好发布到应用商店。');
    
  } catch (error) {
    log.error('发布准备过程中发生错误:');
    log.error(error.message);
    process.exit(1);
  }
};

// 运行主流程
if (require.main === module) {
  main();
}

module.exports = {
  CONFIG,
  runPreflightChecks,
  runLinting,
  runTypeCheck,
  runTests,
  runSecurityCheck,
  optimizeAssets,
  analyzeBundleSize,
  buildApp,
  generateReleaseReport,
};
