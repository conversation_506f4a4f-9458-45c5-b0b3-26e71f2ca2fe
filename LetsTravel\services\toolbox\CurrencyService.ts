/**
 * Trekmate 4.0 - 货币服务
 * 提供汇率查询和货币转换功能
 */

import { defaultStorage } from '../storage/StorageService';
import type { ServiceResponse } from '../../types/CoreServices';

// ============================================================================
// 货币服务数据类型
// ============================================================================

export interface CurrencyRate {
  code: string;
  name: string;
  symbol: string;
  rate: number; // 相对于基准货币(USD)的汇率
  lastUpdated: string;
}

export interface CurrencyConversion {
  fromCurrency: string;
  toCurrency: string;
  originalAmount: number;
  convertedAmount: number;
  exchangeRate: number;
  timestamp: string;
}

export interface ExchangeRateHistory {
  date: string;
  rate: number;
}

// ============================================================================
// 货币服务接口
// ============================================================================

export interface CurrencyService {
  // 基础功能
  getSupportedCurrencies(): Promise<ServiceResponse<CurrencyRate[]>>;
  getExchangeRate(fromCurrency: string, toCurrency: string): Promise<ServiceResponse<number>>;
  convertCurrency(fromCurrency: string, toCurrency: string, amount: number): Promise<ServiceResponse<CurrencyConversion>>;
  
  // 历史数据
  getHistoricalRates(fromCurrency: string, toCurrency: string, days: number): Promise<ServiceResponse<ExchangeRateHistory[]>>;
  
  // 缓存管理
  refreshRates(): Promise<ServiceResponse<void>>;
  getLastUpdateTime(): Promise<ServiceResponse<Date>>;
}

// ============================================================================
// 货币服务实现
// ============================================================================

class CurrencyServiceImpl implements CurrencyService {
  private readonly CACHE_PREFIX = 'currency_cache_';
  private readonly CACHE_EXPIRY = 60 * 60 * 1000; // 1小时

  // Mock 汇率数据
  private readonly mockRates: CurrencyRate[] = [
    {
      code: 'USD',
      name: '美元',
      symbol: '$',
      rate: 1.0000,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'EUR',
      name: '欧元',
      symbol: '€',
      rate: 0.8500,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'GBP',
      name: '英镑',
      symbol: '£',
      rate: 0.7300,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'JPY',
      name: '日元',
      symbol: '¥',
      rate: 149.5000,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'CNY',
      name: '人民币',
      symbol: '¥',
      rate: 7.2500,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'MYR',
      name: '马来西亚林吉特',
      symbol: 'RM',
      rate: 4.6800,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'SGD',
      name: '新加坡元',
      symbol: 'S$',
      rate: 1.3500,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'THB',
      name: '泰铢',
      symbol: '฿',
      rate: 35.8000,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'KRW',
      name: '韩元',
      symbol: '₩',
      rate: 1320.0000,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'AUD',
      name: '澳元',
      symbol: 'A$',
      rate: 1.5200,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'CAD',
      name: '加元',
      symbol: 'C$',
      rate: 1.3600,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'CHF',
      name: '瑞士法郎',
      symbol: 'CHF',
      rate: 0.8800,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'HKD',
      name: '港币',
      symbol: 'HK$',
      rate: 7.8000,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'TWD',
      name: '新台币',
      symbol: 'NT$',
      rate: 31.5000,
      lastUpdated: new Date().toISOString(),
    },
    {
      code: 'INR',
      name: '印度卢比',
      symbol: '₹',
      rate: 83.2000,
      lastUpdated: new Date().toISOString(),
    },
  ];

  // ========================================================================
  // 基础功能
  // ========================================================================

  async getSupportedCurrencies(): Promise<ServiceResponse<CurrencyRate[]>> {
    try {
      // 尝试从缓存加载
      const cached = await this.loadFromCache('supported_currencies');
      if (cached) {
        return {
          success: true,
          data: cached,
        };
      }

      // 模拟API调用延迟
      await this.simulateNetworkDelay();

      // 添加一些随机波动到汇率
      const ratesWithFluctuation = this.mockRates.map(rate => ({
        ...rate,
        rate: rate.code === 'USD' ? 1.0000 : rate.rate * (0.98 + Math.random() * 0.04),
        lastUpdated: new Date().toISOString(),
      }));

      // 保存到缓存
      await this.saveToCache('supported_currencies', ratesWithFluctuation);

      return {
        success: true,
        data: ratesWithFluctuation,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取货币列表失败',
      };
    }
  }

  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<ServiceResponse<number>> {
    try {
      if (fromCurrency === toCurrency) {
        return {
          success: true,
          data: 1.0000,
        };
      }

      const currenciesResponse = await this.getSupportedCurrencies();
      if (!currenciesResponse.success || !currenciesResponse.data) {
        return {
          success: false,
          error: '无法获取汇率数据',
        };
      }

      const currencies = currenciesResponse.data;
      const fromRate = currencies.find(c => c.code === fromCurrency);
      const toRate = currencies.find(c => c.code === toCurrency);

      if (!fromRate || !toRate) {
        return {
          success: false,
          error: '不支持的货币类型',
        };
      }

      // 计算汇率：通过USD作为中间货币
      const exchangeRate = toRate.rate / fromRate.rate;

      return {
        success: true,
        data: exchangeRate,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取汇率失败',
      };
    }
  }

  async convertCurrency(
    fromCurrency: string, 
    toCurrency: string, 
    amount: number
  ): Promise<ServiceResponse<CurrencyConversion>> {
    try {
      if (amount <= 0) {
        return {
          success: false,
          error: '金额必须大于0',
        };
      }

      const rateResponse = await this.getExchangeRate(fromCurrency, toCurrency);
      if (!rateResponse.success || rateResponse.data === undefined) {
        return {
          success: false,
          error: rateResponse.error || '获取汇率失败',
        };
      }

      const exchangeRate = rateResponse.data;
      const convertedAmount = amount * exchangeRate;

      const conversion: CurrencyConversion = {
        fromCurrency,
        toCurrency,
        originalAmount: amount,
        convertedAmount,
        exchangeRate,
        timestamp: new Date().toISOString(),
      };

      return {
        success: true,
        data: conversion,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '货币转换失败',
      };
    }
  }

  // ========================================================================
  // 历史数据
  // ========================================================================

  async getHistoricalRates(
    fromCurrency: string, 
    toCurrency: string, 
    days: number
  ): Promise<ServiceResponse<ExchangeRateHistory[]>> {
    try {
      await this.simulateNetworkDelay();

      // 生成模拟历史数据
      const history: ExchangeRateHistory[] = [];
      const currentRateResponse = await this.getExchangeRate(fromCurrency, toCurrency);
      
      if (!currentRateResponse.success || currentRateResponse.data === undefined) {
        return {
          success: false,
          error: '无法获取当前汇率',
        };
      }

      const baseRate = currentRateResponse.data;
      
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        // 添加随机波动
        const fluctuation = 0.95 + Math.random() * 0.1; // ±5%波动
        const rate = baseRate * fluctuation;
        
        history.push({
          date: date.toISOString().split('T')[0],
          rate: Number(rate.toFixed(4)),
        });
      }

      return {
        success: true,
        data: history,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取历史汇率失败',
      };
    }
  }

  // ========================================================================
  // 缓存管理
  // ========================================================================

  async refreshRates(): Promise<ServiceResponse<void>> {
    try {
      // 清除缓存
      await this.clearCache();
      
      // 重新获取数据
      await this.getSupportedCurrencies();

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '刷新汇率失败',
      };
    }
  }

  async getLastUpdateTime(): Promise<ServiceResponse<Date>> {
    try {
      const cached = await this.loadFromCache('supported_currencies');
      if (cached && cached.length > 0) {
        const lastUpdated = new Date(cached[0].lastUpdated);
        return {
          success: true,
          data: lastUpdated,
        };
      }

      return {
        success: true,
        data: new Date(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取更新时间失败',
      };
    }
  }

  // ========================================================================
  // 辅助方法
  // ========================================================================

  private async simulateNetworkDelay(): Promise<void> {
    const delay = 500 + Math.random() * 1000; // 0.5-1.5秒
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  private async loadFromCache(key: string): Promise<any> {
    try {
      const cached = await defaultStorage.getObject<{
        data: any;
        timestamp: number;
      }>(`${this.CACHE_PREFIX}${key}`);

      if (cached && Date.now() - cached.timestamp < this.CACHE_EXPIRY) {
        return cached.data;
      }

      return null;
    } catch (error) {
      console.warn('加载缓存失败:', error);
      return null;
    }
  }

  private async saveToCache(key: string, data: any): Promise<void> {
    try {
      await defaultStorage.setObject(`${this.CACHE_PREFIX}${key}`, {
        data,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.warn('保存缓存失败:', error);
    }
  }

  private async clearCache(): Promise<void> {
    try {
      const keys = ['supported_currencies'];
      for (const key of keys) {
        await defaultStorage.removeItem(`${this.CACHE_PREFIX}${key}`);
      }
    } catch (error) {
      console.warn('清除缓存失败:', error);
    }
  }
}

// ============================================================================
// 导出服务实例
// ============================================================================

export const currencyService = new CurrencyServiceImpl();
