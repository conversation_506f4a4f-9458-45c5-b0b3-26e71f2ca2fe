{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "expo-gl", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "expo-gl::@6890427a1f51a3e7e1df", "jsonFile": "target-expo-gl-Debug-708ea0ed49df10b9e0c6.json", "name": "expo-gl", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android/.cxx/Debug/6k1h1t2m/armeabi-v7a", "source": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-gl/android"}, "version": {"major": 2, "minor": 3}}