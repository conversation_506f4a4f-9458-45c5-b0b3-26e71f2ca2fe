/**
 * Trekmate 4.0 - 字符串常量定义
 * 为未来i18n国际化做准备，所有用户可见文本都在此定义
 */

// 应用基础信息
export const APP_INFO = {
  NAME: 'Trekmate',
  VERSION: '4.0.0',
  TAGLINE: '智能旅行规划助手',
} as const;

// 导航标签
export const NAVIGATION = {
  TABS: {
    JOURNEY: '行程',
    EXPLORE: '探索', 
    CAMERA: '相机',
    TOOLBOX: '工具箱',
    ME: '我',
  },
  SCREENS: {
    WELCOME: '欢迎',
    LOGIN: '登录',
    ONBOARDING: '引导',
    SPLASH: '启动',
  },
} as const;

// 通用操作
export const COMMON = {
  ACTIONS: {
    SAVE: '保存',
    CANCEL: '取消',
    DELETE: '删除',
    EDIT: '编辑',
    ADD: '添加',
    SHARE: '分享',
    SEARCH: '搜索',
    FILTER: '筛选',
    SORT: '排序',
    REFRESH: '刷新',
    BACK: '返回',
    NEXT: '下一步',
    PREVIOUS: '上一步',
    CONFIRM: '确认',
    SUBMIT: '提交',
    CLOSE: '关闭',
    OPEN: '打开',
    VIEW: '查看',
    COPY: '复制',
    PASTE: '粘贴',
    SELECT: '选择',
    CLEAR: '清除',
  },
  STATUS: {
    LOADING: '加载中...',
    SUCCESS: '成功',
    ERROR: '错误',
    WARNING: '警告',
    INFO: '信息',
    EMPTY: '暂无数据',
    OFFLINE: '离线',
    ONLINE: '在线',
    SYNCING: '同步中...',
    COMPLETED: '已完成',
    PENDING: '待处理',
    FAILED: '失败',
  },
  MESSAGES: {
    NETWORK_ERROR: '网络连接失败，请检查网络设置',
    PERMISSION_DENIED: '权限被拒绝',
    OPERATION_SUCCESS: '操作成功',
    OPERATION_FAILED: '操作失败',
    DATA_SAVED: '数据已保存',
    DATA_SYNC_SUCCESS: '数据同步成功',
    DATA_SYNC_FAILED: '数据同步失败',
  },
} as const;

// 行程模块
export const JOURNEY = {
  TITLE: '我的行程',
  CREATE_NEW: '创建新行程',
  EDIT_JOURNEY: '编辑行程',
  JOURNEY_DETAILS: '行程详情',
  ADD_ACTIVITY: '添加活动',
  TIMELINE: '时间线',
  BUDGET: '预算',
  DURATION: '时长',
  DESTINATION: '目的地',
  START_DATE: '开始日期',
  END_DATE: '结束日期',
  ACTIVITIES: '活动',
  NOTES: '备注',
  SHARE_JOURNEY: '分享行程',
  EXPORT_JOURNEY: '导出行程',
  AI_SUGGESTIONS: 'AI建议',
  OPTIMIZE_ROUTE: '优化路线',
  TIME_CONFLICT: '时间冲突',
  BUDGET_EXCEEDED: '预算超支',
} as const;

// 探索模块
export const EXPLORE = {
  TITLE: '探索',
  SEARCH_PLACEHOLDER: '搜索景点、酒店、餐厅...',
  MAP_VIEW: '地图视图',
  LIST_VIEW: '列表视图',
  NEARBY: '附近',
  POPULAR: '热门',
  RECOMMENDED: '推荐',
  ATTRACTIONS: '景点',
  HOTELS: '酒店',
  RESTAURANTS: '餐厅',
  SHOPPING: '购物',
  ENTERTAINMENT: '娱乐',
  COMMUNITY: '社区动态',
  DISCOVER: '发现',
  FAVORITES: '收藏',
  REVIEWS: '评价',
  PHOTOS: '照片',
  OPENING_HOURS: '营业时间',
  CONTACT_INFO: '联系方式',
  DIRECTIONS: '路线',
  BOOK_NOW: '立即预订',
} as const;

// 相机模块
export const CAMERA = {
  TITLE: '智能相机',
  AR_TRANSLATION: 'AR翻译',
  TEXT_RECOGNITION: '文字识别',
  CURRENCY_DETECTION: '货币识别',
  SCAN_TEXT: '扫描文字',
  TRANSLATE: '翻译',
  COPY_TEXT: '复制文字',
  SAVE_IMAGE: '保存图片',
  CAMERA_PERMISSION: '相机权限',
  CAMERA_PERMISSION_MESSAGE: '需要相机权限才能使用此功能',
  PROCESSING: '处理中...',
  TRANSLATION_RESULT: '翻译结果',
  DETECTED_TEXT: '识别文字',
  LANGUAGE_DETECTED: '检测语言',
  TARGET_LANGUAGE: '目标语言',
} as const;

// 工具箱模块
export const TOOLBOX = {
  TITLE: '工具箱',
  CURRENCY_CONVERTER: '汇率转换',
  WEATHER: '天气',
  ROUTE_PLANNER: '路线规划',
  EMERGENCY_CONTACTS: '紧急联系',
  LANGUAGE_TRANSLATOR: '语言翻译',
  UNIT_CONVERTER: '单位转换',
  TIME_ZONE: '时区',
  TRAVEL_TIPS: '旅行贴士',
  OFFLINE_MAPS: '离线地图',
  EXPENSE_TRACKER: '费用追踪',
  PACKING_LIST: '打包清单',
  TRAVEL_DOCUMENTS: '旅行证件',

  // 汇率转换详细常量
  FROM_CURRENCY: '从',
  TO_CURRENCY: '到',
  AMOUNT: '金额',
  CONVERT: '转换',
  EXCHANGE_RATE: '汇率',
  RATE_UPDATED: '汇率已更新',
  ENTER_AMOUNT: '输入金额',
  SWAP_CURRENCIES: '交换货币',
  QUICK_CURRENCIES: '常用货币',
  CONVERSION_RESULT: '转换结果',

  // 天气详细常量
  CURRENT_WEATHER: '当前天气',
  FORECAST: '预报',
  TEMPERATURE: '温度',
  HUMIDITY: '湿度',
  WIND_SPEED: '风速',
  FEELS_LIKE: '体感温度',
  VISIBILITY: '能见度',
  PRESSURE: '气压',
  UV_INDEX: 'UV指数',
  WEATHER_ALERTS: '天气警报',
  HOURLY_FORECAST: '小时预报',
  DAILY_FORECAST: '每日预报',
  WEATHER_CONDITIONS: '天气状况',

  // 通用工具箱常量
  LOADING_DATA: '加载数据中...',
  REFRESH_DATA: '刷新数据',
  LAST_UPDATED: '最后更新',
  NO_DATA_AVAILABLE: '暂无数据',
  NETWORK_ERROR: '网络连接失败',
  SELECT_LOCATION: '选择位置',
  CURRENT_LOCATION: '当前位置',
} as const;

// 个人中心模块
export const PROFILE = {
  TITLE: '个人中心',
  MY_PROFILE: '我的资料',
  MY_JOURNEYS: '我的行程',
  MY_FAVORITES: '我的收藏',
  MY_POSTS: '我的动态',
  SETTINGS: '设置',
  SYNC_DATA: '数据同步',
  ACCOUNT: '账户',
  PRIVACY: '隐私',
  NOTIFICATIONS: '通知',
  LANGUAGE: '语言',
  THEME: '主题',
  ABOUT: '关于',
  HELP: '帮助',
  FEEDBACK: '反馈',
  LOGOUT: '退出登录',
  EDIT_PROFILE: '编辑资料',
  CHANGE_PASSWORD: '修改密码',
  DELETE_ACCOUNT: '删除账户',
} as const;

// 社区功能
export const COMMUNITY = {
  TITLE: '社区',
  POST: '发布',
  COMMENT: '评论',
  LIKE: '点赞',
  SHARE: '分享',
  FOLLOW: '关注',
  UNFOLLOW: '取消关注',
  FOLLOWERS: '粉丝',
  FOLLOWING: '关注',
  POSTS: '动态',
  CREATE_POST: '发布动态',
  EDIT_POST: '编辑动态',
  DELETE_POST: '删除动态',
  ADD_PHOTO: '添加照片',
  ADD_LOCATION: '添加位置',
  POST_PLACEHOLDER: '分享你的旅行体验...',
  COMMENT_PLACEHOLDER: '写下你的评论...',
  NO_POSTS: '暂无动态',
  NO_COMMENTS: '暂无评论',
  REPORT: '举报',
  BLOCK: '屏蔽',
} as const;

// AI助手
export const AI_ASSISTANT = {
  TITLE: 'AI助手',
  CHAT_PLACEHOLDER: '问我任何关于旅行的问题...',
  SUGGESTIONS: '建议',
  RECOMMENDATIONS: '推荐',
  PLANNING_HELP: '规划帮助',
  ROUTE_OPTIMIZATION: '路线优化',
  BUDGET_ADVICE: '预算建议',
  WEATHER_INFO: '天气信息',
  LOCAL_TIPS: '当地贴士',
  LANGUAGE_HELP: '语言帮助',
  EMERGENCY_INFO: '紧急信息',
  THINKING: '思考中...',
  NO_RESPONSE: '暂无回复',
  TRY_AGAIN: '重试',
} as const;

// 错误信息
export const ERRORS = {
  NETWORK: '网络连接错误',
  SERVER: '服务器错误',
  PERMISSION: '权限错误',
  VALIDATION: '输入验证错误',
  NOT_FOUND: '未找到',
  UNAUTHORIZED: '未授权',
  FORBIDDEN: '禁止访问',
  TIMEOUT: '请求超时',
  UNKNOWN: '未知错误',
  CAMERA_NOT_AVAILABLE: '相机不可用',
  LOCATION_NOT_AVAILABLE: '位置服务不可用',
  STORAGE_FULL: '存储空间不足',
  FILE_TOO_LARGE: '文件过大',
  INVALID_FORMAT: '格式无效',
} as const;

// 成功信息
export const SUCCESS = {
  SAVED: '保存成功',
  UPDATED: '更新成功',
  DELETED: '删除成功',
  SHARED: '分享成功',
  UPLOADED: '上传成功',
  SYNCED: '同步成功',
  SENT: '发送成功',
  COPIED: '复制成功',
  EXPORTED: '导出成功',
  IMPORTED: '导入成功',
} as const;

// 时间相关
export const TIME = {
  NOW: '现在',
  TODAY: '今天',
  YESTERDAY: '昨天',
  TOMORROW: '明天',
  THIS_WEEK: '本周',
  LAST_WEEK: '上周',
  NEXT_WEEK: '下周',
  THIS_MONTH: '本月',
  LAST_MONTH: '上月',
  NEXT_MONTH: '下月',
  MORNING: '上午',
  AFTERNOON: '下午',
  EVENING: '晚上',
  NIGHT: '夜晚',
  MINUTES_AGO: '分钟前',
  HOURS_AGO: '小时前',
  DAYS_AGO: '天前',
  WEEKS_AGO: '周前',
  MONTHS_AGO: '月前',
} as const;

// 单位
export const UNITS = {
  CURRENCY: {
    USD: '美元',
    EUR: '欧元',
    CNY: '人民币',
    JPY: '日元',
    GBP: '英镑',
  },
  DISTANCE: {
    KM: '公里',
    M: '米',
    MILE: '英里',
    FEET: '英尺',
  },
  TEMPERATURE: {
    CELSIUS: '摄氏度',
    FAHRENHEIT: '华氏度',
  },
  WEIGHT: {
    KG: '公斤',
    LB: '磅',
    G: '克',
  },
} as const;

// AI助手功能
export const AI_ASSISTANT = {
  AI_ASSISTANT: 'AI助手',
  SMART_SUGGESTIONS: '智能建议',
  OPTIMIZE_JOURNEY: '优化行程',
  OPTIMIZING: '优化中...',
  ANALYZING: '分析中...',
  ASK_ANYTHING: '问我任何关于旅行的问题...',

  // 建议相关
  SUGGESTION_APPLIED: '建议已应用',
  NO_SUGGESTIONS: '暂无建议',
  HIGH_CONFIDENCE: '高度推荐',
  MEDIUM_CONFIDENCE: '建议考虑',
  LOW_CONFIDENCE: '可选建议',

  // 优化相关
  OPTIMIZATION_COMPLETE: '优化完成',
  OPTIMIZATION_FAILED: '优化失败',
  ROUTE_OPTIMIZED: '路线已优化',
  TIME_OPTIMIZED: '时间已优化',
  BUDGET_OPTIMIZED: '预算已优化',

  // 聊天相关
  CHAT_WITH_AI: '与AI对话',
  AI_THINKING: 'AI助手正在思考...',
  AI_ONLINE: '在线',
  AI_OFFLINE: '离线',

  // 错误提示
  NETWORK_ERROR: 'AI服务暂时不可用',
  PROCESSING_ERROR: '处理请求时出错',
  INVALID_REQUEST: '请求格式不正确',

  // 功能说明
  FEATURE_DESCRIPTION: 'AI助手可以帮您优化行程、推荐景点、回答旅行问题',
  OPTIMIZATION_DESCRIPTION: '基于AI算法优化您的行程安排',
  SUGGESTION_DESCRIPTION: '根据您的偏好提供个性化建议',
} as const;

// 导出所有常量的类型定义，便于TypeScript类型检查
export type StringConstants = typeof APP_INFO | typeof NAVIGATION | typeof COMMON |
  typeof JOURNEY | typeof EXPLORE | typeof CAMERA | typeof TOOLBOX | typeof PROFILE |
  typeof COMMUNITY | typeof AI_ASSISTANT | typeof ERRORS | typeof SUCCESS | typeof TIME | typeof UNITS;
