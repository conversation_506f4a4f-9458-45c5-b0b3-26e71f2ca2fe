/**
 * Trekmate 4.0 - 社区动态组件
 * 展示社区帖子列表，支持下拉刷新、上拉加载更多等功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { COMMUNITY } from '../../constants/Strings';
import PostCard from './PostCard';
import { communityService } from '../../services/community/CommunityService';
import type { CommunityPost, FeedFilter } from '../../services/community/CommunityService';

// ============================================================================
// 社区动态Props
// ============================================================================

interface CommunityFeedProps {
  filter?: FeedFilter;
  onPostPress?: (post: CommunityPost) => void;
  onUserPress?: (userId: string) => void;
  onLocationPress?: (location: CommunityPost['location']) => void;
  onJourneyPress?: (journeyId: string) => void;
  onCreatePost?: () => void;
  style?: any;
}

// ============================================================================
// 社区动态组件
// ============================================================================

export default function CommunityFeed({
  filter,
  onPostPress,
  onUserPress,
  onLocationPress,
  onJourneyPress,
  onCreatePost,
  style,
}: CommunityFeedProps) {
  const [posts, setPosts] = useState<CommunityPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载帖子数据
  const loadPosts = useCallback(async (isRefresh = false) => {
    if (loading && !isRefresh) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
        setError(null);
      } else {
        setLoading(true);
      }

      const currentFilter = {
        ...filter,
        limit: 20,
        offset: isRefresh ? 0 : posts.length,
      };

      const response = await communityService.getFeed(currentFilter);

      if (response.success && response.data) {
        if (isRefresh) {
          setPosts(response.data);
        } else {
          setPosts(prev => [...prev, ...response.data!]);
        }

        // 检查是否还有更多数据
        setHasMore(response.data.length === 20);
      } else {
        setError(response.error || '加载失败');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '网络错误');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [filter, posts.length, loading]);

  // 初始加载
  useEffect(() => {
    loadPosts();
  }, [filter]);

  // 下拉刷新
  const handleRefresh = useCallback(() => {
    loadPosts(true);
  }, [loadPosts]);

  // 上拉加载更多
  const handleLoadMore = useCallback(() => {
    if (!loadingMore && hasMore && posts.length > 0) {
      setLoadingMore(true);
      loadPosts();
    }
  }, [loadingMore, hasMore, posts.length, loadPosts]);

  // 处理点赞
  const handleLike = useCallback(async (postId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      if (post.isLiked) {
        await communityService.unlikePost(postId);
      } else {
        await communityService.likePost(postId);
      }

      // 更新本地状态
      setPosts(prev => prev.map(p => 
        p.id === postId 
          ? { 
              ...p, 
              isLiked: !p.isLiked,
              likes: p.isLiked ? p.likes - 1 : p.likes + 1
            }
          : p
      ));
    } catch (error) {
      Alert.alert('错误', '操作失败，请重试');
    }
  }, [posts]);

  // 处理评论
  const handleComment = useCallback((post: CommunityPost) => {
    onPostPress?.(post);
  }, [onPostPress]);

  // 处理分享
  const handleShare = useCallback(async (post: CommunityPost) => {
    try {
      await communityService.sharePost(post.id);
      
      // 更新本地状态
      setPosts(prev => prev.map(p => 
        p.id === post.id 
          ? { ...p, shares: p.shares + 1 }
          : p
      ));

      Alert.alert('成功', '分享成功');
    } catch (error) {
      Alert.alert('错误', '分享失败，请重试');
    }
  }, []);

  // 处理收藏
  const handleBookmark = useCallback(async (postId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      if (post.isBookmarked) {
        await communityService.unbookmarkPost(postId);
      } else {
        await communityService.bookmarkPost(postId);
      }

      // 更新本地状态
      setPosts(prev => prev.map(p => 
        p.id === postId 
          ? { ...p, isBookmarked: !p.isBookmarked }
          : p
      ));
    } catch (error) {
      Alert.alert('错误', '操作失败，请重试');
    }
  }, [posts]);

  // 渲染帖子项
  const renderPost = useCallback(({ item }: { item: CommunityPost }) => (
    <PostCard
      post={item}
      onPress={onPostPress}
      onLike={handleLike}
      onComment={handleComment}
      onShare={handleShare}
      onBookmark={handleBookmark}
      onUserPress={onUserPress}
      onLocationPress={onLocationPress}
      onJourneyPress={onJourneyPress}
    />
  ), [
    onPostPress,
    handleLike,
    handleComment,
    handleShare,
    handleBookmark,
    onUserPress,
    onLocationPress,
    onJourneyPress,
  ]);

  // 渲染加载更多指示器
  const renderFooter = () => {
    if (!loadingMore) return null;

    return (
      <View style={styles.loadingFooter}>
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  };

  // 渲染空状态
  const renderEmpty = () => {
    if (loading) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.textSecondary} />
          <Text style={styles.emptyTitle}>加载失败</Text>
          <Text style={styles.emptyText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={() => loadPosts(true)}>
            <Text style={styles.retryButtonText}>重试</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="chatbubbles-outline" size={64} color={colors.textSecondary} />
        <Text style={styles.emptyTitle}>暂无动态</Text>
        <Text style={styles.emptyText}>
          {filter?.type === 'following' 
            ? '你关注的用户还没有发布动态' 
            : '这里还没有动态，快来发布第一条吧！'
          }
        </Text>
        {onCreatePost && (
          <TouchableOpacity style={styles.createButton} onPress={onCreatePost}>
            <Ionicons name="add" size={20} color={colors.surface} />
            <Text style={styles.createButtonText}>发布动态</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // 渲染头部过滤器
  const renderHeader = () => {
    if (!filter?.type) return null;

    const filterLabels = {
      all: '全部动态',
      following: '关注的人',
      nearby: '附近',
      trending: '热门',
    };

    return (
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {filterLabels[filter.type] || '动态'}
        </Text>
        {filter.type === 'nearby' && filter.location && (
          <Text style={styles.headerSubtitle}>
            半径 {(filter.location.radius / 1000).toFixed(1)}km
          </Text>
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <FlatList
        data={posts}
        renderItem={renderPost}
        keyExtractor={item => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary[500]]}
            tintColor={colors.primary[500]}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={posts.length === 0 ? styles.emptyContentContainer : undefined}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: spacing[1],
  },
  emptyContentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[6],
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing[4],
    marginBottom: spacing[2],
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: spacing[6],
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary[500],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    gap: spacing[2],
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.surface,
  },
  retryButton: {
    backgroundColor: colors.primary[500],
    borderRadius: borderRadius.md,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
  },
  retryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.surface,
  },
  loadingFooter: {
    paddingVertical: spacing[4],
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
});
