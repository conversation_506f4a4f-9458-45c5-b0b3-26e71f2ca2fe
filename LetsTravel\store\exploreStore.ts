/**
 * Trekmate 4.0 - 探索状态管理
 * 管理POI搜索、地图状态和探索相关功能
 */

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

import type { POI, Location, SearchFilters } from '../types/CoreServices';
import { poiService } from '../services/POIService';

// ============================================================================
// 状态接口定义
// ============================================================================

export interface ExploreState {
  // 搜索状态
  search: {
    query: string;
    filters: SearchFilters;
    location: Location | null;
    isSearching: boolean;
    error: string | null;
  };

  // POI 数据
  pois: {
    search: {
      data: POI[];
      isLoading: boolean;
      error: string | null;
    };
    nearby: {
      data: POI[];
      isLoading: boolean;
      error: string | null;
    };
    favorites: {
      data: POI[];
      isLoading: boolean;
      error: string | null;
    };
  };

  // 地图状态
  map: {
    center: Location | null;
    zoom: number;
    markers: POI[];
    selectedPOI: POI | null;
    isMapReady: boolean;
  };

  // 操作方法
  actions: {
    // 搜索相关
    searchPOIs: (query?: string) => Promise<void>;
    setSearchQuery: (query: string) => void;
    setSearchFilters: (filters: Partial<SearchFilters>) => void;
    setSearchLocation: (location: Location | null) => void;
    clearSearch: () => void;

    // POI 相关
    fetchNearbyPOIs: (location: Location, radius?: number) => Promise<void>;
    addToFavorites: (poi: POI) => Promise<void>;
    removeFromFavorites: (poiId: string) => Promise<void>;
    fetchFavorites: () => Promise<void>;

    // 地图相关
    setMapCenter: (location: Location) => void;
    setSelectedPOI: (poi: POI | null) => void;
    setMapReady: (ready: boolean) => void;
  };
}

// ============================================================================
// 初始状态
// ============================================================================

const initialState: Omit<ExploreState, 'actions'> = {
  search: {
    query: '',
    filters: {
      type: [],
      priceLevel: [],
      rating: 0,
      distance: 10,
      openNow: false,
    },
    location: null,
    isSearching: false,
    error: null,
  },

  pois: {
    search: {
      data: [],
      isLoading: false,
      error: null,
    },
    nearby: {
      data: [],
      isLoading: false,
      error: null,
    },
    favorites: {
      data: [],
      isLoading: false,
      error: null,
    },
  },

  map: {
    center: null,
    zoom: 13,
    markers: [],
    selectedPOI: null,
    isMapReady: false,
  },
};

// ============================================================================
// Store 实现
// ============================================================================

export const useExploreStore = create<ExploreState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    actions: {
      // 搜索 POI
      searchPOIs: async (query?: string) => {
        const state = get();
        const searchQuery = query ?? state.search.query;

        if (!searchQuery.trim()) {
          return;
        }

        set(state => ({
          search: { ...state.search, isSearching: true, error: null },
          pois: {
            ...state.pois,
            search: { ...state.pois.search, isLoading: true, error: null },
          },
        }));

        try {
          const response = await poiService.searchPOIs({
            query: searchQuery,
            location: state.search.location || undefined,
            radius: (state.search.filters.distance || 10) * 1000, // 转换为米
            filters: state.search.filters,
            limit: 20,
          });

          if (response.success && response.data) {
            set(state => ({
              search: { ...state.search, isSearching: false },
              pois: {
                ...state.pois,
                search: {
                  data: response.data || [],
                  isLoading: false,
                  error: null,
                },
              },
              map: {
                ...state.map,
                markers: response.data || [],
              },
            }));
          } else {
            throw new Error(response.error || '搜索失败');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '搜索失败';
          set(state => ({
            search: { ...state.search, isSearching: false, error: errorMessage },
            pois: {
              ...state.pois,
              search: { ...state.pois.search, isLoading: false, error: errorMessage },
            },
          }));
        }
      },

      // 设置搜索查询
      setSearchQuery: (query: string) => {
        set(state => ({
          search: { ...state.search, query },
        }));
      },

      // 设置搜索过滤器
      setSearchFilters: (filters: Partial<SearchFilters>) => {
        set(state => ({
          search: {
            ...state.search,
            filters: { ...state.search.filters, ...filters },
          },
        }));
      },

      // 设置搜索位置
      setSearchLocation: (location: Location | null) => {
        set(state => ({
          search: { ...state.search, location },
        }));
      },

      // 清除搜索
      clearSearch: () => {
        set(state => ({
          search: {
            ...state.search,
            query: '',
            isSearching: false,
            error: null,
          },
          pois: {
            ...state.pois,
            search: { data: [], isLoading: false, error: null },
          },
        }));
      },

      // 获取附近 POI
      fetchNearbyPOIs: async (location: Location, radius = 10000) => {
        // 确保 nearby 对象存在
        set(state => ({
          pois: {
            ...state.pois,
            nearby: {
              ...state.pois.nearby,
              isLoading: true,
              error: null,
            },
          },
        }));

        try {
          const response = await poiService.getNearbyPOIs({
            location,
            radius,
            limit: 20,
          });

          if (response.success && response.data) {
            set(state => ({
              pois: {
                ...state.pois,
                nearby: {
                  data: response.data || [],
                  isLoading: false,
                  error: null,
                },
              },
            }));
          } else {
            throw new Error(response.error || '获取附近地点失败');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取附近地点失败';
          set(state => ({
            pois: {
              ...state.pois,
              nearby: {
                ...state.pois.nearby,
                isLoading: false,
                error: errorMessage,
              },
            },
          }));
        }
      },

      // 添加到收藏
      addToFavorites: async (poi: POI) => {
        try {
          // 这里需要用户ID，暂时使用模拟ID
          const userId = 'mock-user-id';
          const response = await poiService.addToFavorites(poi.id, userId);

          if (response.success) {
            set(state => ({
              pois: {
                ...state.pois,
                favorites: {
                  ...state.pois.favorites,
                  data: [...state.pois.favorites.data, { ...poi, isFavorite: true }],
                },
              },
            }));
          } else {
            throw new Error(response.error || '添加收藏失败');
          }
        } catch (error) {
          console.error('添加收藏失败:', error);
        }
      },

      // 从收藏中移除
      removeFromFavorites: async (poiId: string) => {
        try {
          // 这里需要用户ID，暂时使用模拟ID
          const userId = 'mock-user-id';
          const response = await poiService.removeFromFavorites(poiId, userId);

          if (response.success) {
            set(state => ({
              pois: {
                ...state.pois,
                favorites: {
                  ...state.pois.favorites,
                  data: state.pois.favorites.data.filter(poi => poi.id !== poiId),
                },
              },
            }));
          } else {
            throw new Error(response.error || '移除收藏失败');
          }
        } catch (error) {
          console.error('移除收藏失败:', error);
        }
      },

      // 获取收藏列表
      fetchFavorites: async () => {
        set(state => ({
          pois: {
            ...state.pois,
            favorites: { ...state.pois.favorites, isLoading: true, error: null },
          },
        }));

        try {
          // 这里需要用户ID，暂时使用模拟ID
          const userId = 'mock-user-id';
          const response = await poiService.getUserFavorites(userId);

          if (response.success && response.data) {
            set(state => ({
              pois: {
                ...state.pois,
                favorites: {
                  data: response.data || [],
                  isLoading: false,
                  error: null,
                },
              },
            }));
          } else {
            throw new Error(response.error || '获取收藏失败');
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取收藏失败';
          set(state => ({
            pois: {
              ...state.pois,
              favorites: {
                ...state.pois.favorites,
                isLoading: false,
                error: errorMessage,
              },
            },
          }));
        }
      },

      // 设置地图中心
      setMapCenter: (location: Location) => {
        set(state => ({
          map: { ...state.map, center: location },
        }));
      },

      // 设置选中的 POI
      setSelectedPOI: (poi: POI | null) => {
        set(state => ({
          map: { ...state.map, selectedPOI: poi },
        }));
      },

      // 设置地图就绪状态
      setMapReady: (ready: boolean) => {
        set(state => ({
          map: { ...state.map, isMapReady: ready },
        }));
      },
    },
  }))
);

// ============================================================================
// 选择器
// ============================================================================

export const exploreSelectors = {
  getSearchState: (state: ExploreState) => state.search,
  getPOIs: (state: ExploreState) => state.pois,
  getMapState: (state: ExploreState) => state.map,
  getSearchResults: (state: ExploreState) => state.pois.search.data,
  getNearbyPOIs: (state: ExploreState) => state.pois.nearby.data,
  getFavorites: (state: ExploreState) => state.pois.favorites.data,
  isLoading: (state: ExploreState) => 
    state.search.isSearching || 
    state.pois.search.isLoading || 
    state.pois.nearby.isLoading || 
    state.pois.favorites.isLoading,
};
