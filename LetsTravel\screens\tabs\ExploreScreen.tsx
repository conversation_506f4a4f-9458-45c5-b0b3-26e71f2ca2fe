/**
 * Trekmate 4.0 - 探索页面
 * 发现新地点、搜索POI和社区动态的主页面
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { EXPLORE, COMMON, COMMUNITY } from '../../constants/Strings';
import type { MainTabScreenProps } from '../../types/Navigation';
import { usePOISearch, useNearbyPOIs, useFavoritePOIs } from '../../hooks/useStore';
import SearchBar from '../../components/search/SearchBar';
import CommunityFeed from '../../components/community/CommunityFeed';
import type { POI, CommunityPost } from '../../types/CoreServices';

interface ExploreScreenProps extends MainTabScreenProps<'Explore'> {}

export default function ExploreScreen({ navigation }: ExploreScreenProps) {
  const insets = useSafeAreaInsets();

  const { query, results, isSearching, setSearchQuery, clearSearch } = usePOISearch();
  const { pois: nearbyPOIs, fetchNearbyPOIs } = useNearbyPOIs();
  const { favorites } = useFavoritePOIs();

  const [activeTab, setActiveTab] = useState<'nearby' | 'search' | 'favorites' | 'community'>('nearby');

  useEffect(() => {
    // 加载附近POI
    loadNearbyPOIs();
  }, []);

  const loadNearbyPOIs = async () => {
    try {
      // 这里应该获取用户当前位置
      const userLocation = {
        latitude: 3.1390,
        longitude: 101.6869,
      };
      await fetchNearbyPOIs(userLocation, 10000); // 10km范围
    } catch (error) {
      console.error('加载附近POI失败:', error);
    }
  };

  const handleSearchPress = () => {
    navigation.navigate('Search', { initialQuery: query });
  };

  const handlePOIPress = (poi: POI) => {
    navigation.navigate('POIDetail', { poiId: poi.id });
  };

  const renderPOICard = (poi: POI) => (
    <TouchableOpacity
      key={poi.id}
      style={styles.poiCard}
      onPress={() => handlePOIPress(poi)}
      activeOpacity={0.7}
    >
      <View style={styles.poiHeader}>
        <Text style={styles.poiName} numberOfLines={1}>
          {poi.name}
        </Text>
        <View style={styles.poiRating}>
          <Ionicons name="star" size={14} color="#ffd700" />
          <Text style={styles.ratingText}>{poi.rating?.toFixed(1) || 'N/A'}</Text>
        </View>
      </View>

      <Text style={styles.poiAddress} numberOfLines={1}>
        {poi.address}
      </Text>

      <View style={styles.poiFooter}>
        <View style={styles.poiCategory}>
          <Text style={styles.categoryText}>{poi.category}</Text>
        </View>
        <Text style={styles.poiDistance}>
          {poi.distance ? `${(poi.distance / 1000).toFixed(1)}km` : ''}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#667eea" />
      
      {/* 头部区域 */}
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        style={[styles.header, { paddingTop: insets.top + 16 }]}
      >
        <View style={styles.headerContent}>
          <Text style={styles.headerTitle}>{EXPLORE.TITLE}</Text>
          <TouchableOpacity style={styles.filterButton}>
            <Ionicons name="options" size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* 搜索栏 */}
        <View style={styles.searchContainer}>
          <SearchBar
            value={query}
            onChangeText={setSearchQuery}
            onFocus={handleSearchPress}
            placeholder="搜索地点、美食、景点..."
            editable={false}
          />
        </View>
      </LinearGradient>

      {/* 内容区域 */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 快速分类 */}
        <View style={styles.categories}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity style={[styles.categoryCard, styles.categoryActive]}>
              <Ionicons name="location" size={24} color="#ffffff" />
              <Text style={[styles.categoryText, styles.categoryActiveText]}>
                {EXPLORE.NEARBY}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.categoryCard}>
              <Ionicons name="star" size={24} color="#667eea" />
              <Text style={styles.categoryText}>{EXPLORE.POPULAR}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.categoryCard}>
              <Ionicons name="heart" size={24} color="#667eea" />
              <Text style={styles.categoryText}>{EXPLORE.RECOMMENDED}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.categoryCard, activeTab === 'community' && styles.categoryActive]}
              onPress={() => setActiveTab('community')}
            >
              <Ionicons
                name="people"
                size={24}
                color={activeTab === 'community' ? "#ffffff" : "#667eea"}
              />
              <Text style={[
                styles.categoryText,
                activeTab === 'community' && styles.categoryActiveText
              ]}>
                {EXPLORE.COMMUNITY}
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* POI类型网格 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>探索分类</Text>
          
          <View style={styles.poiGrid}>
            <TouchableOpacity style={styles.poiCard}>
              <View style={[styles.poiIcon, { backgroundColor: '#ff6b6b' }]}>
                <Ionicons name="camera" size={24} color="#ffffff" />
              </View>
              <Text style={styles.poiTitle}>{EXPLORE.ATTRACTIONS}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.poiCard}>
              <View style={[styles.poiIcon, { backgroundColor: '#4ecdc4' }]}>
                <Ionicons name="bed" size={24} color="#ffffff" />
              </View>
              <Text style={styles.poiTitle}>{EXPLORE.HOTELS}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.poiCard}>
              <View style={[styles.poiIcon, { backgroundColor: '#45b7d1' }]}>
                <Ionicons name="restaurant" size={24} color="#ffffff" />
              </View>
              <Text style={styles.poiTitle}>{EXPLORE.RESTAURANTS}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.poiCard}>
              <View style={[styles.poiIcon, { backgroundColor: '#96ceb4' }]}>
                <Ionicons name="bag" size={24} color="#ffffff" />
              </View>
              <Text style={styles.poiTitle}>{EXPLORE.SHOPPING}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.poiCard}>
              <View style={[styles.poiIcon, { backgroundColor: '#ffeaa7' }]}>
                <Ionicons name="musical-notes" size={24} color="#ffffff" />
              </View>
              <Text style={styles.poiTitle}>{EXPLORE.ENTERTAINMENT}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.poiCard}>
              <View style={[styles.poiIcon, { backgroundColor: '#dda0dd' }]}>
                <Ionicons name="ellipsis-horizontal" size={24} color="#ffffff" />
              </View>
              <Text style={styles.poiTitle}>更多</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 根据activeTab渲染不同内容 */}
        {activeTab === 'community' ? (
          <CommunityFeed
            filter={{ type: 'all' }}
            onPostPress={(post) => navigation.navigate('Community', {
              screen: 'PostDetail',
              params: { postId: post.id }
            })}
            onUserPress={(userId) => navigation.navigate('Community', {
              screen: 'UserProfile',
              params: { userId }
            })}
            onLocationPress={(location) => {
              if (location) {
                navigation.navigate('Map', {
                  initialLocation: {
                    latitude: location.latitude,
                    longitude: location.longitude,
                  },
                });
              }
            }}
            onJourneyPress={(journeyId) => navigation.navigate('Journey', {
              screen: 'JourneyDetail',
              params: { journeyId }
            })}
            onCreatePost={() => navigation.navigate('Community', {
              screen: 'CreatePost'
            })}
            style={{ flex: 1 }}
          />
        ) : (
          <>
            {/* 推荐地点 */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>推荐地点</Text>
                <TouchableOpacity>
                  <Text style={styles.seeAllText}>查看全部</Text>
                </TouchableOpacity>
              </View>

              {/* 占位符 - 推荐地点卡片 */}
              <View style={styles.recommendationCard}>
                <View style={styles.placeholderImage}>
                  <Ionicons name="image" size={32} color="#c7c7cc" />
                </View>
                <View style={styles.recommendationContent}>
                  <Text style={styles.recommendationTitle}>吉隆坡双子塔</Text>
                  <Text style={styles.recommendationLocation}>吉隆坡, 马来西亚</Text>
                  <View style={styles.recommendationMeta}>
                    <View style={styles.rating}>
                      <Ionicons name="star" size={14} color="#ffd700" />
                      <Text style={styles.ratingText}>4.5</Text>
                    </View>
                    <Text style={styles.distance}>2.3km</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* 社区动态预览 */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>社区动态</Text>
                <TouchableOpacity onPress={() => setActiveTab('community')}>
                  <Text style={styles.seeAllText}>查看全部</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.communityCard}>
                <View style={styles.communityHeader}>
                  <View style={styles.avatar}>
                    <Ionicons name="person" size={20} color="#667eea" />
                  </View>
                  <View style={styles.communityMeta}>
                    <Text style={styles.username}>旅行达人</Text>
                    <Text style={styles.postTime}>2小时前</Text>
                  </View>
                </View>
                <Text style={styles.postContent}>
                  刚刚从吉隆坡双子塔回来，夜景真的太美了！强烈推荐大家晚上去 ✨
                </Text>
              </View>
            </View>
          </>
        )}

        {/* 底部间距 */}
        <View style={{ height: 100 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    marginTop: 8,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1c1c1e',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  categories: {
    marginTop: -30,
    marginBottom: 24,
  },
  categoryCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryActive: {
    backgroundColor: '#667eea',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#667eea',
  },
  categoryActiveText: {
    color: '#ffffff',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1c1c1e',
  },
  seeAllText: {
    fontSize: 14,
    color: '#667eea',
    fontWeight: '500',
  },
  poiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  poiCard: {
    width: '30%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  poiIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  poiTitle: {
    fontSize: 12,
    fontWeight: '500',
    color: '#1c1c1e',
    textAlign: 'center',
  },
  recommendationCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  placeholderImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  recommendationContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  recommendationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1c1c1e',
    marginBottom: 4,
  },
  recommendationLocation: {
    fontSize: 14,
    color: '#8e8e93',
    marginBottom: 8,
  },
  recommendationMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1c1c1e',
  },
  distance: {
    fontSize: 12,
    color: '#8e8e93',
  },
  communityCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  communityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  communityMeta: {
    flex: 1,
  },
  username: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1c1c1e',
  },
  postTime: {
    fontSize: 12,
    color: '#8e8e93',
  },
  postContent: {
    fontSize: 14,
    color: '#1c1c1e',
    lineHeight: 20,
  },
});
