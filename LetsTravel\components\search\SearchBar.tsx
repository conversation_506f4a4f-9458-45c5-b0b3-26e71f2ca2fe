/**
 * Trekmate 4.0 - 智能搜索栏组件
 * 提供实时搜索建议和智能输入体验
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Keyboard,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { EXPLORE } from '../../constants/Strings';

interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChangeText: (text: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onSubmit?: (text: string) => void;
  onClear?: () => void;
  showCancelButton?: boolean;
  onCancel?: () => void;
  autoFocus?: boolean;
  editable?: boolean;
  loading?: boolean;
}

export default function SearchBar({
  placeholder = EXPLORE.SEARCH_PLACEHOLDER,
  value,
  onChangeText,
  onFocus,
  onBlur,
  onSubmit,
  onClear,
  showCancelButton = false,
  onCancel,
  autoFocus = false,
  editable = true,
  loading = false,
}: SearchBarProps) {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const cancelButtonWidth = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // 动画处理焦点状态
    Animated.timing(animatedValue, {
      toValue: isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();

    // 取消按钮动画
    Animated.timing(cancelButtonWidth, {
      toValue: (isFocused && showCancelButton) ? 80 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, showCancelButton]);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleSubmit = () => {
    if (value.trim()) {
      onSubmit?.(value.trim());
      Keyboard.dismiss();
    }
  };

  const handleClear = () => {
    onChangeText('');
    onClear?.();
    inputRef.current?.focus();
  };

  const handleCancel = () => {
    onChangeText('');
    onCancel?.();
    inputRef.current?.blur();
    Keyboard.dismiss();
  };

  // 动画样式 - 使用静态样式避免属性添加警告
  const getBorderColor = () => {
    return isFocused ? colors.primary[500] : colors.border;
  };

  const getShadowOpacity = () => {
    return isFocused ? 0.2 : 0.1;
  };

  const getSearchIconColor = () => {
    return isFocused ? colors.primary[500] : colors.textSecondary;
  };

  return (
    <View style={styles.container}>
      <View style={[
        styles.searchContainer,
        {
          borderColor: getBorderColor(),
          shadowOpacity: getShadowOpacity(),
        }
      ]}>
        {/* 搜索图标 */}
        <View style={styles.searchIcon}>
          <Ionicons 
            name="search" 
            size={20} 
            color={getSearchIconColor()}
          />
        </View>

        {/* 输入框 */}
        <TextInput
          ref={inputRef}
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          value={value}
          onChangeText={onChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onSubmitEditing={handleSubmit}
          returnKeyType="search"
          autoFocus={autoFocus}
          editable={editable}
          clearButtonMode="never" // 我们使用自定义清除按钮
          autoCorrect={false}
          autoCapitalize="none"
          blurOnSubmit={false}
        />

        {/* 加载指示器或清除按钮 */}
        <View style={styles.rightActions}>
          {loading ? (
            <View style={styles.loadingContainer}>
              <Ionicons name="refresh" size={16} color={colors.textSecondary} />
            </View>
          ) : value.length > 0 ? (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClear}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>

      {/* 取消按钮 */}
      {showCancelButton && (
        <Animated.View style={[styles.cancelButtonContainer, { width: cancelButtonWidth }]}>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={handleCancel}
            hitSlop={{ top: 10, bottom: 10, left: 5, right: 5 }}
          >
            <Animated.Text style={styles.cancelButtonText}>
              取消
            </Animated.Text>
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing[3],
    paddingVertical: Platform.OS === 'ios' ? spacing[3] : spacing[2],
    ...shadows.sm,
  },
  searchIcon: {
    marginRight: spacing[2],
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    paddingVertical: 0, // 移除默认内边距
  },
  rightActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: spacing[2],
  },
  loadingContainer: {
    padding: spacing[1],
  },
  clearButton: {
    padding: spacing[1],
  },
  cancelButtonContainer: {
    overflow: 'hidden',
    marginLeft: spacing[2],
  },
  cancelButton: {
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    color: colors.primary[500],
    fontSize: 16,
    fontWeight: '500',
  },
});

// 紧凑搜索栏变体
export const CompactSearchBar = (props: Omit<SearchBarProps, 'showCancelButton'>) => (
  <SearchBar {...props} showCancelButton={false} />
);

// 全屏搜索栏变体
export const FullScreenSearchBar = (props: SearchBarProps) => (
  <SearchBar {...props} showCancelButton={true} />
);
