[{"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\EventEmitter.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\JSIUtils.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\LazyObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\NativeModule.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\ObjectDeallocator.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\SharedObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\SharedRef.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\TypedArray.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\Exceptions.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\Exceptions.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\Exceptions.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\ExpoModulesHostObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\ExpoModulesHostObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\ExpoModulesHostObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIDeallocator.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIDeallocator.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIDeallocator.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIFunctionBody.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIFunctionBody.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIFunctionBody.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIInjector.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIInjector.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIInjector.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JNIUtils.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIUtils.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JNIUtils.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSIContext.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSIContext.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSIContext.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSReferencesCache.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSReferencesCache.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSReferencesCache.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JSharedObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSharedObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JSharedObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaCallback.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaCallback.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaCallback.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaReferencesCache.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaReferencesCache.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaReferencesCache.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptFunction.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptFunction.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptFunction.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptModuleObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptModuleObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptModuleObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptRuntime.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptRuntime.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptRuntime.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptTypedArray.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptTypedArray.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptTypedArray.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptValue.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptValue.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptValue.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\JavaScriptWeakObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptWeakObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\JavaScriptWeakObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\MethodMetadata.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\MethodMetadata.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\MethodMetadata.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\RuntimeHolder.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\RuntimeHolder.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\RuntimeHolder.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\WeakRuntimeHolder.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\WeakRuntimeHolder.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\WeakRuntimeHolder.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\AnyType.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\AnyType.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\AnyType.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\ExpectedType.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\ExpectedType.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\ExpectedType.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\FrontendConverter.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverter.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverter.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\FrontendConverterProvider.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\types\\JNIToJSIConverter.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSClassesDecorator.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSConstantsDecorator.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSDecoratorsBridgingObject.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSFunctionsDecorator.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSObjectDecorator.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dexpo_modules_core_EXPORTS -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/src/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o CMakeFiles\\expo-modules-core.dir\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\main\\cpp\\decorators\\JSPropertiesDecorator.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\a99b86dc36e202fccd96a7c556adf94f\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewComponentDescriptor.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\b2f2c17edf305786d5d85748423c3cf1\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewEventEmitter.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\C_\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewProps.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\b2f2c17edf305786d5d85748423c3cf1\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\common\\cpp\\fabric\\ExpoViewShadowNode.cpp"}, {"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/.cxx/Debug/2v2d5r5q/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/expo-modules-core/android/../common/cpp/fabric -IC:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include/react/fabric -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/1957b0db02baa50e26af4d5b92e700f6/transformed/fbjni-0.6.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_TARGET_VERSION=76 -fno-limit-debug-info  -fPIC -std=c++20 -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -O2 -frtti -fexceptions -Wall -fstack-protector-all -DUSE_HERMES=0 -DUNIT_TEST=0 -DIS_NEW_ARCHITECTURE_ENABLED=1 -DRN_FABRIC_ENABLED=1 -std=gnu++20 -o src\\fabric\\CMakeFiles\\fabric.dir\\FabricComponentsRegistry.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\fabric\\FabricComponentsRegistry.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\expo-modules-core\\android\\src\\fabric\\FabricComponentsRegistry.cpp"}]