[{"directory": "C:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native-gesture-handler/android/.cxx/Debug/27601h1a/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dgesturehandler_EXPORTS -IC:/AppTest/letstravel3.131/LetsTravel/node_modules/react-native/ReactCommon -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/0f839437195569227022e709030e837c/transformed/react-android-0.76.9-debug/prefab/modules/jsi/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\gesturehandler.dir\\cpp-adapter.cpp.o -c C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\cpp-adapter.cpp", "file": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\cpp-adapter.cpp"}]