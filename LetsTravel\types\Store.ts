/**
 * Trekmate 4.0 - 根状态存储类型定义
 * 整合所有状态存储的类型
 */

import type { UserState } from './userStore';
import type { POI, Location } from './CoreServices';

// ============================================================================
// 通用状态类型
// ============================================================================

export interface AsyncState<T> {
  data: T;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

export interface PaginatedState<T> {
  data: T[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

// ============================================================================
// 行程状态类型
// ============================================================================

export interface JourneyState {
  journeys: any[];
  currentJourney: any | null;
  isLoading: boolean;
  error: string | null;
  actions: any;
}

// ============================================================================
// 探索状态类型
// ============================================================================

export interface SearchFilters {
  type: string[];
  priceLevel: number[];
  rating: number;
  distance: number;
  openNow: boolean;
}

export interface SearchState {
  query: string;
  filters: SearchFilters;
  location: Location | null;
  isSearching: boolean;
  error: string | null;
}

export interface MapState {
  center: Location | null;
  zoom: number;
  markers: POI[];
  selectedPOI: POI | null;
  isMapReady: boolean;
}

export interface POIStates {
  nearby: PaginatedState<POI>;
  search: PaginatedState<POI>;
  favorites: AsyncState<POI[]>;
  recent: AsyncState<POI[]>;
}

export interface ExploreActions {
  searchPOIs: (query: string, location: Location, filters?: any) => Promise<void>;
  fetchNearbyPOIs: (location: Location, radius: number) => Promise<void>;
  fetchPOI: (id: string) => Promise<POI>;
  addToFavorites: (poi: POI) => Promise<void>;
  removeFromFavorites: (poiId: string) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setSearchFilters: (filters: Partial<SearchFilters>) => void;
  setSearchLocation: (location: Location | null) => void;
  clearSearch: () => void;
  setMapCenter: (location: Location) => void;
  setSelectedPOI: (poi: POI | null) => void;
}

export interface ExploreState {
  pois: POIStates;
  search: SearchState;
  map: MapState;
  actions: ExploreActions;
}

// ============================================================================
// 设置状态类型
// ============================================================================

export interface AppSettingsState {
  theme: string;
  language: string;
  notifications: boolean;
  actions: any;
}

// ============================================================================
// UI状态类型
// ============================================================================

/**
 * 定义整个应用的UI状态结构
 */
export interface UIState {
  app: {
    isModified: boolean;
  };
  navigation: {
    currentRoute: string;
    previousRoute: string | null;
    canGoBack: boolean;
  };
  modals: {
    isLoginModalOpen: boolean;
    isCreateJourneyModalOpen: boolean;
    isCreatePostModalOpen: boolean;
    isSettingsModalOpen: boolean;
  };
  loading: {
    global: boolean;
    overlay: boolean;
    message: string | null;
  };
  errors: {
    global: string | null;
    network: boolean;
    lastError: {
      message: string;
      timestamp: string;
    } | null;
  };
  success: {
    message: string | null;
    timestamp: string | null;
  };
  keyboard: {
    isVisible: boolean;
    height: number;
  };
  network: {
    isConnected: boolean;
    type: string | null;
  };
  actions: UIActions;
}

export interface UIActions {
  setModified: (isModified: boolean) => void;
  setCurrentRoute: (route: string) => void;
  openModal: (modalName: keyof Omit<UIState['modals'], 'actions'>) => void;
  closeModal: (modalName: keyof Omit<UIState['modals'], 'actions'>) => void;
  closeAllModals: () => void;
  setGlobalLoading: (loading: boolean, message?: string) => void;
  setOverlayLoading: (loading: boolean) => void;
  setGlobalError: (error: string | null) => void;
  setNetworkError: (hasError: boolean) => void;
  setSuccessMessage: (message: string) => void;
  clearMessages: () => void;
  setKeyboardState: (isVisible: boolean, height: number) => void;
  setNetworkState: (isConnected: boolean, type: string | null) => void;
}


// ============================================================================
// 根状态类型
// ============================================================================

export interface RootState {
  user: UserState;
  journey: JourneyState;
  explore: ExploreState;
  settings: AppSettingsState;
  ui: UIState;
}