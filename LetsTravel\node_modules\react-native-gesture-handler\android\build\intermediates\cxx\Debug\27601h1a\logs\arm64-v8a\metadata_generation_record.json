[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\27601h1a\\arm64-v8a\\android_gradle_build.json due to:", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Microsoft\\\\jdk-17.0.14.7-hotspot\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  26 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging79695242676822760\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.10.2\\\\transforms\\\\0f839437195569227022e709030e837c\\\\transformed\\\\react-android-0.76.9-debug\\\\prefab\" ^\n  \"C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-reanimated\\\\3n573b1t\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.10.2\\\\transforms\\\\1957b0db02baa50e26af4d5b92e700f6\\\\transformed\\\\fbjni-0.6.0\\\\prefab\"\n", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\27601h1a\\arm64-v8a'", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\27601h1a\\arm64-v8a'", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\src\\\\main\\\\jni\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\27601h1a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\27601h1a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\27601h1a\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\27601h1a\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DREACT_NATIVE_DIR=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=76\" ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\src\\\\main\\\\jni\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\27601h1a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\27601h1a\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\27601h1a\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BC:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\27601h1a\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DREACT_NATIVE_DIR=C:\\\\AppTest\\\\letstravel3.131\\\\LetsTravel\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=76\" ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\27601h1a\\arm64-v8a\\compile_commands.json.bin normally", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\27601h1a\\arm64-v8a\\compile_commands.json to C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\.cxx\\tools\\debug\\arm64-v8a\\compile_commands.json", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\AppTest\\letstravel3.131\\LetsTravel\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]