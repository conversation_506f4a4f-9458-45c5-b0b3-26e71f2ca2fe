/**
 * Trekmate 4.0 - 天气服务
 * 提供天气数据查询和预报功能
 */

import { defaultStorage } from '../storage/StorageService';
import type { ServiceResponse, Location } from '../../types/CoreServices';

// ============================================================================
// 天气服务数据类型
// ============================================================================

export interface WeatherData {
  location: Location;
  temperature: number; // 摄氏度
  feelsLike: number;
  condition: 'sunny' | 'cloudy' | 'partly-cloudy' | 'rainy' | 'stormy' | 'snowy' | 'foggy' | 'windy';
  description: string;
  humidity: number; // 百分比
  pressure: number; // hPa
  visibility: number; // 公里
  windSpeed: number; // km/h
  windDirection: string;
  uvIndex: number;
  timestamp: string;
}

export interface WeatherForecast {
  date: string;
  highTemp: number;
  lowTemp: number;
  condition: WeatherData['condition'];
  description: string;
  precipitationChance: number; // 降水概率
  windSpeed: number;
  humidity: number;
}

export interface WeatherAlert {
  id: string;
  type: 'warning' | 'watch' | 'advisory';
  title: string;
  description: string;
  severity: 'minor' | 'moderate' | 'severe' | 'extreme';
  startTime: string;
  endTime: string;
  areas: string[];
}

// ============================================================================
// 天气服务接口
// ============================================================================

export interface WeatherService {
  // 当前天气
  getCurrentWeather(location: Location): Promise<ServiceResponse<WeatherData>>;
  
  // 天气预报
  getWeatherForecast(location: Location, days: number): Promise<ServiceResponse<WeatherForecast[]>>;
  getHourlyForecast(location: Location, hours: number): Promise<ServiceResponse<WeatherData[]>>;
  
  // 天气警报
  getWeatherAlerts(location: Location): Promise<ServiceResponse<WeatherAlert[]>>;
  
  // 历史天气
  getHistoricalWeather(location: Location, date: string): Promise<ServiceResponse<WeatherData>>;
  
  // 缓存管理
  refreshWeatherData(location: Location): Promise<ServiceResponse<void>>;
  getLastUpdateTime(location: Location): Promise<ServiceResponse<Date>>;
}

// ============================================================================
// 天气服务实现
// ============================================================================

class WeatherServiceImpl implements WeatherService {
  private readonly CACHE_PREFIX = 'weather_cache_';
  private readonly CACHE_EXPIRY = 30 * 60 * 1000; // 30分钟

  // Mock 天气条件
  private readonly weatherConditions = [
    'sunny', 'cloudy', 'partly-cloudy', 'rainy', 'stormy', 'snowy', 'foggy', 'windy'
  ] as const;

  private readonly conditionDescriptions = {
    sunny: '晴朗',
    cloudy: '多云',
    'partly-cloudy': '局部多云',
    rainy: '雨天',
    stormy: '雷雨',
    snowy: '雪天',
    foggy: '雾天',
    windy: '大风',
  };

  // ========================================================================
  // 当前天气
  // ========================================================================

  async getCurrentWeather(location: Location): Promise<ServiceResponse<WeatherData>> {
    try {
      // 尝试从缓存加载
      const cacheKey = this.getLocationCacheKey(location, 'current');
      const cached = await this.loadFromCache(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
        };
      }

      // 模拟API调用延迟
      await this.simulateNetworkDelay();

      // 生成模拟天气数据
      const weatherData = this.generateMockWeatherData(location);

      // 保存到缓存
      await this.saveToCache(cacheKey, weatherData);

      return {
        success: true,
        data: weatherData,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取天气数据失败',
      };
    }
  }

  // ========================================================================
  // 天气预报
  // ========================================================================

  async getWeatherForecast(location: Location, days: number): Promise<ServiceResponse<WeatherForecast[]>> {
    try {
      const cacheKey = this.getLocationCacheKey(location, `forecast_${days}`);
      const cached = await this.loadFromCache(cacheKey);
      if (cached) {
        return {
          success: true,
          data: cached,
        };
      }

      await this.simulateNetworkDelay();

      const forecast: WeatherForecast[] = [];
      const baseTemp = this.getBaseTemperature(location);

      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() + i);

        // 生成温度变化
        const tempVariation = (Math.random() - 0.5) * 10; // ±5度变化
        const highTemp = baseTemp + tempVariation + Math.random() * 5;
        const lowTemp = highTemp - (5 + Math.random() * 10);

        // 随机选择天气条件
        const condition = this.weatherConditions[Math.floor(Math.random() * this.weatherConditions.length)];

        forecast.push({
          date: date.toISOString().split('T')[0],
          highTemp,
          lowTemp,
          condition,
          description: this.conditionDescriptions[condition],
          precipitationChance: this.getPrecipitationChance(condition),
          windSpeed: 5 + Math.random() * 20,
          humidity: 40 + Math.random() * 40,
        });
      }

      await this.saveToCache(cacheKey, forecast);

      return {
        success: true,
        data: forecast,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取天气预报失败',
      };
    }
  }

  async getHourlyForecast(location: Location, hours: number): Promise<ServiceResponse<WeatherData[]>> {
    try {
      await this.simulateNetworkDelay();

      const hourlyData: WeatherData[] = [];
      const baseTemp = this.getBaseTemperature(location);

      for (let i = 0; i < hours; i++) {
        const date = new Date();
        date.setHours(date.getHours() + i);

        // 模拟一天中的温度变化
        const hourOfDay = date.getHours();
        const tempModifier = this.getHourlyTempModifier(hourOfDay);
        const temperature = baseTemp + tempModifier + (Math.random() - 0.5) * 3;

        const condition = this.weatherConditions[Math.floor(Math.random() * this.weatherConditions.length)];

        hourlyData.push({
          location,
          temperature,
          feelsLike: temperature + (Math.random() - 0.5) * 4,
          condition,
          description: this.conditionDescriptions[condition],
          humidity: 40 + Math.random() * 40,
          pressure: 1000 + Math.random() * 50,
          visibility: 5 + Math.random() * 15,
          windSpeed: 5 + Math.random() * 20,
          windDirection: this.getRandomWindDirection(),
          uvIndex: this.getUVIndex(hourOfDay, condition),
          timestamp: date.toISOString(),
        });
      }

      return {
        success: true,
        data: hourlyData,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取小时预报失败',
      };
    }
  }

  // ========================================================================
  // 天气警报
  // ========================================================================

  async getWeatherAlerts(location: Location): Promise<ServiceResponse<WeatherAlert[]>> {
    try {
      await this.simulateNetworkDelay();

      // 模拟天气警报（大部分时候没有警报）
      const alerts: WeatherAlert[] = [];
      
      if (Math.random() < 0.2) { // 20%概率有警报
        alerts.push({
          id: `alert_${Date.now()}`,
          type: 'warning',
          title: '雷雨天气预警',
          description: '预计未来6小时内将有雷雨天气，请注意安全。',
          severity: 'moderate',
          startTime: new Date().toISOString(),
          endTime: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(),
          areas: [location.address || '当前区域'],
        });
      }

      return {
        success: true,
        data: alerts,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取天气警报失败',
      };
    }
  }

  // ========================================================================
  // 历史天气
  // ========================================================================

  async getHistoricalWeather(location: Location, date: string): Promise<ServiceResponse<WeatherData>> {
    try {
      await this.simulateNetworkDelay();

      // 生成历史天气数据
      const historicalData = this.generateMockWeatherData(location, new Date(date));

      return {
        success: true,
        data: historicalData,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取历史天气失败',
      };
    }
  }

  // ========================================================================
  // 缓存管理
  // ========================================================================

  async refreshWeatherData(location: Location): Promise<ServiceResponse<void>> {
    try {
      // 清除相关缓存
      await this.clearLocationCache(location);
      
      // 重新获取数据
      await this.getCurrentWeather(location);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '刷新天气数据失败',
      };
    }
  }

  async getLastUpdateTime(location: Location): Promise<ServiceResponse<Date>> {
    try {
      const cacheKey = this.getLocationCacheKey(location, 'current');
      const cached = await this.loadFromCache(cacheKey);
      
      if (cached) {
        return {
          success: true,
          data: new Date(cached.timestamp),
        };
      }

      return {
        success: true,
        data: new Date(),
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取更新时间失败',
      };
    }
  }

  // ========================================================================
  // 辅助方法
  // ========================================================================

  private generateMockWeatherData(location: Location, date?: Date): WeatherData {
    const now = date || new Date();
    const baseTemp = this.getBaseTemperature(location);
    const hourOfDay = now.getHours();
    const tempModifier = this.getHourlyTempModifier(hourOfDay);
    const temperature = baseTemp + tempModifier + (Math.random() - 0.5) * 5;

    const condition = this.weatherConditions[Math.floor(Math.random() * this.weatherConditions.length)];

    return {
      location,
      temperature,
      feelsLike: temperature + (Math.random() - 0.5) * 4,
      condition,
      description: this.conditionDescriptions[condition],
      humidity: 40 + Math.random() * 40,
      pressure: 1000 + Math.random() * 50,
      visibility: 5 + Math.random() * 15,
      windSpeed: 5 + Math.random() * 20,
      windDirection: this.getRandomWindDirection(),
      uvIndex: this.getUVIndex(hourOfDay, condition),
      timestamp: now.toISOString(),
    };
  }

  private getBaseTemperature(location: Location): number {
    // 基于纬度估算基础温度
    const latitude = Math.abs(location.latitude);
    
    if (latitude < 10) return 28; // 热带
    if (latitude < 23.5) return 25; // 亚热带
    if (latitude < 35) return 20; // 温带
    if (latitude < 50) return 15; // 温带
    return 10; // 寒带
  }

  private getHourlyTempModifier(hour: number): number {
    // 模拟一天中的温度变化
    const curve = Math.sin((hour - 6) * Math.PI / 12); // 6点最低，18点最高
    return curve * 8; // ±8度变化
  }

  private getPrecipitationChance(condition: WeatherData['condition']): number {
    const chanceMap = {
      sunny: 0,
      cloudy: 20,
      'partly-cloudy': 10,
      rainy: 80,
      stormy: 90,
      snowy: 70,
      foggy: 30,
      windy: 15,
    };

    return chanceMap[condition] + Math.random() * 20 - 10; // ±10%变化
  }

  private getRandomWindDirection(): string {
    const directions = ['北', '东北', '东', '东南', '南', '西南', '西', '西北'];
    return directions[Math.floor(Math.random() * directions.length)];
  }

  private getUVIndex(hour: number, condition: WeatherData['condition']): number {
    if (hour < 6 || hour > 18) return 0;
    
    let baseUV = Math.sin((hour - 6) * Math.PI / 12) * 10;
    
    // 根据天气条件调整
    const conditionModifier = {
      sunny: 1.0,
      cloudy: 0.3,
      'partly-cloudy': 0.7,
      rainy: 0.2,
      stormy: 0.1,
      snowy: 0.8,
      foggy: 0.2,
      windy: 0.9,
    };

    return Math.max(0, Math.round(baseUV * conditionModifier[condition]));
  }

  private getLocationCacheKey(location: Location, type: string): string {
    const locationKey = `${location.latitude.toFixed(2)}_${location.longitude.toFixed(2)}`;
    return `${locationKey}_${type}`;
  }

  private async simulateNetworkDelay(): Promise<void> {
    const delay = 300 + Math.random() * 700; // 0.3-1秒
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  private async loadFromCache(key: string): Promise<any> {
    try {
      const cached = await defaultStorage.getObject<{
        data: any;
        timestamp: number;
      }>(`${this.CACHE_PREFIX}${key}`);

      if (cached && Date.now() - cached.timestamp < this.CACHE_EXPIRY) {
        return cached.data;
      }

      return null;
    } catch (error) {
      console.warn('加载天气缓存失败:', error);
      return null;
    }
  }

  private async saveToCache(key: string, data: any): Promise<void> {
    try {
      await defaultStorage.setObject(`${this.CACHE_PREFIX}${key}`, {
        data,
        timestamp: Date.now(),
      });
    } catch (error) {
      console.warn('保存天气缓存失败:', error);
    }
  }

  private async clearLocationCache(location: Location): Promise<void> {
    try {
      const locationKey = `${location.latitude.toFixed(2)}_${location.longitude.toFixed(2)}`;
      const keys = ['current', 'forecast_7', 'forecast_14'];
      
      for (const type of keys) {
        await defaultStorage.removeItem(`${this.CACHE_PREFIX}${locationKey}_${type}`);
      }
    } catch (error) {
      console.warn('清除天气缓存失败:', error);
    }
  }
}

// ============================================================================
// 导出服务实例
// ============================================================================

export const weatherService = new WeatherServiceImpl();
