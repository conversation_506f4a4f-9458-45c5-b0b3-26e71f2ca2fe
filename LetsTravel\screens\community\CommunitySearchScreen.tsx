/**
 * Trekmate 4.0 - 社区搜索屏幕
 * 搜索社区帖子、用户和话题的专用界面
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import debounce from 'lodash.debounce';

import { colors, spacing, borderRadius, shadows } from '../../constants/Theme';
import { COMMUNITY, SEARCH } from '../../constants/Strings';
import PostCard from '../../components/community/PostCard';
import { communityService } from '../../services/community/CommunityService';
import type { CommunitySearchScreenProps } from '../../types/Navigation';
import type { CommunityPost } from '../../services/community/CommunityService';

// ============================================================================
// 搜索类型
// ============================================================================

type SearchType = 'posts' | 'users' | 'tags';

interface SearchTab {
  type: SearchType;
  label: string;
  icon: string;
}

const SEARCH_TABS: SearchTab[] = [
  { type: 'posts', label: '帖子', icon: 'document-text-outline' },
  { type: 'users', label: '用户', icon: 'people-outline' },
  { type: 'tags', label: '话题', icon: 'pricetag-outline' },
];

// ============================================================================
// 热门标签
// ============================================================================

const TRENDING_TAGS = [
  '吉隆坡', '美食', '攻略', '摄影', '自由行',
  '马来西亚', '旅行日记', '景点推荐', '住宿', '交通',
];

// ============================================================================
// 社区搜索屏幕组件
// ============================================================================

export default function CommunitySearchScreen({ navigation }: CommunitySearchScreenProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<SearchType>('posts');
  const [searchResults, setSearchResults] = useState<CommunityPost[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // 防抖搜索函数
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (query.length < 2) {
        setSearchResults([]);
        setHasSearched(false);
        return;
      }

      setLoading(true);
      setHasSearched(true);

      try {
        if (activeTab === 'posts') {
          const response = await communityService.searchPosts(query);
          if (response.success && response.data) {
            setSearchResults(response.data);
          }
        } else if (activeTab === 'users') {
          // 用户搜索逻辑
          setSearchResults([]);
        } else if (activeTab === 'tags') {
          // 标签搜索逻辑
          setSearchResults([]);
        }
      } catch (error) {
        console.error('搜索失败:', error);
        setSearchResults([]);
      } finally {
        setLoading(false);
      }
    }, 300),
    [activeTab]
  );

  useEffect(() => {
    debouncedSearch(searchQuery);
  }, [searchQuery, debouncedSearch]);

  // 处理搜索输入
  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  // 处理标签点击
  const handleTagPress = useCallback((tag: string) => {
    setSearchQuery(tag);
    setActiveTab('posts');
  }, []);

  // 处理帖子点击
  const handlePostPress = useCallback((post: CommunityPost) => {
    navigation.navigate('PostDetail', { postId: post.id });
  }, [navigation]);

  // 处理用户点击
  const handleUserPress = useCallback((userId: string) => {
    navigation.navigate('UserProfile', { userId });
  }, [navigation]);

  // 处理位置点击
  const handleLocationPress = useCallback((location: CommunityPost['location']) => {
    if (location) {
      navigation.navigate('Map', {
        initialLocation: {
          latitude: location.latitude,
          longitude: location.longitude,
        },
      });
    }
  }, [navigation]);

  // 处理行程点击
  const handleJourneyPress = useCallback((journeyId: string) => {
    navigation.navigate('JourneyDetail', { journeyId });
  }, [navigation]);

  // 渲染头部
  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Ionicons name="arrow-back" size={24} color={colors.text} />
      </TouchableOpacity>
      
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color={colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          placeholder="搜索帖子、用户或话题..."
          placeholderTextColor={colors.textSecondary}
          value={searchQuery}
          onChangeText={handleSearchChange}
          autoFocus
          returnKeyType="search"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  // 渲染搜索标签页
  const renderSearchTabs = () => (
    <View style={styles.tabsContainer}>
      {SEARCH_TABS.map((tab) => (
        <TouchableOpacity
          key={tab.type}
          style={[
            styles.tab,
            activeTab === tab.type && styles.tabActive,
          ]}
          onPress={() => setActiveTab(tab.type)}
        >
          <Ionicons
            name={tab.icon as any}
            size={18}
            color={activeTab === tab.type ? colors.primary[500] : colors.textSecondary}
          />
          <Text
            style={[
              styles.tabText,
              activeTab === tab.type && styles.tabTextActive,
            ]}
          >
            {tab.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  // 渲染热门标签
  const renderTrendingTags = () => (
    <View style={styles.trendingSection}>
      <Text style={styles.sectionTitle}>热门话题</Text>
      <View style={styles.tagsContainer}>
        {TRENDING_TAGS.map((tag, index) => (
          <TouchableOpacity
            key={index}
            style={styles.trendingTag}
            onPress={() => handleTagPress(tag)}
          >
            <Text style={styles.trendingTagText}>#{tag}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // 渲染搜索结果
  const renderSearchResults = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>搜索中...</Text>
        </View>
      );
    }

    if (!hasSearched) {
      return renderTrendingTags();
    }

    if (searchResults.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="search-outline" size={64} color={colors.textSecondary} />
          <Text style={styles.emptyTitle}>未找到相关内容</Text>
          <Text style={styles.emptyText}>
            尝试使用不同的关键词或浏览热门话题
          </Text>
        </View>
      );
    }

    if (activeTab === 'posts') {
      return (
        <FlatList
          data={searchResults}
          renderItem={({ item }) => (
            <PostCard
              post={item}
              onPress={handlePostPress}
              onUserPress={handleUserPress}
              onLocationPress={handleLocationPress}
              onJourneyPress={handleJourneyPress}
            />
          )}
          keyExtractor={item => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.resultsContainer}
        />
      );
    }

    // 其他标签页的结果渲染
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>该功能将在后续版本中实现</Text>
      </View>
    );
  };

  // 渲染搜索历史
  const renderSearchHistory = () => {
    if (searchQuery.length > 0 || hasSearched) return null;

    return (
      <View style={styles.historySection}>
        <View style={styles.historySectionHeader}>
          <Text style={styles.sectionTitle}>最近搜索</Text>
          <TouchableOpacity>
            <Text style={styles.clearHistoryText}>清除</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.historyContainer}>
          <Text style={styles.emptyText}>暂无搜索历史</Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}
      {renderSearchTabs()}
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSearchHistory()}
        {renderSearchResults()}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    gap: spacing[3],
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.neutral[100],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[2],
    gap: spacing[2],
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing[3],
    gap: spacing[2],
  },
  tabActive: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary[500],
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  tabTextActive: {
    color: colors.primary[500],
  },
  content: {
    flex: 1,
  },
  trendingSection: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
    marginBottom: spacing[3],
  },
  historySection: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
    marginBottom: spacing[3],
  },
  historySectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  clearHistoryText: {
    fontSize: 14,
    color: colors.primary[500],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[2],
  },
  trendingTag: {
    backgroundColor: colors.primary[100],
    borderRadius: borderRadius.lg,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
  },
  trendingTagText: {
    fontSize: 14,
    color: colors.primary[700],
    fontWeight: '500',
  },
  historyContainer: {
    alignItems: 'center',
    paddingVertical: spacing[4],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  loadingText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[6],
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginTop: spacing[4],
    marginBottom: spacing[2],
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  resultsContainer: {
    paddingBottom: spacing[4],
  },
});
